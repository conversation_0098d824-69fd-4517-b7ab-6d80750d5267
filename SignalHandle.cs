﻿using System;
using System.Collections.Generic;

namespace Nirvana
{
	public sealed class SignalHandle
	{
		internal SignalHandle(LinkedList<SignalDelegate> signalList, LinkedListNode<SignalDelegate> signalNode)
		{
			this.signalList = signalList;
			this.signalNode = signalNode;
		}

		public void Dispose()
		{
			bool flag = this.signalList != null && this.signalNode != null;
			if (flag)
			{
				this.signalList.Remove(this.signalNode);
				this.signalNode = null;
				this.signalList = null;
			}
		}

		private LinkedList<SignalDelegate> signalList;

		private LinkedListNode<SignalDelegate> signalNode;
	}
}
