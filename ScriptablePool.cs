﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class ScriptablePool : Singleton<ScriptablePool>
	{
		public ScriptablePool()
		{
			this.sweepChecker = delegate(AssetID assetID, ScriptableCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						float num = cache.LastFreeTime + cache.ReleaseAfterFree;
						bool flag4 = Time.realtimeSinceStartup > num;
						if (flag4)
						{
							ScriptableObject @object = cache.GetObject();
							bool flag5 = @object != null;
							if (flag5)
							{
								this.lookup.Remove(@object);
								AssetManager.UnloadAsseBundle(assetID.BundleName);
							}
							flag2 = true;
						}
						else
						{
							flag2 = false;
						}
					}
				}
				return flag2;
			};
			Scheduler.AddFrameListener(new Action(this.SweepCache));
		}

		public float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				foreach (KeyValuePair<AssetID, ScriptableCache> keyValuePair in this.caches)
				{
					keyValuePair.Value.DefaultReleaseAfterFree = value;
				}
			}
		}

		public void Load(AssetID assetID, Action<ScriptableObject> complete)
		{
			Scheduler.RunCoroutine(this.LoadImpl(assetID, complete));
		}

		public WaitLoadScriptable Load(AssetID assetID)
		{
			ScriptableCache scriptableCache;
			bool flag = this.caches.TryGetValue(assetID, out scriptableCache);
			WaitLoadScriptable waitLoadScriptable;
			if (flag)
			{
				scriptableCache.Retain();
				waitLoadScriptable = new WaitLoadScriptable(scriptableCache);
			}
			else
			{
				scriptableCache = new ScriptableCache(assetID, this.lookup);
				scriptableCache.LoadObject(assetID);
				scriptableCache.Retain();
				this.caches.Add(assetID, scriptableCache);
				waitLoadScriptable = new WaitLoadScriptable(scriptableCache);
			}
			return waitLoadScriptable;
		}

		public void Free(ScriptableObject obj, bool destroy = false)
		{
			bool flag = obj == null;
			if (flag)
			{
				ScriptablePool.logger.LogError("Try to free a null ScriptableObject.");
			}
			else
			{
				ScriptableCache scriptableCache;
				bool flag2 = !this.lookup.TryGetValue(obj, out scriptableCache);
				if (flag2)
				{
					ScriptablePool.logger.LogWarning("Try to free an instance {0} not allocated by this pool.", new object[] { obj.name });
				}
				else
				{
					scriptableCache.Release();
					bool flag3 = destroy && scriptableCache.ReferenceCount == 0;
					if (flag3)
					{
						AssetID assetID = scriptableCache.AssetID;
						ScriptableObject @object = scriptableCache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						this.caches.Remove(assetID);
					}
				}
			}
		}

		public void Clear()
		{
			this.caches.Clear();
			this.lookup.Clear();
		}

		public void ClearAllUnused()
		{
			this.caches.RemoveAll(delegate(AssetID assetID, ScriptableCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						ScriptableObject @object = cache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						flag2 = true;
					}
				}
				return flag2;
			});
		}

		private IEnumerator LoadImpl(AssetID assetID, Action<ScriptableObject> complete)
		{
			WaitLoadScriptable waitLoad = this.Load(assetID);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			bool flag = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag)
			{
				ScriptablePool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private void SweepCache()
		{
			this.caches.RemoveAll(this.sweepChecker);
		}

		private static Logger logger = LogSystem.GetLogger("ScriptablePool");

		private Dictionary<AssetID, ScriptableCache> caches = new Dictionary<AssetID, ScriptableCache>();

		private Dictionary<ScriptableObject, ScriptableCache> lookup = new Dictionary<ScriptableObject, ScriptableCache>();

		private float defaultReleaseAfterFree = 30f;

		private Func<AssetID, ScriptableCache, bool> sweepChecker;
	}
}
