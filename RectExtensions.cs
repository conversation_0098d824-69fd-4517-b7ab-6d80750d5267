﻿using System;
using System.Text.RegularExpressions;
using UnityEngine;

namespace Nirvana
{
	public static class RectExtensions
	{
		public static Rect Parse(string text)
		{
			Rect rect;
			bool flag = !RectExtensions.TryParse(text, out rect);
			if (flag)
			{
				string text2 = string.Format("The string {0} can not convert to Rect.", text);
				throw new FormatException(text2);
			}
			return rect;
		}

		public static bool TryParse(string text, out Rect rect)
		{
			bool flag = RectExtensions.parseRegex == null;
			if (flag)
			{
				RectExtensions.parseRegex = new Regex("^\\(x:(.*), y:(.*), width:(.*), height:(.*)\\)$");
			}
			Match match = RectExtensions.parseRegex.Match(text);
			bool flag2 = !match.Success || match.Groups.Count != 5;
			bool flag3;
			if (flag2)
			{
				rect = Rect.zero;
				flag3 = false;
			}
			else
			{
				float num;
				bool flag4 = !float.TryParse(match.Groups[1].Value, out num);
				if (flag4)
				{
					rect = Rect.zero;
					flag3 = false;
				}
				else
				{
					float num2;
					bool flag5 = !float.TryParse(match.Groups[2].Value, out num2);
					if (flag5)
					{
						rect = Rect.zero;
						flag3 = false;
					}
					else
					{
						float num3;
						bool flag6 = !float.TryParse(match.Groups[3].Value, out num3);
						if (flag6)
						{
							rect = Rect.zero;
							flag3 = false;
						}
						else
						{
							float num4;
							bool flag7 = !float.TryParse(match.Groups[4].Value, out num4);
							if (flag7)
							{
								rect = Rect.zero;
								flag3 = false;
							}
							else
							{
								rect = new Rect(num, num2, num3, num4);
								flag3 = true;
							}
						}
					}
				}
			}
			return flag3;
		}

		private static Regex parseRegex;
	}
}
