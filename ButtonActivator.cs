﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/Button Activator")]
	public sealed class ButtonActivator : MonoBehaviour
	{
		private void Awake()
		{
			this.button = base.GetComponent<Button>();
			this.toggle = base.GetComponent<Toggle>();
			bool flag = this.button != null;
			if (flag)
			{
				this.button.onClick.AddListener(new UnityAction(this.OnClick));
			}
			else
			{
				bool flag2 = this.toggle != null;
				if (flag2)
				{
					this.toggle.onValueChanged.AddListener(new UnityAction<bool>(this.OnToggleChanged));
				}
			}
		}

		private void OnClick()
		{
			foreach (GameObject gameObject in this.objects)
			{
				gameObject.SetActive(!gameObject.activeSelf);
			}
		}

		private void OnToggleChanged(bool isOn)
		{
			if (isOn)
			{
				foreach (GameObject gameObject in this.objects)
				{
					gameObject.SetActive(!gameObject.activeSelf);
				}
			}
		}

		[SerializeField]
		private GameObject[] objects;

		private Button button;

		private Toggle toggle;
	}
}
