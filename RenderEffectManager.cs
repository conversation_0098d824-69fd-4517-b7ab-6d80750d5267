﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public abstract class RenderEffectManager
	{
		public RenderEffectManager()
		{
			this.registerNode = NirvanaRenderer.RegisterEffectManager(this);
		}

		~RenderEffectManager()
		{
			NirvanaRenderer.UnregisterEffectManager(this.registerNode);
		}

		public abstract void SetupRendererStart(NirvanaRenderer renderer, ref ShaderKeywords keywords, ref MaterialPropertyBlock propertyBlock);

		public abstract void SetupRendererEffect(NirvanaRenderer renderer, RenderEffect effect, ref ShaderKeywords keywords, ref MaterialPropertyBlock propertyBlock);

		public abstract void SetupRendererEnd(NirvanaRenderer renderer, ref ShaderKeywords keywords, ref MaterialPropertyBlock propertyBlock);

		internal void SetupRenderer(NirvanaRenderer renderer, ref ShaderKeywords keywords, ref MaterialPropertyBlock propertyBlock)
		{
			this.SetupRendererStart(renderer, ref keywords, ref propertyBlock);
			bool isStatic = renderer.IsStatic;
			if (isStatic)
			{
				RenderEffect[] array;
				bool flag = !this.staticCache.TryGetValue(renderer, out array);
				if (flag)
				{
					this.cacheList.Clear();
					foreach (RenderEffect renderEffect in this.staticEffects)
					{
						bool flag2 = renderEffect.IsTakeEffect(renderer);
						if (flag2)
						{
							this.cacheList.Add(renderEffect);
							this.SetupRendererEffect(renderer, renderEffect, ref keywords, ref propertyBlock);
						}
					}
					this.staticCache.Add(renderer, this.cacheList.ToArray());
				}
				else
				{
					foreach (RenderEffect renderEffect2 in array)
					{
						bool flag3 = renderEffect2.IsTakeEffect(renderer);
						if (flag3)
						{
							this.SetupRendererEffect(renderer, renderEffect2, ref keywords, ref propertyBlock);
						}
					}
				}
			}
			else
			{
				foreach (RenderEffect renderEffect3 in this.staticEffects)
				{
					bool flag4 = renderEffect3.IsTakeEffect(renderer);
					if (flag4)
					{
						this.SetupRendererEffect(renderer, renderEffect3, ref keywords, ref propertyBlock);
					}
				}
			}
			foreach (RenderEffect renderEffect4 in this.dynamicEffects)
			{
				bool flag5 = renderEffect4.IsTakeEffect(renderer);
				if (flag5)
				{
					this.SetupRendererEffect(renderer, renderEffect4, ref keywords, ref propertyBlock);
				}
			}
			this.SetupRendererEnd(renderer, ref keywords, ref propertyBlock);
		}

		internal LinkedListNode<RenderEffect> AddStaticEffect(RenderEffect effect)
		{
			return this.staticEffects.AddLast(effect);
		}

		internal void RemoveStaticEffect(LinkedListNode<RenderEffect> node)
		{
			this.staticEffects.Remove(node);
		}

		internal LinkedListNode<RenderEffect> AddDynamicEffect(RenderEffect effect)
		{
			return this.dynamicEffects.AddLast(effect);
		}

		internal void RemoveDynamicEffect(LinkedListNode<RenderEffect> node)
		{
			this.dynamicEffects.Remove(node);
		}

		private LinkedList<RenderEffect> staticEffects = new LinkedList<RenderEffect>();

		private LinkedList<RenderEffect> dynamicEffects = new LinkedList<RenderEffect>();

		private Dictionary<NirvanaRenderer, RenderEffect[]> staticCache = new Dictionary<NirvanaRenderer, RenderEffect[]>();

		private List<RenderEffect> cacheList = new List<RenderEffect>();

		private LinkedListNode<RenderEffectManager> registerNode;
	}
}
