﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class UI3DDisplayRecord : MonoBehaviour
	{
		internal void Initialize(Renderer renderer, UI3DDisplayCamera camera)
		{
			this.attachRenderer = renderer;
			this.displayCamera = camera;
			this.layer = renderer.gameObject.layer;
			this.visible = renderer.enabled;
			bool flag = null != renderer.sharedMaterial;
			if (flag)
			{
				this.texture = renderer.sharedMaterial.mainTexture;
				bool flag2 = this.texture != null;
				if (flag2)
				{
					this.oldMipMapBias = this.texture.mipMapBias;
					this.texture.mipMapBias = -5f;
				}
			}
		}

		public void ManualDestroy()
		{
			bool flag = !this.attachRenderer;
			if (!flag)
			{
				this.attachRenderer.enabled = this.visible;
				base.gameObject.layer = this.layer;
				bool flag2 = this.texture != null;
				if (flag2)
				{
					this.texture.mipMapBias = this.oldMipMapBias;
				}
				this.attachRenderer = null;
				this.texture = null;
			}
		}

		private static bool IsParentOf(Transform obj, Transform parent)
		{
			bool flag = obj == parent;
			bool flag2;
			if (flag)
			{
				flag2 = true;
			}
			else
			{
				bool flag3 = obj.parent == null;
				flag2 = !flag3 && UI3DDisplayRecord.IsParentOf(obj.parent, parent);
			}
			return flag2;
		}

		private void OnTransformParentChanged()
		{
			bool flag = this.displayCamera == null || !UI3DDisplayRecord.IsParentOf(base.transform, this.displayCamera.transform);
			if (flag)
			{
				Object.Destroy(this);
			}
		}

		private void OnDestroy()
		{
			this.ManualDestroy();
		}

		private int layer;

		private bool visible;

		private Renderer attachRenderer;

		private UI3DDisplayCamera displayCamera;

		private Texture texture;

		private float oldMipMapBias = 0f;
	}
}
