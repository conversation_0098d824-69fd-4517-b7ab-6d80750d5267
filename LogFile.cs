﻿using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace Nirvana
{
	public sealed class LogFile : ILogAppender
	{
		public LogFile(bool isAutoFlush)
		{
			string text = string.Empty;
			RuntimePlatform platform = Application.platform;
			RuntimePlatform runtimePlatform = platform;
			if (runtimePlatform != null)
			{
				if (runtimePlatform != 2)
				{
					switch (runtimePlatform)
					{
					case 7:
						text = Application.dataPath + "/..";
						break;
					case 8:
						text = Application.persistentDataPath;
						break;
					case 11:
						text = Application.persistentDataPath;
						break;
					}
				}
				else
				{
					text = Application.dataPath;
				}
			}
			else
			{
				text = Application.dataPath + "/..";
			}
			string fullPath = Path.GetFullPath(text + "/Log");
			bool flag = !Directory.Exists(fullPath);
			if (flag)
			{
				Directory.CreateDirectory(fullPath);
			}
			this.filePath = fullPath + "/DiagnosisLog.txt";
			Scheduler.AddFrameListener(delegate
			{
				bool flag2 = Time.time >= this.lastWriteTime + 1f;
				if (flag2)
				{
					File.WriteAllLines(this.filePath, this.messageList.ToArray());
					this.lastWriteTime = Time.time;
				}
			});
		}

		public LogFile(string path)
		{
			this.filePath = path;
		}

		public void AppendLog(LogItem item)
		{
			string text = string.Format("[{0}][{1}][{2}]: {3}", new object[] { item.Severity, item.ModuleName, item.RecordTime, item.Message });
			this.messageList.Add(text);
		}

		public void ClearLog()
		{
			File.WriteAllText(this.filePath, "");
		}

		private string filePath;

		private List<string> messageList = new List<string>();

		private float lastWriteTime;
	}
}
