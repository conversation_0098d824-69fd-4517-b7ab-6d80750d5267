﻿using System;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/UI Follow Target")]
	[RequireComponent(typeof(RectTransform))]
	public sealed class UIFollowTarget : MonoBehaviour
	{
		public Transform Target
		{
			get
			{
				return this.target;
			}
			set
			{
				this.target = value;
			}
		}

		public Canvas Canvas
		{
			get
			{
				return this.canvas;
			}
			set
			{
				this.canvas = value;
			}
		}

		public Camera Camera
		{
			get
			{
				return this.gameCamera;
			}
			set
			{
				this.gameCamera = value;
			}
		}

		public static Vector3 CalculateScreenPosition(Vector3 position, Camera camera, Canvas canvas, RectTransform transform)
		{
			Assert.IsNotNull<Camera>(camera);
			Assert.IsNotNull<Canvas>(canvas);
			Assert.IsNotNull<RectTransform>(transform);
			Vector3 vector = camera.WorldToScreenPoint(position);
			bool flag = vector.z < 0f;
			if (flag)
			{
				vector.x = 10000f;
				vector.y = 10000f;
			}
			Vector3 zero = Vector3.zero;
			RenderMode renderMode = canvas.renderMode;
			RenderMode renderMode2 = renderMode;
			if (renderMode2 != null)
			{
				if (renderMode2 - 1 <= 1)
				{
					RectTransformUtility.ScreenPointToWorldPointInRectangle(transform, vector, canvas.worldCamera, ref zero);
				}
			}
			else
			{
				RectTransformUtility.ScreenPointToWorldPointInRectangle(transform, vector, null, ref zero);
			}
			return zero;
		}

		private void Awake()
		{
			this.rectTransform = base.GetComponent<RectTransform>();
			bool flag = this.gameCamera == null;
			if (flag)
			{
				this.gameCamera = Camera.main;
			}
		}

		private void Start()
		{
			this.SyncPosition();
		}

		private void LateUpdate()
		{
			bool flag = this.gameCamera == null;
			if (flag)
			{
				this.gameCamera = Camera.main;
			}
			this.SyncPosition();
		}

		private void SyncPosition()
		{
			bool flag = this.target == null || this.canvas == null || this.gameCamera == null || this.rectTransform == null;
			if (!flag)
			{
				Vector3 vector = UIFollowTarget.CalculateScreenPosition(this.target.position, this.gameCamera, this.canvas, this.rectTransform);
				base.transform.position = vector;
			}
		}

		[SerializeField]
		private Transform target;

		[SerializeField]
		private Camera gameCamera;

		[SerializeField]
		private Canvas canvas;

		private RectTransform rectTransform;
	}
}
