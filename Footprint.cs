﻿using System;
using UnityEngine;

namespace Nirvana
{
	[CreateAssetMenu(fileName = "Footprint", menuName = "Nirvana/Render/Footprint")]
	public sealed class Footprint : ScriptableObject
	{
		internal Material Material
		{
			get
			{
				return this.material;
			}
		}

		internal Vector2 UV1
		{
			get
			{
				return this.uv1;
			}
		}

		internal Vector2 UV2
		{
			get
			{
				return this.uv2;
			}
		}

		internal Vector2 UV3
		{
			get
			{
				return this.uv3;
			}
		}

		internal Vector2 UV4
		{
			get
			{
				return this.uv4;
			}
		}

		internal Vector2 Size
		{
			get
			{
				return this.size;
			}
		}

		internal float Duration
		{
			get
			{
				return this.duration;
			}
		}

		internal float Fadeout
		{
			get
			{
				return this.fadeout;
			}
		}

		internal float GroundOffset
		{
			get
			{
				return this.groundOffset;
			}
		}

		[SerializeField]
		[Tooltip("The material for the footprint.")]
		private Material material;

		[SerializeField]
		[Tooltip("The uv1 of this footprint.")]
		private Vector2 uv1 = new Vector2(0f, 0f);

		[SerializeField]
		[Tooltip("The uv2 of this footprint.")]
		private Vector2 uv2 = new Vector2(1f, 0f);

		[SerializeField]
		[Tooltip("The uv3 of this footprint.")]
		private Vector2 uv3 = new Vector2(0f, 1f);

		[SerializeField]
		[Tooltip("The uv4 of this footprint.")]
		private Vector2 uv4 = new Vector2(1f, 1f);

		[SerializeField]
		[Tooltip("The size of this footprint.")]
		private Vector2 size = new Vector2(0.15f, 0.5f);

		[SerializeField]
		[Tooltip("The furation of this footprint.")]
		private float duration = 5f;

		[SerializeField]
		[Tooltip("The fadeout of this footprint.")]
		private float fadeout = 1f;

		[SerializeField]
		[Tooltip("The distance the footprints are places above the surface it is placed upon.")]
		private float groundOffset = 0.02f;
	}
}
