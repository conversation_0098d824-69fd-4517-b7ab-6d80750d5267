﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class LogUnity : ILogAppender
	{
		public void AppendLog(LogItem item)
		{
			string text = string.Format("[{0}][{1}]: {2}", item.ModuleName, item.RecordTime, item.Message);
			switch (item.Severity)
			{
			case LogSeverity.Debug:
			case LogSeverity.Info:
				Debug.Log(text);
				return;
			case LogSeverity.Warning:
				Debug.LogWarning(text);
				return;
			}
			Debug.Log<PERSON>rror(text);
		}
	}
}
