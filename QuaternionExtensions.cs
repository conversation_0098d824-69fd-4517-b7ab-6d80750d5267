﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class QuaternionExtensions
	{
		public static Quaternion Parse(string text)
		{
			string[] array = text.Substring(1, text.Length - 2).Split(new char[] { ',' });
			float num = float.Parse(array[0]);
			float num2 = float.Parse(array[1]);
			float num3 = float.Parse(array[2]);
			float num4 = float.Parse(array[3]);
			return new Quaternion(num, num2, num3, num4);
		}
	}
}
