﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Diagnostics/Diagnostics System")]
	[DisallowMultipleComponent]
	public sealed class DiagnosticsSystem : MonoBehaviour
	{
		public static DiagnosticsSystem Instance
		{
			get
			{
				return DiagnosticsSystem.instance;
			}
		}

		private void Awake()
		{
			bool flag = this.destroyOnRelease && !Debug.isDebugBuild;
			if (flag)
			{
				Object.Destroy(base.gameObject);
			}
			else
			{
				DiagnosticsSystem.instance = this;
				bool flag2 = this.makePersistent;
				if (flag2)
				{
					Object.DontDestroyOnLoad(this);
				}
				this.fpsProfiler.Initialize();
				this.log.Initialize();
			}
		}

		private void Start()
		{
			this.dockers.AddDocker(this.generic);
			this.dockers.AddDocker(this.fpsProfiler);
			this.dockers.AddDocker(this.memoryProfiler);
			this.workbench.AddTab(this.generic);
			this.workbench.AddTab(this.fpsProfiler);
			this.workbench.AddTab(this.memoryProfiler);
			this.workbench.AddTab(this.log);
		}

		private void OnDestroy()
		{
			DiagnosticsSystem.instance = null;
		}

		private void Update()
		{
			bool flag = !this.showWorkbench && this.hotspot.IsTrigger();
			if (flag)
			{
				this.showWorkbench = true;
			}
			bool flag2 = this.showWorkbench;
			if (flag2)
			{
				bool flag3 = this.generic.ErrorTips && this.log.HasError;
				if (flag3)
				{
					this.log.HasError = false;
				}
			}
			this.fpsProfiler.Update();
		}

		private void OnGUI()
		{
			bool flag = this.generic.ErrorTips && this.log.HasError;
			if (flag)
			{
				this.hotspot.DrawErrorTips();
			}
			else
			{
				bool hotspotTips = this.generic.HotspotTips;
				if (hotspotTips)
				{
					this.hotspot.DrawClickTips();
				}
			}
			this.dockers.Draw();
			bool flag2 = this.showWorkbench;
			if (flag2)
			{
				bool flag3 = Screen.height > Screen.width;
				Vector3 vector;
				if (flag3)
				{
					vector..ctor((float)Screen.width / 768f, (float)Screen.height / 1280f, 1f);
				}
				else
				{
					vector..ctor((float)Screen.width / 1280f, (float)Screen.height / 768f, 1f);
				}
				Matrix4x4 matrix = GUI.matrix;
				GUI.matrix = Matrix4x4.TRS(Vector3.zero, Quaternion.identity, vector);
				this.showWorkbench = this.workbench.Draw(this);
				GUI.matrix = matrix;
			}
		}

		private static DiagnosticsSystem instance;

		[SerializeField]
		[Tooltip("Whether to make the DiagnosticsSystem persist through scene loads.")]
		private bool makePersistent = true;

		[SerializeField]
		[Tooltip("Destroy self on release building.")]
		private bool destroyOnRelease = true;

		[SerializeField]
		[Tooltip("The hotspot for diagnostics.")]
		private DiagnosticsHotspot hotspot = new DiagnosticsHotspot();

		[SerializeField]
		[Tooltip("The generic config for diagnostics.")]
		private DiagnosticsGeneric generic = new DiagnosticsGeneric();

		[SerializeField]
		[Tooltip("The FPS profile config for diagnostics.")]
		private DiagnosticsFPSProfiler fpsProfiler = new DiagnosticsFPSProfiler();

		[SerializeField]
		[Tooltip("The memory profile config for diagnostics.")]
		private DiagnosticsMemoryProfiler memoryProfiler = new DiagnosticsMemoryProfiler();

		[SerializeField]
		[Tooltip("The log config for diagnostics.")]
		private DiagnosticsLog log = new DiagnosticsLog();

		private DiagnosticsWorkbench workbench = new DiagnosticsWorkbench();

		private DiagnosticsDockers dockers = new DiagnosticsDockers();

		private bool showWorkbench = false;
	}
}
