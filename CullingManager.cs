﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	public sealed class CullingManager : Singleton<CullingManager>
	{
		public LinkedListNode<NirvanaRenderer> RegisterRenderer(NirvanaRenderer renderer)
		{
			return this.renderers.AddLast(renderer);
		}

		public void UnregisterRenderer(LinkedListNode<NirvanaRenderer> node)
		{
			this.renderers.Remove(node);
		}

		public void CullingRenderers(Plane[] planes)
		{
			Assert.AreEqual(0, this.culling.Count);
			LinkedListNode<NirvanaRenderer> linkedListNode = this.renderers.First;
			while (linkedListNode != null)
			{
				NirvanaRenderer value = linkedListNode.Value;
				linkedListNode = linkedListNode.Next;
				Bounds[] customBounds = value.CustomBounds;
				bool flag = customBounds != null && customBounds.Length != 0;
				if (flag)
				{
					bool flag2 = false;
					foreach (Bounds bounds in customBounds)
					{
						bool flag3 = GeometryUtility.TestPlanesAABB(planes, bounds);
						if (flag3)
						{
							flag2 = true;
							break;
						}
					}
					bool flag4 = !flag2;
					if (flag4)
					{
						value.UnityRenderer.enabled = false;
						this.culling.Add(value);
					}
				}
			}
		}

		public void RestoreRenderers()
		{
			for (int i = 0; i < this.culling.Count; i++)
			{
				NirvanaRenderer nirvanaRenderer = this.culling[i];
				nirvanaRenderer.UnityRenderer.enabled = true;
			}
			this.culling.Clear();
		}

		private LinkedList<NirvanaRenderer> renderers = new LinkedList<NirvanaRenderer>();

		private List<NirvanaRenderer> culling = new List<NirvanaRenderer>();
	}
}
