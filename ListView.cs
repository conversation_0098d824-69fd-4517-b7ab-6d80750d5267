﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Nirvana
{
	[RequireComponent(typeof(ScrollRect))]
	public abstract class ListView : MonoBeh<PERSON>our, IBeginDragHandler, IEventSystemHandler, IEndDragHandler
	{
		internal ListView()
		{
		}

		public ListView.GetCellDelegate GetCellDel { get; set; }

		public ListView.CellCountDelegate CellCountDel { get; set; }

		public ListView.GetCellSizeDelegate GetCellSizeDel { get; set; }

		public ListView.RecycleCellDelegate RecycleCellDel { get; set; }

		public int ActiveCellsStartIndex
		{
			get
			{
				return this.activeCellsStartIndex;
			}
		}

		public int ActiveCellsEndIndex
		{
			get
			{
				return this.activeCellsEndIndex;
			}
		}

		public bool IsJumping
		{
			get
			{
				return this.jumpIndex >= 0;
			}
		}

		public GameObject[] ActiveCells
		{
			get
			{
				this.activeCells.Sort((ListView.CellData a, ListView.CellData b) => a.Index.CompareTo(b.Index));
				GameObject[] array = new GameObject[this.activeCells.Count];
				for (int i = 0; i < array.Length; i++)
				{
					array[i] = this.activeCells[i].Cell;
				}
				return array;
			}
		}

		protected RectTransform ScrollView
		{
			get
			{
				return this.ScrollRect.viewport;
			}
		}

		protected RectTransform ScrollContent
		{
			get
			{
				return this.ScrollRect.content;
			}
		}

		protected ScrollRect ScrollRect
		{
			get
			{
				bool flag = this.scrollRect == null;
				if (flag)
				{
					this.scrollRect = base.GetComponent<ScrollRect>();
				}
				return this.scrollRect;
			}
		}

		private protected int CellCount { protected get; private set; }

		public void OnBeginDrag(PointerEventData eventData)
		{
			this.draging = true;
		}

		public void OnEndDrag(PointerEventData eventData)
		{
			this.draging = false;
		}

		public void Reload(Action callback = null)
		{
			this.refreshSize = true;
			this.refreshData = true;
			this.refreshCallback = callback;
		}

		public void JumpToIndex(int index, float offset = 0f, float speed = -1f)
		{
			this.jumpIndex = index;
			this.jumpOffset = offset;
			this.jumpSpeed = speed;
		}

		protected abstract Vector2 CalculateContentSize(int cellCount);

		protected abstract void CalculateCurrentActiveCellRange(Vector2 scrollPosition, out int startIndex, out int endIndex);

		protected abstract void LayoutCell(GameObject cell, int index);

		protected abstract Vector2 GetCellPositionAtIndex(int index);

		protected virtual void UpdateCell(GameObject cell, int index)
		{
		}

		protected virtual void UpdateSnapping()
		{
		}

		protected void OnEnable()
		{
			ScrollRect scrollRect = this.ScrollRect;
			scrollRect.onValueChanged.AddListener(new UnityAction<Vector2>(this.OnScrollRectValueChanged));
		}

		protected void OnDisable()
		{
			ScrollRect scrollRect = this.ScrollRect;
			scrollRect.onValueChanged.RemoveListener(new UnityAction<Vector2>(this.OnScrollRectValueChanged));
		}

		private void Update()
		{
			this.DoRefreshSize();
			bool flag = this.jumpIndex >= 0;
			if (flag)
			{
				bool flag2 = this.CellCount > 0;
				if (flag2)
				{
					this.UpdateJumping();
				}
			}
			else
			{
				bool flag3 = !this.draging;
				if (flag3)
				{
					this.UpdateSnapping();
				}
			}
			this.DoRefresh();
		}

		private void LateUpdate()
		{
			this.DoRefreshSize();
			bool flag = this.jumpIndex >= 0 && this.jumpSpeed <= 0f;
			if (flag)
			{
				this.UpdateJumping();
			}
			this.DoRefresh();
		}

		private void UpdateJumping()
		{
			this.jumpIndex = Mathf.Clamp(this.jumpIndex, 0, this.CellCount - 1);
			Vector2 cellPositionAtIndex = this.GetCellPositionAtIndex(this.jumpIndex);
			Rect rect = this.ScrollContent.rect;
			Rect rect2 = this.ScrollView.rect;
			float num = rect.width - rect2.width;
			float num2 = rect.height - rect2.height;
			ScrollRect scrollRect = this.ScrollRect;
			Vector2 normalizedPosition = scrollRect.normalizedPosition;
			bool flag = num > 0f;
			if (flag)
			{
				float num3 = cellPositionAtIndex.x + this.jumpOffset;
				normalizedPosition.x = num3 / num;
			}
			bool flag2 = num2 > 0f;
			if (flag2)
			{
				float num4 = cellPositionAtIndex.y + this.jumpOffset;
				normalizedPosition.y = 1f - num4 / num2;
			}
			bool flag3 = this.jumpSpeed > 0f;
			if (flag3)
			{
				Vector2 normalizedPosition2 = scrollRect.normalizedPosition;
				float num5 = normalizedPosition.x - normalizedPosition2.x;
				float num6 = Mathf.Sign(num5) * this.jumpSpeed * Time.deltaTime;
				bool flag4 = Mathf.Abs(num6) < Mathf.Abs(num5);
				if (flag4)
				{
					normalizedPosition2.x += num6;
					scrollRect.normalizedPosition = normalizedPosition2;
				}
				else
				{
					scrollRect.normalizedPosition = normalizedPosition;
					this.jumpIndex = -1;
				}
			}
			else
			{
				scrollRect.normalizedPosition = normalizedPosition;
				this.jumpIndex = -1;
			}
		}

		private void DoRefreshSize()
		{
			bool flag = this.refreshSize;
			if (flag)
			{
				this.CellCount = this.CellCountDel();
				Vector2 vector = this.CalculateContentSize(this.CellCount);
				ScrollRect scrollRect = this.ScrollRect;
				RectTransform viewport = scrollRect.viewport;
				bool flag2 = viewport != null;
				if (flag2)
				{
					vector.x = Mathf.Max(vector.x, viewport.rect.width);
					vector.y = Mathf.Max(vector.y, viewport.rect.height);
				}
				RectTransform content = scrollRect.content;
				content.anchorMin = new Vector2(0.5f, 0.5f);
				content.anchorMax = new Vector2(0.5f, 0.5f);
				content.pivot = new Vector2(0.5f, 0.5f);
				content.sizeDelta = vector;
				this.refreshSize = false;
			}
		}

		private void DoRefresh()
		{
			bool flag = this.refreshData;
			if (flag)
			{
				this.RefreshData();
				this.refreshData = false;
				this.refreshActive = false;
				bool flag2 = this.refreshCallback != null;
				if (flag2)
				{
					this.refreshCallback();
					this.refreshCallback = null;
				}
			}
			else
			{
				bool flag3 = this.refreshActive;
				if (flag3)
				{
					this.RefreshActive();
					this.refreshActive = false;
				}
			}
		}

		private void OnScrollRectValueChanged(Vector2 val)
		{
			this.refreshActive = true;
		}

		private void RefreshActive()
		{
			int num;
			int num2;
			this.CalculateCurrentActiveCellRange(this.scrollRect.normalizedPosition, out num, out num2);
			num = Mathf.Clamp(num, 0, this.CellCount);
			num2 = Mathf.Clamp(num2, 0, this.CellCount);
			bool flag = num != this.activeCellsStartIndex || num2 != this.activeCellsEndIndex;
			if (flag)
			{
				this.ResetVisibleCells(num, num2);
			}
			foreach (ListView.CellData cellData in this.activeCells)
			{
				this.UpdateCell(cellData.Cell, cellData.Index);
			}
		}

		private void ResetVisibleCells(int startIndex, int endIndex)
		{
			this.activeCells.RemoveAll(delegate(ListView.CellData cellData)
			{
				int index = cellData.Index;
				bool flag4 = index < startIndex || index >= endIndex;
				bool flag6;
				if (flag4)
				{
					cellData.Cell.transform.localPosition = Vector3.zero;
					cellData.Cell.transform.localRotation = Quaternion.identity;
					cellData.Cell.transform.localScale = Vector3.one;
					bool flag5 = this.RecycleCellDel != null;
					if (flag5)
					{
						this.RecycleCellDel(cellData.Index, cellData.Cell);
					}
					else
					{
						Singleton<GameObjectPool>.Instance.Free(cellData.Cell, false);
					}
					flag6 = true;
				}
				else
				{
					flag6 = false;
				}
				return flag6;
			});
			ScrollRect scrollRect = this.ScrollRect;
			for (int i = startIndex; i < endIndex; i++)
			{
				bool flag = false;
				foreach (ListView.CellData cellData3 in this.activeCells)
				{
					bool flag2 = cellData3.Index == i;
					if (flag2)
					{
						flag = true;
						break;
					}
				}
				bool flag3 = flag;
				if (!flag3)
				{
					GameObject gameObject = this.GetCellDel(i);
					gameObject.transform.SetParent(scrollRect.content, false);
					this.LayoutCell(gameObject, i);
					ListView.CellData cellData2 = new ListView.CellData
					{
						Cell = gameObject,
						Index = i
					};
					this.activeCells.Add(cellData2);
				}
			}
			this.activeCellsStartIndex = startIndex;
			this.activeCellsEndIndex = endIndex;
		}

		private void RefreshData()
		{
			int num;
			int num2;
			this.CalculateCurrentActiveCellRange(this.scrollRect.normalizedPosition, out num, out num2);
			num = Mathf.Clamp(num, 0, this.CellCount);
			num2 = Mathf.Clamp(num2, 0, this.CellCount);
			foreach (ListView.CellData cellData in this.activeCells)
			{
				bool flag = this.RecycleCellDel != null;
				if (flag)
				{
					this.RecycleCellDel(cellData.Index, cellData.Cell);
				}
				else
				{
					Singleton<GameObjectPool>.Instance.Free(cellData.Cell, false);
				}
			}
			this.activeCells.Clear();
			ScrollRect scrollRect = this.ScrollRect;
			for (int i = num; i < num2; i++)
			{
				GameObject gameObject = this.GetCellDel(i);
				gameObject.transform.SetParent(scrollRect.content, false);
				this.LayoutCell(gameObject, i);
				ListView.CellData cellData2 = new ListView.CellData
				{
					Cell = gameObject,
					Index = i
				};
				this.activeCells.Add(cellData2);
			}
			this.activeCellsStartIndex = num;
			this.activeCellsEndIndex = num2;
			foreach (ListView.CellData cellData3 in this.activeCells)
			{
				this.UpdateCell(cellData3.Cell, cellData3.Index);
			}
		}

		private ScrollRect scrollRect;

		private bool refreshSize;

		private bool refreshData;

		private Action refreshCallback;

		private bool refreshActive;

		private int activeCellsStartIndex;

		private int activeCellsEndIndex;

		private List<ListView.CellData> activeCells = new List<ListView.CellData>();

		private bool draging = false;

		private int jumpIndex = -1;

		private float jumpOffset = 0f;

		private float jumpSpeed = -1f;

		public delegate GameObject GetCellDelegate(int index);

		public delegate int CellCountDelegate();

		public delegate Vector2 GetCellSizeDelegate(int index);

		public delegate void RecycleCellDelegate(int index, GameObject go);

		private struct CellData
		{
			public GameObject Cell { readonly get; set; }

			public int Index { readonly get; set; }
		}
	}
}
