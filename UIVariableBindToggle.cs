﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Toggle")]
	[RequireComponent(typeof(Toggle))]
	public sealed class UIVariableBindToggle : UIVariableBindBool
	{
		protected override void OnValueChanged()
		{
			bool flag = this.toggle == null;
			if (flag)
			{
				this.toggle = base.GetComponent<Toggle>();
			}
			this.toggle.isOn = base.GetResult();
		}

		private Toggle toggle;
	}
}
