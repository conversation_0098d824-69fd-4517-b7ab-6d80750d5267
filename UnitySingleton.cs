﻿using System;
using UnityEngine;

namespace Nirvana
{
	public class UnitySingleton<T> : MonoBehaviour where T : MonoBehaviour
	{
		public static T Instance
		{
			get
			{
				UnitySingleton<T>.CheckInstance();
				return UnitySingleton<T>.instance;
			}
		}

		public static bool InstanceExisted
		{
			get
			{
				return UnitySingleton<T>.instance != null;
			}
		}

		public static void CheckInstance()
		{
			bool flag = UnitySingleton<T>.instance == null;
			if (flag)
			{
				GameObject gameObject = new GameObject(typeof(T).Name, new Type[] { typeof(T) });
				Object.DontDestroyOnLoad(gameObject);
				UnitySingleton<T>.instance = gameObject.GetComponent<T>();
			}
		}

		protected void OnDestroy()
		{
			UnitySingleton<T>.instance = default(T);
		}

		protected void OnApplicationQuit()
		{
			bool flag = UnitySingleton<T>.instance != null;
			if (flag)
			{
				Object.DestroyImmediate(UnitySingleton<T>.instance.gameObject);
			}
		}

		private static T instance;
	}
}
