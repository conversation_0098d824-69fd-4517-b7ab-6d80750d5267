﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/UI Blur")]
	[ExecuteInEditMode]
	[DisallowMultipleComponent]
	[RequireComponent(typeof(UIMaterialEffect))]
	public sealed class UIBlur : MonoBehaviour
	{
		public float Blur
		{
			get
			{
				return this.blur;
			}
			set
			{
				bool flag = this.blur != value;
				if (flag)
				{
					this.blur = value;
					this.Refresh();
				}
			}
		}

		private void Awake()
		{
			this.Refresh();
		}

		private void Refresh()
		{
			bool flag = this.materialEffect == null;
			if (flag)
			{
				this.materialEffect = this.GetOrAddComponent<UIMaterialEffect>();
			}
			UIEffectMaterialKey materialKey = this.materialEffect.MaterialKey;
			materialKey.BlurDistance = (float)((byte)this.blur);
			this.materialEffect.MaterialKey = materialKey;
			this.materialEffect.MarkDirty();
		}

		[SerializeField]
		[Range(0f, 0.25f)]
		private float blur = 0f;

		private UIMaterialEffect materialEffect;
	}
}
