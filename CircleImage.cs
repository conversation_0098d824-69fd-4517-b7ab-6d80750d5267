﻿using System;
using UnityEngine;
using UnityEngine.Sprites;
using UnityEngine.UI;

namespace Nirvana
{
	public sealed class CircleImage : Image
	{
		public int SegmentCount
		{
			get
			{
				return this.segmentCount;
			}
			set
			{
				bool flag = this.segmentCount != value;
				if (flag)
				{
					this.segmentCount = value;
					this.SetVerticesDirty();
				}
			}
		}

		protected override void OnPopulateMesh(VertexHelper vh)
		{
			vh.Clear();
			Vector2 pivot = base.rectTransform.pivot;
			Rect rect = base.rectTransform.rect;
			float num = -pivot.x * rect.width;
			float width = base.rectTransform.rect.width;
			float height = base.rectTransform.rect.height;
			Vector4 vector = ((base.overrideSprite != null) ? DataUtility.GetOuterUV(base.overrideSprite) : Vector4.zero);
			float num2 = (vector.x + vector.z) * 0.5f;
			float num3 = (vector.y + vector.w) * 0.5f;
			float num4 = (vector.z - vector.x) / width;
			float num5 = (vector.w - vector.y) / height;
			float num6 = (float)this.fillPercent / 100f * 6.2831855f / (float)this.segmentCount;
			float num7 = 0f;
			Vector2 vector2 = Vector2.zero;
			for (int i = 0; i < this.segmentCount + 1; i++)
			{
				float num8 = Mathf.Cos(num7);
				float num9 = Mathf.Sin(num7);
				Vector2 vector3 = vector2;
				Vector2 vector4;
				vector4..ctor(num * num8, num * num9);
				Vector2 zero = Vector2.zero;
				Vector2 zero2 = Vector2.zero;
				vector2 = vector4;
				Vector2 vector5;
				vector5..ctor(vector3.x * num4 + num2, vector3.y * num5 + num3);
				Vector2 vector6;
				vector6..ctor(vector4.x * num4 + num2, vector4.y * num5 + num3);
				Vector2 vector7;
				vector7..ctor(zero.x * num4 + num2, zero.y * num5 + num3);
				Vector2 vector8;
				vector8..ctor(zero2.x * num4 + num2, zero2.y * num5 + num3);
				UIVertex[] array = new UIVertex[4];
				int num10 = 0;
				UIVertex uivertex = default(UIVertex);
				uivertex.color = this.color;
				uivertex.position = vector3;
				uivertex.uv0 = vector5;
				array[num10] = uivertex;
				int num11 = 1;
				uivertex = default(UIVertex);
				uivertex.color = this.color;
				uivertex.position = vector4;
				uivertex.uv0 = vector6;
				array[num11] = uivertex;
				int num12 = 2;
				uivertex = default(UIVertex);
				uivertex.color = this.color;
				uivertex.position = zero;
				uivertex.uv0 = vector7;
				array[num12] = uivertex;
				int num13 = 3;
				uivertex = default(UIVertex);
				uivertex.color = this.color;
				uivertex.position = zero2;
				uivertex.uv0 = vector8;
				array[num13] = uivertex;
				UIVertex[] array2 = array;
				vh.AddUIVertexQuad(array2);
				num7 += num6;
			}
		}

		[SerializeField]
		[Range(4f, 360f)]
		private int segmentCount = 36;

		[SerializeField]
		[Range(-100f, 100f)]
		private int fillPercent = 100;
	}
}
