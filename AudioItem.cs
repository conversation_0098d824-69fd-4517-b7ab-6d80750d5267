﻿using System;
using UnityEngine;
using UnityEngine.Audio;

namespace Nirvana
{
	[CreateAssetMenu(fileName = "AudioItem", menuName = "Nirvana/Audio/AudioItem")]
	public sealed class AudioItem : ScriptableObject
	{
		public float Delay
		{
			get
			{
				return this.delay;
			}
		}

		public void Reset()
		{
			this.index = 0;
			this.lastTime = 0f;
			this.playingCount = 0;
		}

		internal void SetupAudioSource(AudioSource source, AudioSubItem subItem)
		{
			source.transform.SetPositionAndRotation(Vector3.zero, Quaternion.identity);
			source.transform.localScale = Vector3.one;
			source.clip = subItem.Clip;
			bool flag = subItem.MixerGroup != null;
			if (flag)
			{
				source.outputAudioMixerGroup = subItem.MixerGroup;
			}
			else
			{
				source.outputAudioMixerGroup = this.outputAudioMixerGroup;
			}
			source.volume = this.volume * subItem.GetVolume();
			source.pitch = subItem.GetPitch();
			source.time = subItem.GetStartAt();
			source.spatialBlend = this.spatialBlend;
		}

		internal void ReducePlayingCount(IAudioController ctrl)
		{
			this.playingCount--;
			bool flag = this.audioGroup != null;
			if (flag)
			{
				this.audioGroup.StopPlaying(ctrl);
			}
		}

		public IAudioController Play(AudioSourcePool pool)
		{
			bool flag = this.subItems.Length == 0;
			IAudioController audioController;
			if (flag)
			{
				audioController = AudioDummyController.Default;
			}
			else
			{
				bool flag2 = Time.realtimeSinceStartup < this.lastTime + this.interval;
				if (flag2)
				{
					audioController = AudioDummyController.Default;
				}
				else
				{
					bool flag3 = this.maxCount > 0 && this.playingCount >= this.maxCount;
					if (flag3)
					{
						audioController = AudioDummyController.Default;
					}
					else
					{
						bool flag4 = this.audioGroup != null;
						if (flag4)
						{
							bool flag5 = !this.audioGroup.CheckPlayableAndTryToEliminate();
							if (flag5)
							{
								return AudioDummyController.Default;
							}
						}
						this.playingCount++;
						this.lastTime = Time.realtimeSinceStartup;
						IAudioController audioController2;
						switch (this.playMode)
						{
						case AudioItem.AudioPlayMode.SequencePick:
							audioController2 = this.PlaySequencePick(pool);
							break;
						case AudioItem.AudioPlayMode.ShufflePick:
							audioController2 = this.PlayShufflePick(pool);
							break;
						case AudioItem.AudioPlayMode.RandomPick:
							audioController2 = this.PlayRandomPick(pool);
							break;
						case AudioItem.AudioPlayMode.SequenceLoop:
							audioController2 = this.PlaySequenceLoop(pool);
							break;
						case AudioItem.AudioPlayMode.ShuffleLoop:
							audioController2 = this.PlayShuffleLoop(pool);
							break;
						default:
							audioController2 = AudioDummyController.Default;
							break;
						}
						bool flag6 = this.audioGroup != null && audioController2 != AudioDummyController.Default;
						if (flag6)
						{
							this.audioGroup.AddPlaying(audioController2);
						}
						audioController = audioController2;
					}
				}
			}
			return audioController;
		}

		private IAudioController PlaySequencePick(AudioSourcePool pool)
		{
			bool flag = this.index >= this.subItems.Length;
			if (flag)
			{
				this.index = 0;
			}
			AudioSubItem[] array = this.subItems;
			int num = this.index;
			this.index = num + 1;
			AudioSubItem audioSubItem = array[num];
			return new AudioSingleController(pool, this, audioSubItem, false);
		}

		private IAudioController PlayShufflePick(AudioSourcePool pool)
		{
			bool flag = this.sequence == null || this.sequence.Length != this.subItems.Length;
			if (flag)
			{
				this.sequence = new AudioSubItem[this.subItems.Length];
				for (int i = 0; i < this.subItems.Length; i++)
				{
					this.sequence[i] = this.subItems[i];
				}
				this.index = 0;
				this.sequence.Shuffle<AudioSubItem>();
			}
			bool flag2 = this.index >= this.subItems.Length;
			if (flag2)
			{
				this.index = 0;
				this.sequence.Shuffle<AudioSubItem>();
			}
			AudioSubItem[] array = this.sequence;
			int num = this.index;
			this.index = num + 1;
			AudioSubItem audioSubItem = array[num];
			return new AudioSingleController(pool, this, audioSubItem, false);
		}

		private IAudioController PlayRandomPick(AudioSourcePool pool)
		{
			float num = 0f;
			foreach (AudioSubItem audioSubItem in this.subItems)
			{
				num += audioSubItem.Weight;
			}
			bool flag = Mathf.Approximately(num, 0f);
			IAudioController audioController;
			if (flag)
			{
				int num2 = Random.Range(0, this.subItems.Length);
				AudioSubItem audioSubItem2 = this.subItems[num2];
				audioController = new AudioSingleController(pool, this, audioSubItem2, false);
			}
			else
			{
				float num3 = Random.Range(0f, num);
				foreach (AudioSubItem audioSubItem3 in this.subItems)
				{
					num3 -= audioSubItem3.Weight;
					bool flag2 = num3 <= 0f;
					if (flag2)
					{
						return new AudioSingleController(pool, this, audioSubItem3, false);
					}
				}
				audioController = AudioDummyController.Default;
			}
			return audioController;
		}

		private IAudioController PlaySequenceLoop(AudioSourcePool pool)
		{
			bool flag = this.subItems.Length == 1;
			IAudioController audioController;
			if (flag)
			{
				AudioSubItem audioSubItem = this.subItems[0];
				audioController = new AudioSingleController(pool, this, audioSubItem, true);
			}
			else
			{
				AudioSource audioSource = pool.Allocate(base.name);
				audioSource.loop = false;
				AudioSequenceController audioSequenceController = new AudioSequenceController(this, pool, audioSource, this.subItems);
				audioController = audioSequenceController;
			}
			return audioController;
		}

		private IAudioController PlayShuffleLoop(AudioSourcePool pool)
		{
			return AudioDummyController.Default;
		}

		public bool IsValid()
		{
			for (int i = 0; i < this.subItems.Length; i++)
			{
				bool flag = this.subItems[i].Clip == null;
				if (flag)
				{
					return false;
				}
			}
			return true;
		}

		[SerializeField]
		[Tooltip("How to play a sub-item.")]
		[EnumLabel]
		private AudioItem.AudioPlayMode playMode;

		[SerializeField]
		[Tooltip("The audio sub-items.")]
		private AudioSubItem[] subItems;

		[SerializeField]
		[Tooltip("The output audio mixer group.")]
		private AudioMixerGroup outputAudioMixerGroup;

		[SerializeField]
		[Tooltip("The volume control for this audio item.")]
		[Range(0f, 1f)]
		private float volume = 1f;

		[SerializeField]
		[Tooltip("The delay of this audio.")]
		private float delay = 0f;

		[SerializeField]
		[Tooltip("The spatial blend, 0 means this audio is pure 2D.")]
		[Range(0f, 1f)]
		private float spatialBlend = 0f;

		[SerializeField]
		[Tooltip("The play interval between two play.")]
		private float interval = 0f;

		[SerializeField]
		[Tooltip("The max count of this item can play, 0 mean no limit.")]
		private int maxCount = 0;

		[SerializeField]
		[Tooltip("The group used to control the audio playing.")]
		private AudioGroup audioGroup;

		private AudioSubItem[] sequence;

		private int index;

		private float lastTime;

		private int playingCount;

		private enum AudioPlayMode
		{
			[EnumLabel("Sequence Pick")]
			SequencePick,
			[EnumLabel("Shuffle Pick")]
			ShufflePick,
			[EnumLabel("Random Pick")]
			RandomPick,
			[EnumLabel("Sequence Loop")]
			SequenceLoop,
			[EnumLabel("Shuffle Loop")]
			ShuffleLoop
		}
	}
}
