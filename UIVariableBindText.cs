﻿using System;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Text")]
	[RequireComponent(typeof(Text))]
	public sealed class UIVariableBindText : UIVariableBind
	{
		public string Format
		{
			get
			{
				return this.format;
			}
			set
			{
				bool flag = this.format != value;
				if (flag)
				{
					this.format = value;
					this.OnValueChanged();
				}
			}
		}

		public override void BindVariables()
		{
			Assert.IsNull<UIVariable[]>(this.paramVariables);
			bool flag = this.paramBinds != null && this.paramBinds.Length != 0;
			if (flag)
			{
				this.paramVariables = new UIVariable[this.paramBinds.Length];
				for (int i = 0; i < this.paramBinds.Length; i++)
				{
					string text = this.paramBinds[i];
					bool flag2 = string.IsNullOrEmpty(text);
					if (!flag2)
					{
						UIVariable uivariable = base.FindVariable(text);
						bool flag3 = uivariable == null;
						if (flag3)
						{
							UIVariableBindText.logger.LogWarning("{0} can not find a variable {1}", new object[] { base.name, text });
						}
						else
						{
							uivariable.OnValueInitialized += this.OnValueChanged;
							uivariable.OnValueChanged += this.OnValueChanged;
							this.paramVariables[i] = uivariable;
						}
					}
				}
				this.OnValueChanged();
			}
		}

		public override void UnbindVariables()
		{
			bool flag = this.paramVariables != null;
			if (flag)
			{
				foreach (UIVariable uivariable in this.paramVariables)
				{
					bool flag2 = uivariable != null;
					if (flag2)
					{
						uivariable.OnValueInitialized -= this.OnValueChanged;
						uivariable.OnValueChanged -= this.OnValueChanged;
					}
				}
				this.paramVariables = null;
			}
		}

		private void OnValueChanged()
		{
			bool flag = this.text == null;
			if (flag)
			{
				this.text = base.GetComponent<Text>();
			}
			bool flag2 = this.text == null || this.paramBinds == null || this.paramVariables == null;
			if (!flag2)
			{
				bool flag3 = string.IsNullOrEmpty(this.format);
				if (flag3)
				{
					bool flag4 = this.paramBinds.Length != 0;
					if (flag4)
					{
						UIVariable uivariable = this.paramVariables[0];
						bool flag5 = uivariable != null;
						if (flag5)
						{
							object valueObject = uivariable.ValueObject;
							bool flag6 = valueObject != null;
							if (flag6)
							{
								this.text.text = valueObject.ToString();
							}
						}
					}
				}
				else
				{
					object[] array = new object[this.paramBinds.Length];
					for (int i = 0; i < this.paramBinds.Length; i++)
					{
						UIVariable uivariable2 = this.paramVariables[i];
						bool flag7 = uivariable2 != null;
						if (flag7)
						{
							array[i] = uivariable2.ValueObject;
						}
					}
					try
					{
						this.text.text = string.Format(this.format, array);
					}
					catch (FormatException ex)
					{
						bool isPlaying = Application.isPlaying;
						if (isPlaying)
						{
							Debug.LogError(ex.Message, this);
						}
					}
				}
			}
		}

		private new void Awake()
		{
			base.Awake();
			this.text = base.GetComponent<Text>();
			bool flag = this.text == null;
			if (flag)
			{
			}
		}

		private static Logger logger = LogSystem.GetLogger("UIVariableBindText");

		[SerializeField]
		[Delayed]
		[TextArea(2, 10)]
		public string format;

		[SerializeField]
		[VariableName(UIVariableType.Boolean, UIVariableType.Integer, UIVariableType.Float, UIVariableType.String)]
		public string[] paramBinds;

		private Text text;

		[NonSerialized]
		public UIVariable[] paramVariables;
	}
}
