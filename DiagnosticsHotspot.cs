﻿using System;
using UnityEngine;

namespace Nirvana
{
	[Serializable]
	internal sealed class DiagnosticsHotspot
	{
		internal void DrawErrorTips()
		{
			float num = 0.6f * Mathf.Clamp01(Mathf.Sin(4f * Time.time) - 0.5f);
			Color color;
			color..ctor(1f, 0f, 0f, num);
			this.DrawTips(color);
		}

		internal void DrawClickTips()
		{
			float num = 0.6f * Mathf.Clamp01(Mathf.Sin(Time.time) - 0.5f);
			Color color;
			color..ctor(1f, 1f, 1f, num);
			this.DrawTips(color);
		}

		internal bool IsTrigger()
		{
			foreach (KeyCode keyCode in this.hotKeys)
			{
				bool keyDown = Input.GetKeyDown(keyCode);
				if (keyDown)
				{
					return true;
				}
			}
			bool flag = this.IsClickSpot();
			if (flag)
			{
				float num = Time.unscaledTime - this.hotSpotClickTime;
				bool flag2 = num < 0.5f;
				if (flag2)
				{
					int num2 = this.hotSpotClickCounter + 1;
					this.hotSpotClickCounter = num2;
					bool flag3 = num2 >= 2;
					if (flag3)
					{
						return true;
					}
				}
				else
				{
					this.hotSpotClickCounter = 0;
				}
				this.hotSpotClickTime = Time.unscaledTime;
			}
			return false;
		}

		private void DrawTips(Color color)
		{
			bool flag = this.hotSpotTexture == null;
			if (flag)
			{
				this.hotSpotTexture = Texture2D.whiteTexture;
			}
			Color color2 = GUI.color;
			GUI.color = color;
			foreach (Rect rect in this.hotSpots)
			{
				GUI.DrawTexture(this.TransformSpot(rect), this.hotSpotTexture, 0, true);
			}
			GUI.color = color2;
		}

		private bool IsClickSpot()
		{
			for (int i = 0; i < Input.touchCount; i++)
			{
				Touch touch = Input.GetTouch(i);
				bool flag = 3 == touch.phase;
				if (flag)
				{
					foreach (Rect rect in this.hotSpots)
					{
						bool flag2 = this.TransformSpot(rect).Contains(touch.position);
						if (flag2)
						{
							return true;
						}
					}
				}
			}
			bool mouseButtonUp = Input.GetMouseButtonUp(0);
			if (mouseButtonUp)
			{
				Vector2 vector;
				vector..ctor(Input.mousePosition.x, (float)Screen.height - Input.mousePosition.y);
				foreach (Rect rect2 in this.hotSpots)
				{
					bool flag3 = this.TransformSpot(rect2).Contains(vector);
					if (flag3)
					{
						return true;
					}
				}
			}
			return false;
		}

		private Rect TransformSpot(Rect spot)
		{
			int num = Mathf.Max(Screen.width, Screen.height);
			return new Rect(spot.x * (float)num, spot.y * (float)num, spot.height * (float)num, spot.width * (float)num);
		}

		[SerializeField]
		[Tooltip("The hot keys to show the diagnostics bench on device which has keyboard.")]
		private KeyCode[] hotKeys;

		[SerializeField]
		[Tooltip("The hot spots with thrice click to show the diagnostics bench on device which has touch screen.")]
		private Rect[] hotSpots;

		private float hotSpotClickTime;

		private int hotSpotClickCounter;

		private Texture2D hotSpotTexture;
	}
}
