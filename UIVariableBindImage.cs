﻿using System;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Image")]
	[RequireComponent(typeof(Image))]
	public sealed class UIVariableBindImage : UIVariableBind
	{
		public override void BindVariables()
		{
			Assert.IsNull<UIVariable>(this.spriteVariable);
			Assert.IsNull<UIVariable>(this.fillAmountVariable);
			bool flag = !string.IsNullOrEmpty(this.spriteBind);
			if (flag)
			{
				this.spriteVariable = base.FindVariable(this.spriteBind);
				bool flag2 = this.spriteVariable == null;
				if (flag2)
				{
					UIVariableBindImage.logger.LogWarning("{0} can not find a variable {1}", new object[] { base.name, this.spriteBind });
				}
				else
				{
					this.spriteVariable.OnValueInitialized += this.OnSpriteChanged;
					this.spriteVariable.OnValueChanged += this.OnSpriteChanged;
					this.OnSpriteChanged();
				}
			}
			bool flag3 = !string.IsNullOrEmpty(this.fillAmountBind);
			if (flag3)
			{
				this.fillAmountVariable = base.FindVariable(this.fillAmountBind);
				bool flag4 = this.fillAmountVariable == null;
				if (flag4)
				{
					UIVariableBindImage.logger.LogWarning("{0} can not find a variable {1}", new object[] { base.name, this.spriteBind });
				}
				else
				{
					this.fillAmountVariable.OnValueInitialized += this.OnFillAmountChanged;
					this.fillAmountVariable.OnValueChanged += this.OnFillAmountChanged;
					this.OnFillAmountChanged();
				}
			}
		}

		public override void UnbindVariables()
		{
			bool flag = this.spriteVariable != null;
			if (flag)
			{
				this.spriteVariable.OnValueInitialized -= this.OnSpriteChanged;
				this.spriteVariable.OnValueChanged -= this.OnSpriteChanged;
				this.spriteVariable = null;
			}
			bool flag2 = this.fillAmountVariable != null;
			if (flag2)
			{
				this.fillAmountVariable.OnValueInitialized -= this.OnFillAmountChanged;
				this.fillAmountVariable.OnValueChanged -= this.OnFillAmountChanged;
				this.fillAmountVariable = null;
			}
		}

		private void OnSpriteChanged()
		{
			bool flag = this.image == null;
			if (flag)
			{
				this.image = base.GetComponent<Image>();
			}
			bool flag2 = this.image != null;
			if (flag2)
			{
				AssetID asset = this.spriteVariable.GetAsset();
				this.ChangeSprite(asset);
			}
		}

		private void OnFillAmountChanged()
		{
			bool flag = this.image == null;
			if (flag)
			{
				this.image = base.GetComponent<Image>();
			}
			float @float = this.fillAmountVariable.GetFloat();
			this.image.fillAmount = @float;
		}

		private void ChangeSprite(AssetID assetID)
		{
			bool flag = this.spriteAsset.Equals(assetID);
			if (flag)
			{
				this.CheckEnable(this.image);
			}
			else
			{
				this.spriteAsset = assetID;
				bool isEmpty = assetID.IsEmpty;
				if (isEmpty)
				{
					this.image.sprite = null;
					this.CheckEnable(this.image);
				}
				else
				{
					this.LoadFromPool(assetID);
				}
			}
		}

		private void LoadFromPool(AssetID assetID)
		{
			Singleton<SpritePool>.Instance.Load(assetID, delegate(Sprite sprite)
			{
				bool flag = null != sprite;
				if (flag)
				{
					Singleton<SpritePool>.Instance.Free(sprite, false);
				}
				bool flag2 = this.image == null;
				if (!flag2)
				{
					bool flag3 = sprite == null;
					if (flag3)
					{
						string text = string.Format("Load sprite {0} failed.", assetID);
						Debug.LogError(text, this.gameObject);
					}
					bool flag4 = this.spriteAsset.Equals(assetID);
					if (flag4)
					{
						Singleton<SpritePool>.Instance.SetImageSprite(this.image, sprite);
						bool flag5 = this.autoFitNativeSize;
						if (flag5)
						{
							this.image.SetNativeSize();
						}
						this.CheckEnable(this.image);
					}
				}
			}, false);
		}

		private void CheckEnable(Image image)
		{
			bool flag = this.autoDisable;
			if (flag)
			{
				image.enabled = image.sprite != null;
			}
		}

		private new void Awake()
		{
			base.Awake();
			this.image = base.GetComponent<Image>();
			this.CheckEnable(this.image);
		}

		private static Logger logger = LogSystem.GetLogger("UIVariableBindImage");

		[SerializeField]
		[VariableName(UIVariableType.Asset)]
		public string spriteBind;

		[SerializeField]
		[VariableName(UIVariableType.Float)]
		public string fillAmountBind;

		[SerializeField]
		public bool autoFitNativeSize = false;

		[SerializeField]
		public bool autoDisable = true;

		private Image image;

		private AssetID spriteAsset;

		private UIVariable spriteVariable;

		private UIVariable fillAmountVariable;
	}
}
