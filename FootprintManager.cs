﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class FootprintManager : Singleton<FootprintManager>
	{
		public void AddFootprint(Vector3 pos, Vector3 fwd, Vector3 rht, Footprint footprint)
		{
			Material material = footprint.Material;
			FootprintMesh footprintMesh;
			bool flag = !this.footprintMeshes.TryGetValue(material, out footprintMesh);
			if (flag)
			{
				GameObject gameObject = new GameObject("Footprint: " + material.name);
				footprintMesh = gameObject.AddComponent<FootprintMesh>();
				footprintMesh.Initialize(64, material);
				this.footprintMeshes.Add(material, footprintMesh);
			}
			footprintMesh.AddFootprint(pos, fwd, rht, footprint);
		}

		private Dictionary<Material, FootprintMesh> footprintMeshes = new Dictionary<Material, FootprintMesh>();
	}
}
