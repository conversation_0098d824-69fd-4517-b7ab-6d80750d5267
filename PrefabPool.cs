﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class PrefabPool : Singleton<PrefabPool>
	{
		public PrefabPool()
		{
			this.sweepChecker = delegate(AssetID assetID, PrefabCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						bool flag4 = this.unloadedCountInTime < 5 && Time.time > cache.LastFreeTime + cache.ReleaseAfterFree;
						if (flag4)
						{
							this.unloadedCountInTime++;
							GameObject @object = cache.GetObject();
							bool flag5 = @object != null;
							if (flag5)
							{
								this.lookup.Remove(@object);
								AssetManager.UnloadAsseBundle(assetID.BundleName);
							}
							flag2 = true;
						}
						else
						{
							flag2 = false;
						}
					}
				}
				return flag2;
			};
			Scheduler.AddFrameListener(new Action(this.Update));
		}

		public void SetMaxLoadingCount(int value)
		{
			this.maxLoadingCount = value;
		}

		public float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				foreach (KeyValuePair<AssetID, PrefabCache> keyValuePair in this.caches)
				{
					keyValuePair.Value.DefaultReleaseAfterFree = value;
				}
			}
		}

		private void Update()
		{
			this.QueueLoadPrefab();
			bool flag = Time.time - this.lastCheckSweepTime >= 1f;
			if (flag)
			{
				this.lastCheckSweepTime = Time.time;
				this.unloadedCountInTime = 0;
				this.caches.RemoveAll(this.sweepChecker);
			}
		}

		private void QueueLoadPrefab()
		{
			int num = this.maxLoadingCount + this.loadQueue.Count / 5;
			while (this.loadQueue.Count > 0 && num - this.loadingCount > 0)
			{
				this.loadingCount++;
				PrefabLoadItem prefabLoadItem = this.loadQueue.Dequeue();
				Scheduler.RunCoroutine(this.LoadAsyncImplInQueueLoad(prefabLoadItem.assetId, prefabLoadItem.complete));
			}
		}

		public GameObject Instantiate(GameObject prefab)
		{
			bool flag = prefab == null;
			GameObject gameObject;
			if (flag)
			{
				PrefabPool.logger.LogError("Try to Instantiate a null Prefab.");
				gameObject = null;
			}
			else
			{
				GameObject gameObject2 = Object.Instantiate<GameObject>(prefab);
				PrefabReference prefabReference = ReferenceDict.AddPrefabReference(gameObject2, prefab);
				PrefabReferenceHolder orAddComponent = gameObject2.GetOrAddComponent<PrefabReferenceHolder>();
				orAddComponent.SetPrefabReference(prefabReference);
				gameObject = gameObject2;
			}
			return gameObject;
		}

		public bool Retain(GameObject prefab)
		{
			bool flag = prefab == null;
			bool flag2;
			if (flag)
			{
				PrefabPool.logger.LogError("Try to Retain a null Prefab.");
				flag2 = false;
			}
			else
			{
				PrefabCache prefabCache;
				bool flag3 = !this.lookup.TryGetValue(prefab, out prefabCache);
				if (flag3)
				{
					flag2 = false;
				}
				else
				{
					prefabCache.Retain();
					flag2 = true;
				}
			}
			return flag2;
		}

		public void Free(GameObject prefab, bool destroy = false)
		{
			bool flag = prefab == null;
			if (flag)
			{
				PrefabPool.logger.LogError("Try to free a null Prefab.");
			}
			else
			{
				PrefabCache prefabCache;
				bool flag2 = !this.lookup.TryGetValue(prefab, out prefabCache);
				if (flag2)
				{
					PrefabPool.logger.LogWarning("Try to free an instance {0} not allocated by this pool.", new object[] { prefab.name });
				}
				else
				{
					prefabCache.Release();
					bool flag3 = destroy && prefabCache.ReferenceCount == 0;
					if (flag3)
					{
						AssetID assetID = prefabCache.AssetID;
						GameObject @object = prefabCache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						this.caches.Remove(assetID);
					}
				}
			}
		}

		public void Clear()
		{
			this.caches.Clear();
			this.lookup.Clear();
		}

		public void ClearAllUnused()
		{
			this.caches.RemoveAll(delegate(AssetID assetID, PrefabCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						GameObject @object = cache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						flag2 = true;
					}
				}
				return flag2;
			});
		}

		public void Load(AssetID assetID, Action<GameObject> complete, bool isSync = false)
		{
			bool flag = !assetID.AssetName.EndsWith(".prefab");
			if (flag)
			{
				assetID.AssetName += ".prefab";
			}
			assetID.AssetName = assetID.AssetName.ToLower();
			if (isSync)
			{
				Scheduler.RunCoroutine(this.LoadSyncImpl(assetID, complete));
			}
			else
			{
				bool flag2 = !AssetManager.IsVersionCached(assetID.BundleName);
				if (flag2)
				{
					Scheduler.RunCoroutine(this.LoadAsyncImpl(assetID, complete));
				}
				else
				{
					PrefabLoadItem prefabLoadItem = new PrefabLoadItem(assetID, complete);
					this.loadQueue.Enqueue(prefabLoadItem);
				}
			}
		}

		private IEnumerator LoadAsyncImpl(AssetID assetID, Action<GameObject> complete)
		{
			WaitLoadPrefab waitLoad = this.InternalLoad(assetID, false);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			bool flag = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag)
			{
				PrefabPool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private IEnumerator LoadAsyncImplInQueueLoad(AssetID assetID, Action<GameObject> complete)
		{
			WaitLoadPrefab waitLoad = this.InternalLoad(assetID, false);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			this.loadingCount--;
			bool flag = this.loadingCount < 0;
			if (flag)
			{
				Debug.LogError("[PrefabPool] loadingCount is occur error " + this.loadingCount.ToString());
			}
			bool flag2 = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag2)
			{
				PrefabPool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private IEnumerator LoadSyncImpl(AssetID assetID, Action<GameObject> complete)
		{
			WaitLoadPrefab waitLoad = this.InternalLoad(assetID, true);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			bool flag = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag)
			{
				PrefabPool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private WaitLoadPrefab InternalLoad(AssetID assetID, bool isSync)
		{
			PrefabCache prefabCache;
			bool flag = this.caches.TryGetValue(assetID, out prefabCache);
			WaitLoadPrefab waitLoadPrefab;
			if (flag)
			{
				prefabCache.Retain();
				waitLoadPrefab = new WaitLoadPrefab(prefabCache);
			}
			else
			{
				prefabCache = new PrefabCache(assetID, this.lookup);
				prefabCache.DefaultReleaseAfterFree = this.DefaultReleaseAfterFree;
				prefabCache.IsSync = isSync;
				prefabCache.LoadObject(assetID);
				prefabCache.Retain();
				this.caches.Add(assetID, prefabCache);
				waitLoadPrefab = new WaitLoadPrefab(prefabCache);
			}
			return waitLoadPrefab;
		}

		private static Logger logger = LogSystem.GetLogger("PrefabPool");

		private Dictionary<AssetID, PrefabCache> caches = new Dictionary<AssetID, PrefabCache>();

		private Dictionary<GameObject, PrefabCache> lookup = new Dictionary<GameObject, PrefabCache>();

		private float defaultReleaseAfterFree = 30f;

		private Func<AssetID, PrefabCache, bool> sweepChecker;

		private int unloadedCountInTime = 0;

		private float lastCheckSweepTime = 0f;

		private Queue<PrefabLoadItem> loadQueue = new Queue<PrefabLoadItem>();

		private int maxLoadingCount = 1;

		private int loadingCount = 0;
	}
}
