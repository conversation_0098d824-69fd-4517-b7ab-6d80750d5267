﻿using System;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/Toggle Color")]
	[RequireComponent(typeof(Toggle))]
	public sealed class ToggleColor : MonoBehaviour
	{
		private void Awake()
		{
			this.toggle = base.GetComponent<Toggle>();
			Assert.IsNotNull<Toggle>(this.toggle);
			bool flag = this.graphic != null;
			if (flag)
			{
				this.color = this.graphic.color;
			}
			this.OnValueChanged(this.toggle.isOn);
			this.toggle.onValueChanged.AddListener(new UnityAction<bool>(this.OnValueChanged));
		}

		private void OnEnable()
		{
			this.OnValueChanged(this.toggle.isOn);
		}

		private void OnDestroy()
		{
			bool flag = this.toggle != null;
			if (flag)
			{
				this.toggle.onValueChanged.RemoveListener(new UnityAction<bool>(this.OnValueChanged));
			}
		}

		private void OnValueChanged(bool isOn)
		{
			bool flag = this.graphic == null;
			if (!flag)
			{
				bool flag2 = this.transitTime > 0f;
				if (flag2)
				{
					this.colorFrom = this.graphic.color;
					this.colorTo = (isOn ? this.toggledColor : this.color);
					this.leftTime = this.transitTime;
					bool flag3 = this.autoHide;
					if (flag3)
					{
						this.graphic.enabled = true;
					}
				}
				else if (isOn)
				{
					this.graphic.color = this.toggledColor;
					bool flag4 = this.autoHide;
					if (flag4)
					{
						this.graphic.enabled = true;
					}
				}
				else
				{
					this.graphic.color = this.color;
					bool flag5 = this.autoHide;
					if (flag5)
					{
						bool flag6 = Mathf.Approximately(this.graphic.color.a, 0f);
						if (flag6)
						{
							this.graphic.enabled = false;
						}
					}
				}
			}
		}

		private void Update()
		{
			bool flag = this.graphic == null;
			if (!flag)
			{
				bool flag2 = this.leftTime > 0f;
				if (flag2)
				{
					this.leftTime -= Time.deltaTime;
					bool flag3 = this.leftTime <= 0f;
					if (flag3)
					{
						this.graphic.color = this.colorTo;
						bool flag4 = this.autoHide;
						if (flag4)
						{
							bool flag5 = Mathf.Approximately(this.graphic.color.a, 0f);
							if (flag5)
							{
								this.graphic.enabled = false;
							}
						}
					}
					else
					{
						this.graphic.color = Color.Lerp(this.colorFrom, this.colorTo, 1f - this.leftTime / this.transitTime);
					}
				}
			}
		}

		[SerializeField]
		[Tooltip("The graphic target to change color.")]
		private Graphic graphic;

		[SerializeField]
		[Tooltip("The toggled color.")]
		private Color toggledColor = Color.white;

		[SerializeField]
		[Tooltip("The transit time.")]
		private float transitTime = 0.1f;

		[SerializeField]
		[Tooltip("Whether auto hide the target when the alpha is zero.")]
		private bool autoHide = true;

		private Toggle toggle;

		private Color color;

		private Color colorFrom;

		private Color colorTo;

		private float leftTime = -1f;
	}
}
