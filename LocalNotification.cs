﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class LocalNotification
	{
		public static void SendNotification(int id, long triggerAtMillis, string title, string message)
		{
			LocalNotification.GetLocalNotification().CallStatic("SetNotification", new object[] { id, triggerAtMillis, title, message, message });
		}

		public static void SendRepeatingNotification(int id, long triggerAtMillis, LocalNotification.CalendarUnit repeatInterval, string title, string message)
		{
			long num = 0L;
			switch (repeatInterval)
			{
			case LocalNotification.CalendarUnit.Era:
				num = 3153600000000L;
				break;
			case LocalNotification.CalendarUnit.Year:
				num = 31536000000L;
				break;
			case LocalNotification.CalendarUnit.Month:
				num = -1702967296L;
				break;
			case LocalNotification.CalendarUnit.Day:
				num = 86400000L;
				break;
			case LocalNotification.CalendarUnit.Hour:
				num = 3600000L;
				break;
			case LocalNotification.CalendarUnit.Minute:
				num = 60000L;
				break;
			case LocalNotification.CalendarUnit.Second:
				num = 1000L;
				break;
			case LocalNotification.CalendarUnit.Week:
				num = 604800000L;
				break;
			case LocalNotification.CalendarUnit.Quarter:
				num = 900000L;
				break;
			}
			LocalNotification.GetLocalNotification().CallStatic("SetRepeatingNotification", new object[] { id, triggerAtMillis, num, title, message, message });
		}

		public static void CancelNotification(int id)
		{
			LocalNotification.GetLocalNotification().CallStatic("CancelNotification", new object[] { id });
		}

		public static void CancelAllNotifications(int maxID = 50)
		{
			LocalNotification.GetLocalNotification().CallStatic("CancelAllNotifications", new object[] { maxID });
		}

		private static AndroidJavaClass GetLocalNotification()
		{
			bool flag = LocalNotification.localNotification == null;
			if (flag)
			{
				LocalNotification.localNotification = new AndroidJavaClass("com.winunet.and.LocalReporter");
			}
			return LocalNotification.localNotification;
		}

		private static AndroidJavaClass localNotification;

		public enum CalendarUnit
		{
			Era,
			Year,
			Month,
			Day,
			Hour,
			Minute,
			Second,
			Week,
			Quarter
		}
	}
}
