﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class SpriteCache
	{
		internal SpriteCache(AssetID assetID, IDictionary<Sprite, SpriteCache> lookup)
		{
			this.assetID = assetID;
			this.lookup = lookup;
		}

		public AssetID AssetID
		{
			get
			{
				return this.assetID;
			}
		}

		public int ReferenceCount
		{
			get
			{
				return this.referenceCount;
			}
		}

		public float LastFreeTime
		{
			get
			{
				return this.lastFreeTime;
			}
		}

		public float ReleaseAfterFree
		{
			get
			{
				return this.releaseAfterFree;
			}
		}

		public bool IsSync
		{
			get
			{
				return this.isSync;
			}
			set
			{
				this.isSync = value;
			}
		}

		public string Error { get; private set; }

		internal float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				this.releaseAfterFree = this.defaultReleaseAfterFree;
			}
		}

		internal void Retain()
		{
			this.referenceCount++;
		}

		internal void Release()
		{
			this.referenceCount--;
			this.lastFreeTime = Time.time;
			bool flag = this.referenceCount < 0;
			if (flag)
			{
				Debug.LogErrorFormat("[SpriteCache] referenceCount is error {0} {1}", new object[]
				{
					this.assetID.ToString(),
					this.referenceCount
				});
			}
		}

		internal void LoadObject(AssetID assetID)
		{
			Scheduler.RunCoroutine(this.LoadObjectImpl(assetID));
		}

		internal bool HasLoaded()
		{
			return this.cachedObject != null;
		}

		internal Sprite GetObject()
		{
			return this.cachedObject;
		}

		private IEnumerator LoadObjectImpl(AssetID assetID)
		{
			WaitLoadObject waitobj = null;
			bool flag = this.isSync;
			if (flag)
			{
				waitobj = AssetManager.LoadObjectSync(assetID, typeof(Sprite));
			}
			else
			{
				waitobj = AssetManager.LoadObject(assetID, typeof(Sprite));
			}
			yield return waitobj;
			bool flag2 = !string.IsNullOrEmpty(waitobj.Error);
			if (flag2)
			{
				this.Error = waitobj.Error;
				yield break;
			}
			this.cachedObject = waitobj.GetObject() as Sprite;
			bool flag3 = this.cachedObject == null;
			if (flag3)
			{
				this.Error = string.Format("The asset {0} is not a Sprite.", assetID);
				yield break;
			}
			this.releaseAfterFree = this.DefaultReleaseAfterFree;
			bool flag4 = this.lookup.ContainsKey(this.cachedObject);
			if (flag4)
			{
				SpriteCache.logger.LogWarning("The sprite {0} has been loaded.", new object[] { assetID });
				this.lookup[this.cachedObject] = this;
			}
			else
			{
				this.lookup.Add(this.cachedObject, this);
			}
			yield break;
		}

		private static Logger logger = LogSystem.GetLogger("SpriteCache");

		private Sprite cachedObject;

		private int referenceCount;

		private AssetID assetID;

		private IDictionary<Sprite, SpriteCache> lookup;

		private float lastFreeTime = -1f;

		private float defaultReleaseAfterFree = 30f;

		private float releaseAfterFree;

		private bool isSync;
	}
}
