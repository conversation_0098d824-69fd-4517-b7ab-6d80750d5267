﻿using System;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	public abstract class UIVariableBindBool : UIVariableBind
	{
		protected bool GetResult()
		{
			bool flag = this.variables == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = this.booleanLogic == UIVariableBindBool.BooleanLogic.And;
				if (flag3)
				{
					bool flag4 = true;
					foreach (UIVariableBindBool.VariableRef variableRef in this.variables)
					{
						bool flag5 = variableRef != null;
						if (flag5)
						{
							flag4 = flag4 && variableRef.GetResult();
						}
					}
					flag2 = flag4;
				}
				else
				{
					bool flag6 = false;
					foreach (UIVariableBindBool.VariableRef variableRef2 in this.variables)
					{
						bool flag7 = variableRef2 != null;
						if (flag7)
						{
							flag6 = flag6 || variableRef2.GetResult();
						}
					}
					flag2 = flag6;
				}
			}
			return flag2;
		}

		protected abstract void OnValueChanged();

		public override void BindVariables()
		{
			bool flag = this.variables != null;
			if (flag)
			{
				foreach (UIVariableBindBool.VariableRef variableRef in this.variables)
				{
					Assert.IsNull<UIVariable>(variableRef.Variable);
					bool flag2 = string.IsNullOrEmpty(variableRef.VariableName);
					if (!flag2)
					{
						variableRef.Variable = base.FindVariable(variableRef.VariableName);
						bool flag3 = variableRef.Variable == null;
						if (flag3)
						{
							UIVariableBindBool.logger.LogWarning("{0} can not find a variable {1}", new object[] { base.name, variableRef.VariableName });
						}
						else
						{
							variableRef.Variable.OnValueInitialized += this.OnValueChanged;
							variableRef.Variable.OnValueChanged += this.OnValueChanged;
						}
					}
				}
			}
			this.OnValueChanged();
		}

		public override void UnbindVariables()
		{
			bool flag = this.variables != null;
			if (flag)
			{
				foreach (UIVariableBindBool.VariableRef variableRef in this.variables)
				{
					bool flag2 = variableRef.Variable != null;
					if (flag2)
					{
						variableRef.Variable.OnValueInitialized -= this.OnValueChanged;
						variableRef.Variable.OnValueChanged -= this.OnValueChanged;
						variableRef.Variable = null;
					}
				}
			}
		}

		private static Logger logger = LogSystem.GetLogger("UIVariableBindBool");

		[SerializeField]
		[Tooltip("The boolean logic.")]
		public UIVariableBindBool.BooleanLogic booleanLogic = UIVariableBindBool.BooleanLogic.And;

		[SerializeField]
		[Tooltip("The variables for calculate the boolean value.")]
		public UIVariableBindBool.VariableRef[] variables;

		public enum BooleanLogic
		{
			And,
			Or
		}

		public enum CompareModeEnum
		{
			Less,
			LessEqual,
			Equal,
			Great,
			GreatEqual
		}

		[Serializable]
		public class VariableRef
		{
			public string VariableName
			{
				get
				{
					return this.variableName;
				}
			}

			public UIVariable Variable { get; set; }

			public bool GetResult()
			{
				bool flag = this.Variable == null;
				bool flag2;
				if (flag)
				{
					flag2 = false;
				}
				else
				{
					bool flag3 = this.Variable.Type == UIVariableType.Boolean;
					if (flag3)
					{
						bool flag4 = this.Variable.GetBoolean();
						bool flag5 = this.reverse;
						if (flag5)
						{
							flag4 = !flag4;
						}
						flag2 = flag4;
					}
					else
					{
						bool flag6 = this.Variable.Type == UIVariableType.Integer;
						if (flag6)
						{
							int integer = this.Variable.GetInteger();
							bool flag7 = false;
							switch (this.compareMode)
							{
							case UIVariableBindBool.CompareModeEnum.Less:
								flag7 = integer < this.referenceInt;
								break;
							case UIVariableBindBool.CompareModeEnum.LessEqual:
								flag7 = integer <= this.referenceInt;
								break;
							case UIVariableBindBool.CompareModeEnum.Equal:
								flag7 = integer == this.referenceInt;
								break;
							case UIVariableBindBool.CompareModeEnum.Great:
								flag7 = integer > this.referenceInt;
								break;
							case UIVariableBindBool.CompareModeEnum.GreatEqual:
								flag7 = integer >= this.referenceInt;
								break;
							}
							bool flag8 = this.reverse;
							if (flag8)
							{
								flag7 = !flag7;
							}
							flag2 = flag7;
						}
						else
						{
							bool flag9 = this.Variable.Type == UIVariableType.Float;
							if (flag9)
							{
								float @float = this.Variable.GetFloat();
								bool flag10 = false;
								switch (this.compareMode)
								{
								case UIVariableBindBool.CompareModeEnum.Less:
									flag10 = @float < this.referenceFloat;
									break;
								case UIVariableBindBool.CompareModeEnum.LessEqual:
									flag10 = @float <= this.referenceFloat;
									break;
								case UIVariableBindBool.CompareModeEnum.Equal:
									flag10 = Mathf.Approximately(@float, this.referenceFloat);
									break;
								case UIVariableBindBool.CompareModeEnum.Great:
									flag10 = @float > this.referenceFloat;
									break;
								case UIVariableBindBool.CompareModeEnum.GreatEqual:
									flag10 = @float >= this.referenceFloat;
									break;
								}
								bool flag11 = this.reverse;
								if (flag11)
								{
									flag10 = !flag10;
								}
								flag2 = flag10;
							}
							else
							{
								UIVariableBindBool.VariableRef.logger.LogError("Variable {0} type is {1}, does not support this variable type.", new object[]
								{
									this.Variable.Name,
									this.Variable.Type
								});
								flag2 = false;
							}
						}
					}
				}
				return flag2;
			}

			private static Logger logger = LogSystem.GetLogger("VariableRef");

			[SerializeField]
			[VariableName(UIVariableType.Boolean, UIVariableType.Integer, UIVariableType.Float)]
			private string variableName;

			[SerializeField]
			private UIVariableBindBool.CompareModeEnum compareMode = UIVariableBindBool.CompareModeEnum.Equal;

			[SerializeField]
			private int referenceInt = 0;

			[SerializeField]
			private float referenceFloat = 0f;

			[SerializeField]
			public bool reverse = false;
		}
	}
}
