﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	[ExecuteInEditMode]
	[DisallowMultipleComponent]
	public sealed class AudioManager : UnitySingleton<AudioManager>
	{
		public static IAudioPlayer Play(AudioItem item)
		{
			return AudioManager.Play(item, Vector3.zero);
		}

		public static IAudioPlayer Play(AudioItem item, Vector3 position)
		{
			Assert.IsNotNull<AudioItem>(item);
			bool flag = UnitySingleton<AudioManager>.Instance == null || item == null;
			IAudioPlayer audioPlayer;
			if (flag)
			{
				audioPlayer = AudioDummyController.Default;
			}
			else
			{
				IAudioController audioController = item.Play(UnitySingleton<AudioManager>.Instance.sourcePool);
				audioController.SetPosition(position);
				audioController.Play();
				UnitySingleton<AudioManager>.Instance.controllers.Add(audioController);
				audioPlayer = audioController;
			}
			return audioPlayer;
		}

		public static IAudioPlayer Play(AudioItem item, Transform trans)
		{
			Assert.IsNotNull<AudioItem>(item);
			bool flag = UnitySingleton<AudioManager>.Instance == null || item == null;
			IAudioPlayer audioPlayer;
			if (flag)
			{
				audioPlayer = AudioDummyController.Default;
			}
			else
			{
				IAudioController audioController = item.Play(UnitySingleton<AudioManager>.Instance.sourcePool);
				audioController.SetTransform(trans);
				audioController.Play();
				UnitySingleton<AudioManager>.Instance.controllers.Add(audioController);
				audioPlayer = audioController;
			}
			return audioPlayer;
		}

		public static void PlayAndForget(AudioItem item)
		{
			AudioManager.PlayAndForget(item, Vector3.zero);
		}

		public static void PlayAndForget(string bundle, string asset)
		{
			AudioManager.PlayAndForget(new AssetID(bundle, asset));
		}

		public static void PlayAndForget(AssetID assetID)
		{
			bool flag = UnitySingleton<AudioManager>.Instance == null;
			if (!flag)
			{
				UnitySingleton<AudioManager>.Instance.StartCoroutine(AudioManager.PlayAndForgetImpl(assetID));
			}
		}

		public static void PlayAndForget(AudioItem item, Vector3 position)
		{
			Assert.IsNotNull<AudioItem>(item);
			bool flag = UnitySingleton<AudioManager>.Instance == null || item == null;
			if (!flag)
			{
				IAudioController audioController = item.Play(UnitySingleton<AudioManager>.Instance.sourcePool);
				audioController.SetPosition(position);
				audioController.Play();
				UnitySingleton<AudioManager>.Instance.controllers.Add(audioController);
			}
		}

		public static void PlayAndForget(string bundle, string asset, Vector3 position)
		{
			AudioManager.PlayAndForget(new AssetID(bundle, asset), position);
		}

		public static void PlayAndForget(AssetID assetID, Vector3 position)
		{
			bool flag = UnitySingleton<AudioManager>.Instance == null;
			if (!flag)
			{
				UnitySingleton<AudioManager>.Instance.StartCoroutine(AudioManager.PlayAndForgetImpl(assetID, position));
			}
		}

		public static void PlayAndForget(AudioItem item, Transform trans)
		{
			Assert.IsNotNull<AudioItem>(item);
			bool flag = UnitySingleton<AudioManager>.Instance == null || item == null;
			if (!flag)
			{
				IAudioController audioController = item.Play(UnitySingleton<AudioManager>.Instance.sourcePool);
				audioController.SetTransform(trans);
				audioController.Play();
				UnitySingleton<AudioManager>.Instance.controllers.Add(audioController);
			}
		}

		public static void PlayAndForget(string bundle, string asset, Transform trans)
		{
			AudioManager.PlayAndForget(new AssetID(bundle, asset), trans);
		}

		public static void PlayAndForget(AssetID assetID, Transform trans)
		{
			bool flag = UnitySingleton<AudioManager>.Instance == null;
			if (!flag)
			{
				UnitySingleton<AudioManager>.Instance.StartCoroutine(AudioManager.PlayAndForgetImpl(assetID, trans));
			}
		}

		public static void StopAll()
		{
			bool flag = UnitySingleton<AudioManager>.Instance == null;
			if (!flag)
			{
				UnitySingleton<AudioManager>.Instance.StopAllControllers();
			}
		}

		private static IEnumerator PlayAndForgetImpl(AssetID assetID)
		{
			WaitLoadScriptable item = Singleton<ScriptablePool>.Instance.Load(assetID);
			yield return item;
			bool flag = !string.IsNullOrEmpty(item.Error);
			if (flag)
			{
				AudioManager.logger.LogError("PlayAndForget {0} failed: {1}", new object[] { assetID, item.Error });
				yield break;
			}
			AudioItem audioItem = item.LoadedObject as AudioItem;
			AudioManager.PlayAndForget(audioItem);
			Singleton<ScriptablePool>.Instance.Free(audioItem, false);
			yield break;
		}

		private static IEnumerator PlayAndForgetImpl(AssetID assetID, Vector3 position)
		{
			WaitLoadScriptable item = Singleton<ScriptablePool>.Instance.Load(assetID);
			yield return item;
			bool flag = !string.IsNullOrEmpty(item.Error);
			if (flag)
			{
				AudioManager.logger.LogError("PlayAndForget {0} failed: {1}", new object[] { assetID, item.Error });
				yield break;
			}
			AudioItem audioItem = item.LoadedObject as AudioItem;
			AudioManager.PlayAndForget(audioItem, position);
			Singleton<ScriptablePool>.Instance.Free(audioItem, false);
			yield break;
		}

		private static IEnumerator PlayAndForgetImpl(AssetID assetID, Transform trans)
		{
			WaitLoadScriptable item = Singleton<ScriptablePool>.Instance.Load(assetID);
			yield return item;
			bool flag = !string.IsNullOrEmpty(item.Error);
			if (flag)
			{
				AudioManager.logger.LogError("PlayAndForget {0} failed: {1}", new object[] { assetID, item.Error });
				yield break;
			}
			AudioItem audioItem = item.LoadedObject as AudioItem;
			AudioManager.PlayAndForget(audioItem, trans);
			Singleton<ScriptablePool>.Instance.Free(audioItem, false);
			yield break;
		}

		private void StopAllControllers()
		{
			foreach (IAudioController audioController in this.controllers)
			{
				audioController.Stop();
			}
		}

		private void Awake()
		{
			Assert.IsNull<AudioSourcePool>(this.sourcePool);
			GameObject gameObject = new GameObject("Audio Source Pool");
			gameObject.transform.parent = base.transform;
			this.sourcePool = new AudioSourcePool(base.transform, gameObject.transform);
			Object.DontDestroyOnLoad(base.gameObject);
		}

		private void Update()
		{
			this.controllers.RemoveAll(delegate(IAudioController controller)
			{
				bool isPlaying = controller.IsPlaying;
				bool flag;
				if (isPlaying)
				{
					controller.Update();
					flag = false;
				}
				else
				{
					controller.FinshAudio();
					flag = true;
				}
				return flag;
			});
		}

		private new void OnDestroy()
		{
			this.StopAllControllers();
			base.OnDestroy();
		}

		private static Logger logger = LogSystem.GetLogger("AudioManager");

		private AudioSourcePool sourcePool;

		private List<IAudioController> controllers = new List<IAudioController>();
	}
}
