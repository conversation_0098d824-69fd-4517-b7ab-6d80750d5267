﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/UI Variable Table")]
	public sealed class UIVariableTable : MonoBehaviour
	{
		private Dictionary<string, UIVariable> Lookup
		{
			get
			{
				bool flag = this.lookup == null;
				if (flag)
				{
					this.lookup = new Dictionary<string, UIVariable>(StringComparer.Ordinal);
					bool flag2 = this.variables != null;
					if (flag2)
					{
						foreach (UIVariable uivariable in this.variables)
						{
							this.lookup.Add(uivariable.Name, uivariable);
						}
					}
				}
				return this.lookup;
			}
		}

		public UIVariable FindVariable(string name)
		{
			UIVariable uivariable;
			bool flag = this.Lookup.TryGetValue(name, out uivariable);
			UIVariable uivariable2;
			if (flag)
			{
				uivariable2 = uivariable;
			}
			else
			{
				uivariable2 = null;
			}
			return uivariable2;
		}

		private static void InternalInitializeBinds(Transform transform)
		{
			UIVariableBind[] components = transform.GetComponents<UIVariableBind>();
			foreach (UIVariableBind uivariableBind in components)
			{
				uivariableBind.Initialize();
			}
			foreach (object obj in transform)
			{
				Transform transform2 = (Transform)obj;
				UIVariableTable.InitializeBindsDeep(transform2);
			}
		}

		private static void InitializeBindsDeep(Transform transform)
		{
			bool flag = transform.HasComponent<UIVariableTable>();
			if (!flag)
			{
				UIVariableBind[] components = transform.GetComponents<UIVariableBind>();
				foreach (UIVariableBind uivariableBind in components)
				{
					uivariableBind.Initialize();
				}
				foreach (object obj in transform)
				{
					Transform transform2 = (Transform)obj;
					UIVariableTable.InitializeBindsDeep(transform2);
				}
			}
		}

		private void Awake()
		{
			UIVariableTable.InternalInitializeBinds(base.transform);
		}

		[SerializeField]
		public UIVariable[] variables;

		private Dictionary<string, UIVariable> lookup;
	}
}
