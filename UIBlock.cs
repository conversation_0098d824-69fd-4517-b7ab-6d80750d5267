﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[RequireComponent(typeof(CanvasRenderer))]
	public sealed class UIBlock : Graphic, ICanvasRaycastFilter
	{
		public override Texture mainTexture
		{
			get
			{
				return null;
			}
		}

		public override Material materialForRendering
		{
			get
			{
				return null;
			}
		}

		public bool IsRaycastLocationValid(Vector2 screenPoint, Camera eventCamera)
		{
			return true;
		}

		protected override void OnPopulateMesh(VertexHelper vh)
		{
			vh.Clear();
		}
	}
}
