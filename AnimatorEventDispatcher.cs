﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Animator/Animator Event Dispatcher")]
	[DisallowMultipleComponent]
	public sealed class AnimatorEventDispatcher : MonoBehaviour
	{
		public IEnumerator WaitEvent(string eventName)
		{
			bool finished = false;
			SignalHandle handle = null;
			handle = this.ListenEvent(eventName, delegate(string param, AnimatorStateInfo stateInfo)
			{
				finished = true;
				handle.Dispose();
			});
			return new WaitUntil(() => finished);
		}

		public void WaitEvent(string eventName, Action<string, AnimatorStateInfo> complete)
		{
			Assert.IsNotNull<Action<string, AnimatorStateInfo>>(complete);
			SignalHandle handle = null;
			handle = this.ListenEvent(eventName, delegate(string param, AnimatorStateInfo stateInfo)
			{
				complete(param, stateInfo);
				handle.Dispose();
			});
		}

		public SignalHandle ListenEvent(string eventName, Action<string, AnimatorStateInfo> eventDelegate)
		{
			Signal signal;
			bool flag = !this.eventTable.TryGetValue(eventName, out signal);
			if (flag)
			{
				signal = new Signal();
				this.eventTable.Add(eventName, signal);
			}
			return signal.Add(delegate(object[] args)
			{
				eventDelegate((string)args[0], (AnimatorStateInfo)args[1]);
			});
		}

		internal void DispatchEvent(string eventName, string param, AnimatorStateInfo stateInfo)
		{
			Signal signal;
			bool flag = this.eventTable.TryGetValue(eventName, out signal);
			if (flag)
			{
				signal.Invoke(new object[] { param, stateInfo });
			}
		}

		private Dictionary<string, Signal> eventTable = new Dictionary<string, Signal>(32, StringComparer.Ordinal);
	}
}
