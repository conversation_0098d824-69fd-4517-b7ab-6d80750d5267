﻿using System;
using System.Diagnostics;
using UnityEngine;

namespace Nirvana
{
	public sealed class Logger
	{
		internal Logger(LogDestination destination, string moduleName)
		{
			this.destination = destination;
			this.moduleName = moduleName;
		}

		public void Log(LogSeverity severity, string message)
		{
			LogItem logItem = default(LogItem);
			logItem.ModuleName = this.moduleName;
			logItem.Severity = severity;
			logItem.Message = message;
			this.destination.Log(logItem);
		}

		public void Log(LogSeverity severity, Object context, string message)
		{
			LogItem logItem = default(LogItem);
			logItem.ModuleName = this.moduleName;
			logItem.Severity = severity;
			logItem.Message = message;
			logItem.Context = context;
			this.destination.Log(logItem);
		}

		public void Log(LogSeverity severity, string format, params object[] args)
		{
			this.Log(severity, string.Format(format, args));
		}

		public void Log(LogSeverity severity, Object context, string format, params object[] args)
		{
			this.Log(severity, context, string.Format(format, args));
		}

		[Conditional("DEBUG")]
		public void LogDebug(string message)
		{
			this.Log(LogSeverity.Debug, message);
		}

		[Conditional("DEBUG")]
		public void LogDebug(Object context, string message)
		{
			this.Log(LogSeverity.Debug, context, message);
		}

		[Conditional("DEBUG")]
		public void LogDebug(string format, params object[] args)
		{
			this.Log(LogSeverity.Debug, format, args);
		}

		[Conditional("DEBUG")]
		public void LogDebug(Object context, string format, params object[] args)
		{
			this.Log(LogSeverity.Debug, context, format, args);
		}

		public void LogInfo(string message)
		{
			this.Log(LogSeverity.Info, message);
		}

		public void LogInfo(Object context, string message)
		{
			this.Log(LogSeverity.Info, context, message);
		}

		public void LogInfo(string format, params object[] args)
		{
			this.Log(LogSeverity.Info, format, args);
		}

		public void LogInfo(Object context, string format, params object[] args)
		{
			this.Log(LogSeverity.Info, context, format, args);
		}

		public void LogWarning(string message)
		{
			this.Log(LogSeverity.Warning, message);
		}

		public void LogWarning(Object context, string message)
		{
			this.Log(LogSeverity.Warning, context, message);
		}

		public void LogWarning(string format, params object[] args)
		{
			this.Log(LogSeverity.Warning, format, args);
		}

		public void LogWarning(Object context, string format, params object[] args)
		{
			this.Log(LogSeverity.Warning, context, format, args);
		}

		public void LogError(string message)
		{
			this.Log(LogSeverity.Error, message);
		}

		public void LogError(Object context, string message)
		{
			this.Log(LogSeverity.Error, context, message);
		}

		public void LogError(string format, params object[] args)
		{
			this.Log(LogSeverity.Error, format, args);
		}

		public void LogError(Object context, string format, params object[] args)
		{
			this.Log(LogSeverity.Error, context, format, args);
		}

		public void LogError(Exception e)
		{
			this.Log(LogSeverity.Error, e.ToString());
		}

		public void LogError(Object context, Exception e)
		{
			this.Log(LogSeverity.Error, context, e.ToString());
		}

		private LogDestination destination;

		private string moduleName;
	}
}
