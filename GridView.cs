﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	public sealed class GridView : ListView
	{
		protected override Vector2 CalculateContentSize(int cellCount)
		{
			int num = Mathf.CeilToInt((float)cellCount / (float)this.arrangeCount);
			bool flag = this.scrollDirection == GridView.ScrollDirectionEnum.Horizontal;
			Vector2 vector;
			if (flag)
			{
				float num2 = this.cellSize.x * (float)num;
				float num3 = this.cellSize.y * (float)this.arrangeCount;
				num2 += this.spacing.x * (float)(num - 1);
				num3 += this.spacing.y * (float)(this.arrangeCount - 1);
				vector = new Vector2(num2, num3);
			}
			else
			{
				float num4 = this.cellSize.x * (float)this.arrangeCount;
				float num5 = this.cellSize.y * (float)num;
				num4 += this.spacing.x * (float)(this.arrangeCount - 1);
				num5 += this.spacing.y * (float)(num - 1);
				vector = new Vector2(num4, num5);
			}
			return vector;
		}

		protected override void CalculateCurrentActiveCellRange(Vector2 scrollPosition, out int startIndex, out int endIndex)
		{
			Rect rect = base.ScrollContent.rect;
			Rect rect2 = base.ScrollView.rect;
			bool flag = this.scrollDirection == GridView.ScrollDirectionEnum.Horizontal;
			if (flag)
			{
				float num = rect.width - rect2.width;
				float num2 = scrollPosition.x * num;
				float num3 = num2;
				float num4 = num2 + rect2.width;
				startIndex = this.GetCellIndexAtPositionXStart(num3);
				endIndex = this.GetCellIndexAtPositionXEnd(num4);
			}
			else
			{
				float num5 = rect.height - rect2.height;
				float num6 = scrollPosition.y * num5;
				float num7 = num6 + rect2.height;
				float num8 = num6;
				num7 = rect.height - num7;
				num8 = rect.height - num8;
				startIndex = this.GetCellIndexAtPositionYStart(num7);
				endIndex = this.GetCellIndexAtPositionYEnd(num8);
			}
		}

		protected override void LayoutCell(GameObject cell, int index)
		{
			int num = index / this.arrangeCount;
			bool flag = this.fillDirection == GridView.FillDirectionEnum.Forward;
			int num2;
			if (flag)
			{
				num2 = index % this.arrangeCount;
			}
			else
			{
				num2 = this.arrangeCount - index % this.arrangeCount - 1;
			}
			RectTransform rectTransform = (RectTransform)cell.transform;
			rectTransform.anchorMin = Vector2.zero;
			rectTransform.anchorMax = Vector2.zero;
			rectTransform.sizeDelta = this.cellSize;
			bool flag2 = this.scrollDirection == GridView.ScrollDirectionEnum.Horizontal;
			if (flag2)
			{
				rectTransform.pivot = Vector2.zero;
				rectTransform.anchoredPosition = new Vector2((float)num * (this.cellSize.x + this.spacing.x), (float)num2 * (this.cellSize.y + this.spacing.y));
			}
			else
			{
				ScrollRect scrollRect = base.ScrollRect;
				Vector2 sizeDelta = scrollRect.content.sizeDelta;
				rectTransform.pivot = new Vector2(0f, 1f);
				rectTransform.anchoredPosition = new Vector2((float)num2 * (this.cellSize.x + this.spacing.x), sizeDelta.y - (float)num * (this.cellSize.y + this.spacing.y));
			}
		}

		protected override Vector2 GetCellPositionAtIndex(int index)
		{
			int num = index / this.arrangeCount;
			int num2 = index % this.arrangeCount;
			bool flag = this.scrollDirection == GridView.ScrollDirectionEnum.Horizontal;
			Vector2 vector;
			if (flag)
			{
				vector = new Vector2((float)num * (this.cellSize.x + this.spacing.x), (float)num2 * (this.cellSize.y + this.spacing.y));
			}
			else
			{
				vector = new Vector2((float)num2 * (this.cellSize.x + this.spacing.x), (float)num * (this.cellSize.y + this.spacing.y));
			}
			return vector;
		}

		protected override void UpdateSnapping()
		{
			bool flag = !this.snapping;
			if (!flag)
			{
				RectTransform scrollContent = base.ScrollContent;
				Rect rect = scrollContent.rect;
				Rect rect2 = base.ScrollView.rect;
				Vector2 normalizedPosition = base.ScrollRect.normalizedPosition;
				bool flag2 = this.scrollDirection == GridView.ScrollDirectionEnum.Horizontal;
				if (flag2)
				{
					float num = rect.width - rect2.width;
					float num2 = normalizedPosition.x * num + this.cellSize.x / 2f;
					int num3 = Mathf.RoundToInt((float)this.GetCellIndexAtPositionXStart(num2));
					Vector2 cellPositionAtIndex = this.GetCellPositionAtIndex(num3);
					float num4 = num / 2f - cellPositionAtIndex.x;
					Vector2 anchoredPosition = scrollContent.anchoredPosition;
					float num5 = Mathf.Abs(num4 - anchoredPosition.x);
					bool flag3 = num5 > 0.03f * this.snapSpeed;
					if (flag3)
					{
						float num6 = Mathf.Lerp(anchoredPosition.x, num4, Time.deltaTime * this.snapSpeed);
						scrollContent.anchoredPosition = new Vector2(num6, anchoredPosition.y);
					}
					else
					{
						scrollContent.anchoredPosition = new Vector2(num4, anchoredPosition.y);
					}
				}
				else
				{
					float num7 = rect.height - rect2.height;
					float num8 = normalizedPosition.y * num7 + this.cellSize.y / 2f;
					int num9 = Mathf.RoundToInt((float)this.GetCellIndexAtPositionYStart(num8));
					Vector2 cellPositionAtIndex2 = this.GetCellPositionAtIndex(num9);
					float num10 = num7 / 2f - cellPositionAtIndex2.y;
					Vector2 anchoredPosition2 = scrollContent.anchoredPosition;
					float num11 = Mathf.Abs(num10 - anchoredPosition2.y);
					bool flag4 = num11 > 0.03f * this.snapSpeed;
					if (flag4)
					{
						float num12 = Mathf.Lerp(anchoredPosition2.y, num10, Time.deltaTime * this.snapSpeed);
						scrollContent.anchoredPosition = new Vector2(anchoredPosition2.x, num12);
					}
					else
					{
						scrollContent.anchoredPosition = new Vector2(anchoredPosition2.x, num10);
					}
				}
			}
		}

		private int GetCellIndexAtPositionXStart(float position)
		{
			float num = (position + this.spacing.x) / (this.cellSize.x + this.spacing.x);
			return Mathf.FloorToInt(num) * this.arrangeCount;
		}

		private int GetCellIndexAtPositionXEnd(float position)
		{
			float num = (position - 0.5f * this.spacing.x) / (this.cellSize.x + this.spacing.x);
			return (Mathf.FloorToInt(num) + 1) * this.arrangeCount;
		}

		private int GetCellIndexAtPositionYStart(float position)
		{
			float num = (position + this.spacing.y) / (this.cellSize.y + this.spacing.y);
			return Mathf.FloorToInt(num) * this.arrangeCount;
		}

		private int GetCellIndexAtPositionYEnd(float position)
		{
			float num = (position - 0.5f * this.spacing.y) / (this.cellSize.y + this.spacing.y);
			return (Mathf.FloorToInt(num) + 1) * this.arrangeCount;
		}

		[SerializeField]
		[Tooltip("The element scroll direction.")]
		private GridView.ScrollDirectionEnum scrollDirection;

		[SerializeField]
		[Tooltip("The element fill direction.")]
		private GridView.FillDirectionEnum fillDirection;

		[SerializeField]
		[Tooltip("The space between each element.")]
		private Vector2 spacing;

		[SerializeField]
		[Tooltip("The size of each cell.")]
		private Vector2 cellSize;

		[SerializeField]
		[Tooltip("The arrange count of the grid.")]
		private int arrangeCount = 1;

		[SerializeField]
		[Tooltip("Whether snapping the element in the border.")]
		private bool snapping;

		[SerializeField]
		[Tooltip("The snap animation speed.")]
		private float snapSpeed = 5f;

		public enum ScrollDirectionEnum
		{
			Horizontal,
			Vertical
		}

		public enum FillDirectionEnum
		{
			Forward,
			Reverse
		}
	}
}
