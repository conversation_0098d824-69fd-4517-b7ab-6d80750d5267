﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Curved Text")]
	[RequireComponent(typeof(Text))]
	public sealed class UICurvedText : BaseMeshEffect
	{
		public UICurvedText.CurveModeEnum CurveMode
		{
			get
			{
				return this.curveMode;
			}
			set
			{
				bool flag = this.curveMode != value;
				if (flag)
				{
					this.curveMode = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public float Strength
		{
			get
			{
				return this.strength;
			}
			set
			{
				bool flag = this.strength != value;
				if (flag)
				{
					this.strength = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public float Frequency
		{
			get
			{
				return this.frequency;
			}
			set
			{
				bool flag = this.frequency != value;
				if (flag)
				{
					this.frequency = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public float Phase
		{
			get
			{
				return this.phase;
			}
			set
			{
				bool flag = this.phase != value;
				if (flag)
				{
					this.phase = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive() || vh.currentVertCount <= 0;
			if (!flag)
			{
				List<UIVertex> list = UIVertexListPool.Get();
				vh.GetUIVertexStream(list);
				List<UIVertex> list2 = this.Modify(list);
				bool flag2 = list2 != null;
				if (flag2)
				{
					vh.Clear();
					vh.AddUIVertexTriangleStream(list2);
				}
				UIVertexListPool.Release(list);
			}
		}

		private List<UIVertex> Modify(List<UIVertex> verts)
		{
			Vector2 vector = Vector2.zero;
			Vector2 vector2 = Vector2.zero;
			bool flag = this.curveMode == UICurvedText.CurveModeEnum.FullRect;
			if (flag)
			{
				RectTransform rectTransform = base.transform as RectTransform;
				Rect rect = rectTransform.rect;
				vector..ctor(rect.xMin, rect.yMax);
				vector2..ctor(rect.xMax, rect.yMin);
			}
			else
			{
				vector = verts[0].position;
				vector2 = verts[verts.Count - 1].position;
				for (int i = 0; i < verts.Count; i++)
				{
					bool flag2 = verts[i].position.x < vector.x;
					if (flag2)
					{
						vector.x = verts[i].position.x;
					}
					bool flag3 = verts[i].position.y > vector.y;
					if (flag3)
					{
						vector.y = verts[i].position.y;
					}
					bool flag4 = verts[i].position.x > vector2.x;
					if (flag4)
					{
						vector2.x = verts[i].position.x;
					}
					bool flag5 = verts[i].position.y < vector2.y;
					if (flag5)
					{
						vector2.y = verts[i].position.y;
					}
				}
			}
			float num = vector2.x - vector.x;
			for (int j = 0; j < verts.Count; j++)
			{
				UIVertex uivertex = verts[j];
				float num2 = (uivertex.position.x - vector.x) / num;
				num2 = this.frequency * (this.phase + num2);
				uivertex.position.y = uivertex.position.y + this.curve.Evaluate(num2) * this.strength;
				verts[j] = uivertex;
			}
			return verts;
		}

		[SerializeField]
		private UICurvedText.CurveModeEnum curveMode = UICurvedText.CurveModeEnum.TextArea;

		[SerializeField]
		private AnimationCurve curve = new AnimationCurve(new Keyframe[]
		{
			new Keyframe(0f, 0f, 0f, 2f),
			new Keyframe(1f, 0f, -2f, 0f)
		});

		[SerializeField]
		private float strength = 1f;

		[SerializeField]
		private float frequency = 1f;

		[SerializeField]
		private float phase = 0f;

		public enum CurveModeEnum
		{
			TextArea,
			FullRect
		}
	}
}
