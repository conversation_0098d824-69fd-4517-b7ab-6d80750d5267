﻿using System;
using System.IO;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.Networking;

namespace Nirvana
{
	internal sealed class AssetBundleLoadTask
	{
		internal AssetBundleLoadTask(bool updateOnly, AssetBundleDownloader downloader, string url, AssetBundleCache cache, string bundleName, Hash128 hash)
		{
			this.UpdateOnly = updateOnly;
			this.downloader = downloader;
			this.url = url;
			this.cache = cache;
			this.bundleName = bundleName;
			this.hash = hash;
			this.ResetSampleSpeed(0.05f);
		}

		internal AssetBundleLoadTask(bool updateOnly, AssetBundleDownloader downloader, string url, AssetBundleCache cache, string bundleName)
		{
			this.UpdateOnly = updateOnly;
			this.downloader = downloader;
			this.url = url;
			this.cache = cache;
			this.bundleName = bundleName;
			this.ResetSampleSpeed(0.05f);
		}

		internal AssetBundleLoadTask(AssetBundleCache cache, AssetBundleCreateRequest request, string bundleName, Hash128 hash)
		{
			this.UpdateOnly = false;
			this.cache = cache;
			this.request = request;
			this.bundleName = bundleName;
			this.hash = hash;
		}

		internal AssetBundleLoadTask(AssetBundleCache cache, AssetBundleCreateRequest request, string bundleName)
		{
			this.UpdateOnly = false;
			this.cache = cache;
			this.request = request;
			this.bundleName = bundleName;
		}

		internal AssetBundle AssetBundle { get; private set; }

		internal string Error { get; private set; }

		internal string AssetBundleName
		{
			get
			{
				return this.bundleName;
			}
		}

		internal Hash128 Hash
		{
			get
			{
				return this.hash;
			}
		}

		internal string Url
		{
			get
			{
				return this.url;
			}
		}

		internal int BytesDownloaded
		{
			get
			{
				return (int)this.bytesDownloaded;
			}
		}

		internal int ContentLength
		{
			get
			{
				return 0;
			}
		}

		internal float Progress
		{
			get
			{
				return this.progress;
			}
		}

		internal int DownloadSpeed
		{
			get
			{
				return (int)this.sampleSpeed;
			}
		}

		internal bool UpdateOnly { get; set; }

		internal bool Updating
		{
			get
			{
				return this.www != null;
			}
		}

		public static void SetMaxWriteCountLimit(int value)
		{
			AssetBundleLoadTask.maxWriteCountInTime = value;
		}

		public static void ClearWriteCountLimit()
		{
			AssetBundleLoadTask.writeCountInTime = 0;
		}

		internal void StartDownload()
		{
			Assert.IsNull<UnityWebRequest>(this.www);
			bool isValid = this.hash.isValid;
			if (isValid)
			{
				this.cachePath = this.cache.GetCachePath(this.bundleName, this.hash);
			}
			else
			{
				this.cachePath = this.cache.GetCachePath(this.bundleName);
			}
			this.www = UnityWebRequest.Get(this.url);
			this.www.Send();
			this.sendRequest = true;
		}

		internal bool Update()
		{
			bool flag = this.www != null;
			if (flag)
			{
				this.UpdateDownload();
			}
			else
			{
				this.UpdateRequest();
			}
			bool flag2 = !string.IsNullOrEmpty(this.Error);
			bool flag3;
			if (flag2)
			{
				flag3 = false;
			}
			else
			{
				bool updateOnly = this.UpdateOnly;
				if (updateOnly)
				{
					bool flag4 = this.sendRequest && !this.Updating;
					if (flag4)
					{
						return false;
					}
				}
				flag3 = this.AssetBundle == null;
			}
			return flag3;
		}

		private void UpdateDownload()
		{
			ulong num = this.www.downloadedBytes - this.bytesDownloaded;
			this.bytesDownloaded = this.www.downloadedBytes;
			this.progress = this.www.downloadProgress;
			this.UpdateSampleSpeed(num);
			bool isNetworkError = this.www.isNetworkError;
			if (isNetworkError)
			{
				this.Error = string.Format("http load error: {0} {1}", this.url, this.www.error);
				this.www.Dispose();
				this.www = null;
				AssetBundleDownloader assetBundleDownloader = this.downloader;
				int num2 = this.tryReDownlodCount + 1;
				this.tryReDownlodCount = num2;
				assetBundleDownloader.Complete(this, num2 <= this.maxReDownlodCount);
			}
			else
			{
				bool flag = this.www.responseCode < 0L || this.www.responseCode >= 400L;
				if (flag)
				{
					this.Error = string.Format("http load error: {0} {1}", this.url, this.www.responseCode.ToString());
					this.www.Dispose();
					this.www = null;
					AssetBundleDownloader assetBundleDownloader2 = this.downloader;
					int num2 = this.tryReDownlodCount + 1;
					this.tryReDownlodCount = num2;
					assetBundleDownloader2.Complete(this, num2 <= this.maxReDownlodCount);
				}
				else
				{
					bool isDone = this.www.isDone;
					if (isDone)
					{
						bool flag2 = AssetBundleLoadTask.writeCountInTime < AssetBundleLoadTask.maxWriteCountInTime;
						if (flag2)
						{
							AssetBundleLoadTask.writeCountInTime++;
							try
							{
								string directoryName = Path.GetDirectoryName(this.cachePath);
								bool flag3 = !Directory.Exists(directoryName);
								if (flag3)
								{
									Directory.CreateDirectory(directoryName);
								}
								File.WriteAllBytes(this.cachePath, this.www.downloadHandler.data);
							}
							catch (Exception ex)
							{
								this.Error = ex.Message;
								this.www.Dispose();
								this.www = null;
								AssetBundleDownloader assetBundleDownloader3 = this.downloader;
								int num2 = this.tryReDownlodCount + 1;
								this.tryReDownlodCount = num2;
								assetBundleDownloader3.Complete(this, num2 <= this.maxReDownlodCount);
								return;
							}
							this.www.Dispose();
							this.www = null;
							this.UpdateComplete();
						}
						this.downloader.Complete(this, false);
					}
				}
			}
		}

		private void UpdateComplete()
		{
			bool updateOnly = this.UpdateOnly;
			if (!updateOnly)
			{
				bool flag = this.cache != null;
				if (flag)
				{
					bool isValid = this.hash.isValid;
					if (isValid)
					{
						Assert.IsNotNull<AssetBundleCache>(this.cache);
						this.request = this.cache.LoadFromCache(this.bundleName, this.hash, false);
						bool flag2 = this.request == null;
						if (flag2)
						{
							this.Error = string.Format("The asset at: {0} with hash {1} is not in the cache.", this.www.url, this.hash);
						}
					}
					else
					{
						Assert.IsNotNull<AssetBundleCache>(this.cache);
						this.request = this.cache.LoadFromCache(this.bundleName);
						bool flag3 = this.request == null;
						if (flag3)
						{
							this.Error = string.Format("The asset at: {0} is not in the cache.", this.www.url, this.hash);
						}
					}
				}
				else
				{
					AssetBundle content = DownloadHandlerAssetBundle.GetContent(this.www);
					bool flag4 = content != null;
					if (flag4)
					{
						this.SetAssetBundle(content);
					}
					else
					{
						this.Error = string.Format("The asset at: {0} is not an asset bundle.", this.www.url);
					}
				}
			}
		}

		private void UpdateRequest()
		{
			bool flag = this.request == null || !this.request.isDone;
			if (!flag)
			{
				bool flag2 = this.request.assetBundle != null;
				if (flag2)
				{
					this.SetAssetBundle(this.request.assetBundle);
				}
				else
				{
					bool flag3 = this.cache != null;
					if (flag3)
					{
						bool isValid = this.hash.isValid;
						if (isValid)
						{
							this.cache.DeleteCache(this.bundleName, this.hash);
							this.Error = string.Format("The asset at: {0} with hash {1} in cache is not an asset bundle.", this.bundleName, this.hash);
						}
						else
						{
							this.cache.DeleteCache(this.bundleName);
							this.Error = string.Format("The asset at: {0} in cache is not an asset bundle.", this.bundleName);
						}
					}
					else
					{
						this.Error = string.Format("The asset at: {0} with hash {1} is not an asset bundle.", this.bundleName, this.hash);
					}
				}
			}
		}

		private void SetAssetBundle(AssetBundle assetBundle)
		{
			this.AssetBundle = assetBundle;
		}

		private void ResetSampleSpeed(float interval)
		{
			this.sampleTimeLeft = interval;
			this.sampleAccumTime = 0f;
			this.sampleDownloadBytes = 0UL;
		}

		private void UpdateSampleSpeed(ulong deltaBytes)
		{
			this.sampleTimeLeft -= Time.unscaledDeltaTime;
			this.sampleAccumTime += Time.unscaledDeltaTime;
			this.sampleDownloadBytes += deltaBytes;
			bool flag = this.sampleTimeLeft <= 0f;
			if (flag)
			{
				this.sampleSpeed = (ulong)(this.sampleDownloadBytes / this.sampleAccumTime);
				bool flag2 = this.sampleSpeed == 0UL;
				if (flag2)
				{
					this.ResetSampleSpeed(0.05f);
				}
				else
				{
					this.ResetSampleSpeed(0.5f);
				}
			}
		}

		private const float SampleSpeedInitInterval = 0.05f;

		private const float SampleSpeedUpdateInterval = 0.5f;

		private static int maxWriteCountInTime = 5;

		private static int writeCountInTime = 0;

		private AssetBundleDownloader downloader;

		private string url;

		private UnityWebRequest www;

		private bool sendRequest;

		private string cachePath;

		private AssetBundleCache cache;

		private AssetBundleCreateRequest request;

		private string bundleName;

		private Hash128 hash;

		private ulong bytesDownloaded;

		private float progress;

		private ulong sampleSpeed;

		private float sampleTimeLeft;

		private float sampleAccumTime;

		private ulong sampleDownloadBytes;

		private int maxReDownlodCount = 6;

		private int tryReDownlodCount = 0;
	}
}
