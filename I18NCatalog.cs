﻿using System;
using UnityEngine;

namespace Nirvana
{
	[CreateAssetMenu(fileName = "I18NCatalog", menuName = "Nirvana/I18N/I18NCatalog")]
	public sealed class I18NCatalog : ScriptableObject
	{
		public AssetID FindCatalog(SystemLanguage language, out bool find)
		{
			bool flag = this.entries == null;
			AssetID assetID;
			if (flag)
			{
				find = false;
				assetID = AssetID.Empty;
			}
			else
			{
				foreach (I18NCatalog.CatalogEntry catalogEntry in this.entries)
				{
					bool flag2 = catalogEntry.Language == language;
					if (flag2)
					{
						find = true;
						return catalogEntry.Asset;
					}
				}
				find = false;
				assetID = AssetID.Empty;
			}
			return assetID;
		}

		[SerializeField]
		private I18NCatalog.CatalogEntry[] entries;

		[Serializable]
		private struct CatalogEntry
		{
			[SerializeField]
			[Tooltip("The target language.")]
			public SystemLanguage Language;

			[SerializeField]
			[Tooltip("The i18n mo file asset.")]
			[AssetType(typeof(TextAsset))]
			public AssetID Asset;
		}
	}
}
