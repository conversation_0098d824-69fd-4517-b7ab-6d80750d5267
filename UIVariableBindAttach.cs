﻿using System;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Attach")]
	public sealed class UIVariableBindAttach : UIVariableBind
	{
		public override void BindVariables()
		{
			Assert.IsNull<UIVariable>(this.assetVariable);
			bool flag = !string.IsNullOrEmpty(this.assetBind);
			if (flag)
			{
				this.assetVariable = base.FindVariable(this.assetBind);
				bool flag2 = this.assetVariable == null;
				if (flag2)
				{
					UIVariableBindAttach.logger.LogWarning("{0} can not find a variable {1}", new object[] { base.name, this.assetBind });
				}
				else
				{
					this.assetVariable.OnValueInitialized += this.OnAssetChanged;
					this.assetVariable.OnValueChanged += this.OnAssetChanged;
					this.OnAssetChanged();
				}
			}
		}

		public override void UnbindVariables()
		{
			bool flag = this.assetVariable != null;
			if (flag)
			{
				this.assetVariable.OnValueInitialized -= this.OnAssetChanged;
				this.assetVariable.OnValueChanged -= this.OnAssetChanged;
				this.assetVariable = null;
			}
		}

		private void OnAssetChanged()
		{
		}

		private static Logger logger = LogSystem.GetLogger("UIVariableBindAttach");

		[SerializeField]
		[VariableName(UIVariableType.Asset)]
		private string assetBind;

		private UIVariable assetVariable;
	}
}
