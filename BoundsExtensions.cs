﻿using System;
using System.Text.RegularExpressions;
using UnityEngine;

namespace Nirvana
{
	public static class BoundsExtensions
	{
		public static Bounds Parse(string text)
		{
			Bounds bounds;
			bool flag = !BoundsExtensions.TryParse(text, out bounds);
			if (flag)
			{
				string text2 = string.Format("The string {0} can not convert to Rect.", text);
				throw new FormatException(text2);
			}
			return bounds;
		}

		public static bool TryParse(string text, out Bounds bounds)
		{
			bool flag = BoundsExtensions.parseRegex == null;
			if (flag)
			{
				BoundsExtensions.parseRegex = new Regex("^Center: \\((.*), (.*), (.*)\\), Extents: \\((.*), (.*), (.*)\\)$");
			}
			Match match = BoundsExtensions.parseRegex.Match(text);
			bool flag2 = !match.Success || match.Groups.Count != 7;
			bool flag3;
			if (flag2)
			{
				bounds = new Bounds(Vector3.zero, Vector3.zero);
				flag3 = false;
			}
			else
			{
				float num;
				bool flag4 = !float.TryParse(match.Groups[1].Value, out num);
				if (flag4)
				{
					bounds = new Bounds(Vector3.zero, Vector3.zero);
					flag3 = false;
				}
				else
				{
					float num2;
					bool flag5 = !float.TryParse(match.Groups[2].Value, out num2);
					if (flag5)
					{
						bounds = new Bounds(Vector3.zero, Vector3.zero);
						flag3 = false;
					}
					else
					{
						float num3;
						bool flag6 = !float.TryParse(match.Groups[3].Value, out num3);
						if (flag6)
						{
							bounds = new Bounds(Vector3.zero, Vector3.zero);
							flag3 = false;
						}
						else
						{
							float num4;
							bool flag7 = !float.TryParse(match.Groups[4].Value, out num4);
							if (flag7)
							{
								bounds = new Bounds(Vector3.zero, Vector3.zero);
								flag3 = false;
							}
							else
							{
								float num5;
								bool flag8 = !float.TryParse(match.Groups[5].Value, out num5);
								if (flag8)
								{
									bounds = new Bounds(Vector3.zero, Vector3.zero);
									flag3 = false;
								}
								else
								{
									float num6;
									bool flag9 = !float.TryParse(match.Groups[6].Value, out num6);
									if (flag9)
									{
										bounds = new Bounds(Vector3.zero, Vector3.zero);
										flag3 = false;
									}
									else
									{
										bounds = new Bounds(new Vector3(num, num2, num3), new Vector3(num4, num5, num6));
										flag3 = true;
									}
								}
							}
						}
					}
				}
			}
			return flag3;
		}

		private static Regex parseRegex;
	}
}
