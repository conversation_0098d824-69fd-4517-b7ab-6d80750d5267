﻿using System;
using System.Collections;
using UnityEngine;

namespace Nirvana
{
	public sealed class AudioDummyController : IAudioController, IAudioPlayer
	{
		internal AudioDummyController()
		{
		}

		public bool IsPlaying
		{
			get
			{
				return false;
			}
		}

		public float LeftTime
		{
			get
			{
				return 0f;
			}
		}

		internal static AudioDummyController Default
		{
			get
			{
				bool flag = AudioDummyController.dummyDefault == null;
				if (flag)
				{
					AudioDummyController.dummyDefault = new AudioDummyController();
				}
				return AudioDummyController.dummyDefault;
			}
		}

		public IEnumerator WaitFinish()
		{
			return null;
		}

		public void Stop()
		{
		}

		public void SetPosition(Vector3 position)
		{
		}

		public void SetTransform(Transform transform)
		{
		}

		public void Play()
		{
		}

		public void Update()
		{
		}

		public void FinshAudio()
		{
		}

		private static AudioDummyController dummyDefault;
	}
}
