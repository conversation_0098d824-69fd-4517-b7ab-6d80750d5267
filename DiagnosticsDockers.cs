﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	internal sealed class DiagnosticsDockers
	{
		internal void AddDocker(IDiagnosticsDocker docker)
		{
			DiagnosticsDockers.DockerRecord dockerRecord = new DiagnosticsDockers.DockerRecord();
			dockerRecord.Docker = docker;
			this.dockers.Add(dockerRecord);
		}

		internal void Draw()
		{
			this.UpdateTexts();
			this.DrawTexts();
		}

		private void UpdateTexts()
		{
			bool flag = false;
			for (int i = 0; i < this.dockers.Count; i++)
			{
				DiagnosticsDockers.DockerRecord dockerRecord = this.dockers[i];
				IDiagnosticsDocker docker = dockerRecord.Docker;
				bool dockDirty = docker.DockDirty;
				if (dockDirty)
				{
					string dockText = docker.GetDockText();
					dockerRecord.Text = dockText;
					docker.DockDirty = false;
					flag = true;
				}
			}
			bool flag2 = flag;
			if (flag2)
			{
				this.textTL = string.Empty;
				this.textTR = string.Empty;
				this.textBL = string.Empty;
				this.textBR = string.Empty;
				for (int j = 0; j < this.dockers.Count; j++)
				{
					DiagnosticsDockers.DockerRecord dockerRecord2 = this.dockers[j];
					IDiagnosticsDocker docker2 = dockerRecord2.Docker;
					switch (docker2.DockAnchor)
					{
					case DiagnosticsDockAnchor.TopLeft:
						this.textTL += dockerRecord2.Text;
						break;
					case DiagnosticsDockAnchor.TopRight:
						this.textTR += dockerRecord2.Text;
						break;
					case DiagnosticsDockAnchor.BottomLeft:
						this.textBL += dockerRecord2.Text;
						break;
					case DiagnosticsDockAnchor.BottomRight:
						this.textBR += dockerRecord2.Text;
						break;
					}
				}
			}
		}

		private void DrawTexts()
		{
			bool flag = this.labelStyle == null;
			if (flag)
			{
				this.labelStyle = new GUIStyle(GUI.skin.label);
			}
			Rect rect;
			rect..ctor(0f, 0f, (float)Screen.width, (float)Screen.height);
			bool flag2 = !string.IsNullOrEmpty(this.textTL);
			if (flag2)
			{
				this.labelStyle.alignment = 0;
				GUI.Label(rect, this.textTL, this.labelStyle);
			}
			bool flag3 = !string.IsNullOrEmpty(this.textTR);
			if (flag3)
			{
				this.labelStyle.alignment = 2;
				GUI.Label(rect, this.textTR, this.labelStyle);
			}
			bool flag4 = !string.IsNullOrEmpty(this.textBL);
			if (flag4)
			{
				this.labelStyle.alignment = 6;
				GUI.Label(rect, this.textBL, this.labelStyle);
			}
			bool flag5 = !string.IsNullOrEmpty(this.textBR);
			if (flag5)
			{
				this.labelStyle.alignment = 8;
				GUI.Label(rect, this.textBR, this.labelStyle);
			}
		}

		private List<DiagnosticsDockers.DockerRecord> dockers = new List<DiagnosticsDockers.DockerRecord>();

		private GUIStyle labelStyle;

		private string textTL;

		private string textTR;

		private string textBL;

		private string textBR;

		private class DockerRecord
		{
			public IDiagnosticsDocker Docker { get; set; }

			public string Text { get; set; }
		}
	}
}
