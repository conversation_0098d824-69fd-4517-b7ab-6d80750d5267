﻿using System;
using UnityEngine;

namespace Nirvana
{
	public struct SphereBounds
	{
		public SphereBounds(Vector3 center, float radius)
		{
			this.Center = center;
			this.Radius = radius;
		}

		public Vector3 Center { readonly get; set; }

		public float Radius { readonly get; set; }

		public bool Intersects(SphereBounds bounds)
		{
			float sqrMagnitude = (this.Center - bounds.Center).sqrMagnitude;
			float num = this.Radius + bounds.Radius;
			return sqrMagnitude <= num * num;
		}

		public bool Intersects(Bounds bounds)
		{
			bool flag = bounds.Contains(this.Center);
			bool flag2;
			if (flag)
			{
				flag2 = true;
			}
			else
			{
				Vector3 min = bounds.min;
				Vector3 max = bounds.max;
				float num = 0f;
				bool flag3 = this.Center.x < min.x;
				if (flag3)
				{
					float num2 = this.Center.x - min.x;
					num += num2 * num2;
				}
				else
				{
					bool flag4 = this.Center.x > max.x;
					if (flag4)
					{
						float num2 = this.Center.x - max.x;
						num += num2 * num2;
					}
				}
				bool flag5 = this.Center.y < min.y;
				if (flag5)
				{
					float num2 = this.Center.y - min.y;
					num += num2 * num2;
				}
				else
				{
					bool flag6 = this.Center.y > max.y;
					if (flag6)
					{
						float num2 = this.Center.y - max.y;
						num += num2 * num2;
					}
				}
				bool flag7 = this.Center.z < min.z;
				if (flag7)
				{
					float num2 = this.Center.z - min.z;
					num += num2 * num2;
				}
				else
				{
					bool flag8 = this.Center.z > max.z;
					if (flag8)
					{
						float num2 = this.Center.z - max.z;
						num += num2 * num2;
					}
				}
				flag2 = num <= this.Radius * this.Radius;
			}
			return flag2;
		}
	}
}
