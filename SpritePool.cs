﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	public sealed class SpritePool : Singleton<SpritePool>
	{
		public SpritePool()
		{
			this.sweepChecker = delegate(AssetID assetID, SpriteCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						bool flag4 = this.unloadedCountInTime < 5 && Time.time > cache.LastFreeTime + cache.ReleaseAfterFree;
						if (flag4)
						{
							this.unloadedCountInTime++;
							Sprite @object = cache.GetObject();
							bool flag5 = @object != null;
							if (flag5)
							{
								this.lookup.Remove(@object);
								AssetManager.UnloadAsseBundle(assetID.BundleName);
							}
							flag2 = true;
						}
						else
						{
							flag2 = false;
						}
					}
				}
				return flag2;
			};
			Scheduler.AddFrameListener(new Action(this.Update));
		}

		public void SetMaxLoadingCount(int value)
		{
			this.maxLoadingCount = value;
		}

		public float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				foreach (KeyValuePair<AssetID, SpriteCache> keyValuePair in this.caches)
				{
					keyValuePair.Value.DefaultReleaseAfterFree = value;
				}
			}
		}

		private void Update()
		{
			this.QueueLoadSprite();
			bool flag = Time.time - this.lastCheckSweepTime >= 1f;
			if (flag)
			{
				this.lastCheckSweepTime = Time.time;
				this.unloadedCountInTime = 0;
				this.caches.RemoveAll(this.sweepChecker);
			}
		}

		private void QueueLoadSprite()
		{
			int num = this.maxLoadingCount + this.loadQueue.Count / 5;
			while (this.loadQueue.Count > 0 && num - this.loadingCount > 0)
			{
				this.loadingCount++;
				SpriteLoadItem spriteLoadItem = this.loadQueue.Dequeue();
				Scheduler.RunCoroutine(this.LoadAsyncImplInQueueLoad(spriteLoadItem.assetId, spriteLoadItem.complete));
			}
		}

		public void Load(AssetID assetID, Action<Sprite> complete, bool isSync = false)
		{
			bool flag = !assetID.AssetName.EndsWith(".png");
			if (flag)
			{
				assetID.AssetName += ".png";
			}
			assetID.AssetName = assetID.AssetName.ToLower();
			if (isSync)
			{
				Scheduler.RunCoroutine(this.LoadSyncImpl(assetID, complete));
			}
			else
			{
				bool flag2 = !AssetManager.IsVersionCached(assetID.BundleName);
				if (flag2)
				{
					Scheduler.RunCoroutine(this.LoadAsyncImpl(assetID, complete));
				}
				else
				{
					SpriteLoadItem spriteLoadItem = new SpriteLoadItem(assetID, complete);
					this.loadQueue.Enqueue(spriteLoadItem);
				}
			}
		}

		public void SetImageSprite(Image image, Sprite sprite)
		{
			bool flag = null == image || null == sprite;
			if (!flag)
			{
				SpriteReference spriteReference = ReferenceDict.AddSpriteReference(image, sprite);
				image.sprite = sprite;
				SpriteReferenceHolder orAddComponent = image.GetOrAddComponent<SpriteReferenceHolder>();
				orAddComponent.SetSpriteReference(spriteReference);
			}
		}

		public bool Retain(Sprite sprite)
		{
			bool flag = sprite == null;
			bool flag2;
			if (flag)
			{
				SpritePool.logger.LogError("Try to Retain a null sprite.");
				flag2 = false;
			}
			else
			{
				SpriteCache spriteCache;
				bool flag3 = !this.lookup.TryGetValue(sprite, out spriteCache);
				if (flag3)
				{
					flag2 = false;
				}
				else
				{
					spriteCache.Retain();
					flag2 = true;
				}
			}
			return flag2;
		}

		public void Free(Sprite sprite, bool destroy = false)
		{
			bool flag = sprite == null;
			if (flag)
			{
				SpritePool.logger.LogError("Try to free a null Sprite.");
			}
			else
			{
				SpriteCache spriteCache;
				bool flag2 = !this.lookup.TryGetValue(sprite, out spriteCache);
				if (flag2)
				{
					SpritePool.logger.LogWarning("Try to free an instance {0} not allocated by this pool.", new object[] { sprite.name });
				}
				else
				{
					spriteCache.Release();
					bool flag3 = destroy && spriteCache.ReferenceCount == 0;
					if (flag3)
					{
						AssetID assetID = spriteCache.AssetID;
						Sprite @object = spriteCache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						this.caches.Remove(assetID);
					}
				}
			}
		}

		public void Clear()
		{
			this.caches.Clear();
			this.lookup.Clear();
		}

		public void ClearAllUnused()
		{
			this.caches.RemoveAll(delegate(AssetID assetID, SpriteCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						Sprite @object = cache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						flag2 = true;
					}
				}
				return flag2;
			});
		}

		private IEnumerator LoadAsyncImpl(AssetID assetID, Action<Sprite> complete)
		{
			WaitLoadSprite waitLoad = this.InternalLoad(assetID, false);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			bool flag = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag)
			{
				SpritePool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private IEnumerator LoadAsyncImplInQueueLoad(AssetID assetID, Action<Sprite> complete)
		{
			WaitLoadSprite waitLoad = this.InternalLoad(assetID, false);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			this.loadingCount--;
			bool flag = this.loadingCount < 0;
			if (flag)
			{
				Debug.LogError("[SpritePool] loadingCount is occur error " + this.loadingCount.ToString());
			}
			bool flag2 = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag2)
			{
				SpritePool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private IEnumerator LoadSyncImpl(AssetID assetID, Action<Sprite> complete)
		{
			WaitLoadSprite waitLoad = this.InternalLoad(assetID, false);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			bool flag = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag)
			{
				SpritePool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private WaitLoadSprite InternalLoad(AssetID assetID, bool isSync)
		{
			SpriteCache spriteCache;
			bool flag = this.caches.TryGetValue(assetID, out spriteCache);
			WaitLoadSprite waitLoadSprite;
			if (flag)
			{
				spriteCache.Retain();
				waitLoadSprite = new WaitLoadSprite(spriteCache);
			}
			else
			{
				spriteCache = new SpriteCache(assetID, this.lookup);
				spriteCache.DefaultReleaseAfterFree = this.DefaultReleaseAfterFree;
				spriteCache.IsSync = isSync;
				spriteCache.LoadObject(assetID);
				spriteCache.Retain();
				this.caches.Add(assetID, spriteCache);
				waitLoadSprite = new WaitLoadSprite(spriteCache);
			}
			return waitLoadSprite;
		}

		public bool CheckCanLoadSprite(GameObject gameObject, AssetID assetID)
		{
			return true;
		}

		private static Logger logger = LogSystem.GetLogger("SpritePool");

		private Dictionary<AssetID, SpriteCache> caches = new Dictionary<AssetID, SpriteCache>();

		private Dictionary<Sprite, SpriteCache> lookup = new Dictionary<Sprite, SpriteCache>();

		private float defaultReleaseAfterFree = 30f;

		private Func<AssetID, SpriteCache, bool> sweepChecker;

		private int unloadedCountInTime = 0;

		private float lastCheckSweepTime = 0f;

		private Queue<SpriteLoadItem> loadQueue = new Queue<SpriteLoadItem>();

		private int maxLoadingCount = 4;

		private int loadingCount = 0;
	}
}
