﻿using System;
using System.Collections;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Active")]
	public sealed class UIVariableBindActive : UIVariableBindBool
	{
		protected override void OnValueChanged()
		{
			bool result = base.GetResult();
			bool flag = this.transitionMode == UIVariableBindActive.TransitionModeEnum.Instant;
			if (flag)
			{
				base.gameObject.SetActive(result);
			}
			else
			{
				bool flag2 = this.canvasGroup == null;
				if (flag2)
				{
					this.canvasGroup = base.gameObject.GetComponent<CanvasGroup>();
				}
				bool flag3 = this.canvasGroup != null;
				if (flag3)
				{
					base.gameObject.SetActive(true);
					bool activeInHierarchy = base.gameObject.activeInHierarchy;
					if (activeInHierarchy)
					{
						base.StopAllCoroutines();
						bool flag4 = result;
						if (flag4)
						{
							base.StartCoroutine(this.TransiteFade(this.canvasGroup, 1f, true));
						}
						else
						{
							base.StartCoroutine(this.TransiteFade(this.canvasGroup, 0f, false));
						}
					}
					else
					{
						base.gameObject.SetActive(result);
					}
				}
				else
				{
					base.gameObject.SetActive(result);
				}
			}
		}

		private IEnumerator TransiteFade(CanvasGroup group, float alphaTarget, bool activeTarget)
		{
			float leftTime = this.transitionTime;
			float alphaStart = group.alpha;
			while (leftTime > 0f)
			{
				yield return null;
				leftTime -= Time.deltaTime;
				float alpha = Mathf.Lerp(alphaStart, alphaTarget, 1f - leftTime / this.transitionTime);
				group.alpha = alpha;
			}
			group.gameObject.SetActive(activeTarget);
			yield break;
		}

		[SerializeField]
		private UIVariableBindActive.TransitionModeEnum transitionMode = UIVariableBindActive.TransitionModeEnum.Instant;

		[SerializeField]
		private float transitionTime = 0.1f;

		private CanvasGroup canvasGroup;

		public enum TransitionModeEnum
		{
			Instant,
			Fade
		}
	}
}
