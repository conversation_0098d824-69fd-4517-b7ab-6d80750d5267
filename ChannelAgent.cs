﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class ChannelAgent
	{
		public static event Action<bool> InitializedEvent;

		public static event Action<string> LoginEvent;

		public static event Action<string> ReserveEvent;

		public static event Action LogoutEvent;

		public static event Action ExitEvent;

		public static string GetChannelID()
		{
			return ChannelAgent.GetAgent().Call<string>("getChannelID", new object[0]);
		}

		public static string GetAgentID()
		{
			return ChannelAgent.GetAgent().Call<string>("getAgentID", new object[0]);
		}

		public static string GetAliasPathMapPath()
		{
			return string.Empty;
		}

		public static string GetEncryptKey()
		{
			return "5,1,9,6";
		}

		public static bool GetOutlog()
		{
			return ChannelAgent.GetAgent().Call<string>("getOutLog", new object[0]) == "1";
		}

		public static string GetInitUrl()
		{
			return ChannelAgent.GetAgent().Call<string>("getInitUrl", new object[0]);
		}

		public static string GetInitData()
		{
			return ChannelAgent.GetAgent().Call<string>("getInitData", new object[0]);
		}

		public static void Initialize()
		{
			ChannelAgent.GetAgent().Call("initialize", new object[0]);
		}

		public static void Login(ChannelUserInfo userInfo)
		{
			ChannelAgent.GetAgent().Call("login", new object[] { userInfo.GetJson() });
		}

		public static void Reserve(string reserveContent)
		{
			ChannelAgent.GetAgent().Call("reserve", new object[] { reserveContent });
		}

		public static void Logout(ChannelUserInfo userInfo)
		{
			ChannelAgent.GetAgent().Call("logout", new object[] { userInfo.GetJson() });
		}

		public static void Pay(ChannelUserInfo userInfo, string orderID, string productID, double amount)
		{
			ChannelAgent.GetAgent().Call("pay", new object[]
			{
				userInfo.GetJson(),
				orderID,
				productID,
				amount
			});
		}

		public static void ReportEnterZone(ChannelUserInfo userInfo)
		{
			ChannelAgent.GetAgent().Call("reportEnterZone", new object[] { userInfo.GetJson() });
		}

		public static void ReportCreateRole(ChannelUserInfo userInfo)
		{
			ChannelAgent.GetAgent().Call("reportCreateRole", new object[] { userInfo.GetJson() });
		}

		public static void ReportLoginRole(ChannelUserInfo userInfo)
		{
			ChannelAgent.GetAgent().Call("reportLoginRole", new object[] { userInfo.GetJson() });
		}

		public static void ReportLogoutRole(ChannelUserInfo userInfo)
		{
			ChannelAgent.GetAgent().Call("reportLogoutRole", new object[] { userInfo.GetJson() });
		}

		public static void ReportLevelUp(ChannelUserInfo userInfo)
		{
			ChannelAgent.GetAgent().Call("reportLevelUp", new object[] { userInfo.GetJson() });
		}

		public static void FacebookActive(string type)
		{
			ChannelAgent.GetAgent().Call("facebookActive", new object[] { type });
		}

		public static void CloseSplash()
		{
			ChannelAgent.GetAgent().Call("DeleteSplash", new object[0]);
		}

		private static AndroidJavaObject GetAgent()
		{
			if (ChannelAgent.agentObject == null)
			{
				ChannelAgent.agentObject = new AndroidJavaClass("com.winunet.issue.WGChannelAgent").CallStatic<AndroidJavaObject>("getInstance", new object[0]);
				ChannelAgent.agentObject.Call("setEventListener", new object[] { ChannelAgent.wgChannelListener });
			}
			return ChannelAgent.agentObject;
		}

		public static void ReportCustomEvent(ChannelUserInfo userInfo, int eventType)
		{
			ChannelAgent.GetAgent().Call("reportCustomEvent", new object[]
			{
				userInfo.GetJson(),
				eventType
			});
		}

		private static AndroidJavaObject agentObject;

		private static ChannelAgent.WGChannelListener wgChannelListener = new ChannelAgent.WGChannelListener();

		private class WGChannelListener : AndroidJavaProxy
		{
			public WGChannelListener()
				: base("com.winunet.issue.WGChannelListener")
			{
			}

			private void onInitialized(bool success)
			{
				SdkScheduler.PostTask(delegate
				{
					if (ChannelAgent.InitializedEvent != null)
					{
						ChannelAgent.InitializedEvent(success);
					}
				});
			}

			private void onLogin(string json)
			{
				SdkScheduler.PostTask(delegate
				{
					if (ChannelAgent.LoginEvent != null)
					{
						ChannelAgent.LoginEvent(json);
					}
				});
			}

			private void onReserve(string json)
			{
				SdkScheduler.PostTask(delegate
				{
					if (ChannelAgent.ReserveEvent != null)
					{
						ChannelAgent.ReserveEvent(json);
					}
				});
			}

			private void onLogout()
			{
				SdkScheduler.PostTask(delegate
				{
					if (ChannelAgent.LogoutEvent != null)
					{
						ChannelAgent.LogoutEvent();
					}
				});
			}

			private void onExit()
			{
				SdkScheduler.PostTask(delegate
				{
					if (ChannelAgent.ExitEvent != null)
					{
						ChannelAgent.ExitEvent();
					}
				});
			}
		}
	}
}
