﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class FontCache
	{
		internal FontCache(AssetID assetID, IDictionary<Font, FontCache> lookup)
		{
			this.assetID = assetID;
			this.lookup = lookup;
		}

		public AssetID AssetID
		{
			get
			{
				return this.assetID;
			}
		}

		public int ReferenceCount
		{
			get
			{
				return this.referenceCount;
			}
		}

		public float LastFreeTime
		{
			get
			{
				return this.lastFreeTime;
			}
		}

		public float ReleaseAfterFree
		{
			get
			{
				return this.releaseAfterFree;
			}
		}

		public string Error { get; private set; }

		internal float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				this.releaseAfterFree = this.defaultReleaseAfterFree;
			}
		}

		internal void Retain()
		{
			this.referenceCount++;
		}

		internal void Release()
		{
			this.referenceCount--;
			this.lastFreeTime = Time.realtimeSinceStartup;
		}

		internal void LoadObject(AssetID assetID)
		{
			Scheduler.RunCoroutine(this.LoadObjectImpl(assetID));
		}

		internal bool HasLoaded()
		{
			return this.cachedObject != null;
		}

		internal Font GetObject()
		{
			return this.cachedObject;
		}

		private IEnumerator LoadObjectImpl(AssetID assetID)
		{
			WaitLoadObject waitobj = AssetManager.LoadObject(assetID, typeof(Font));
			yield return waitobj;
			bool flag = !string.IsNullOrEmpty(waitobj.Error);
			if (flag)
			{
				this.Error = waitobj.Error;
				yield break;
			}
			this.cachedObject = waitobj.GetObject() as Font;
			bool flag2 = this.cachedObject == null;
			if (flag2)
			{
				this.Error = string.Format("The asset {0} is not a Font.", assetID);
				yield break;
			}
			this.releaseAfterFree = this.DefaultReleaseAfterFree;
			bool flag3 = this.lookup.ContainsKey(this.cachedObject);
			if (flag3)
			{
				FontCache.logger.LogWarning("The font {0} has been loaded.", new object[] { assetID });
				this.lookup[this.cachedObject] = this;
			}
			else
			{
				this.lookup.Add(this.cachedObject, this);
			}
			yield break;
		}

		private static Logger logger = LogSystem.GetLogger("FontCache");

		private Font cachedObject;

		private int referenceCount;

		private AssetID assetID;

		private IDictionary<Font, FontCache> lookup;

		private float lastFreeTime = -1f;

		private float defaultReleaseAfterFree = 30f;

		private float releaseAfterFree;
	}
}
