﻿using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Layout/Rich Text Group")]
	[RequireComponent(typeof(FlowLayoutGroup))]
	public sealed class RichTextGroup : MonoBehaviour
	{
		public static void ClearPool()
		{
			RichTextGroup.PoolTransform = null;
			RichTextGroup.BackPoolCallback = null;
			RichTextGroup.NewLineCacheCount = 0;
			RichTextGroup.NewLineCache.Clear();
			RichTextGroup.TextItemCacheCount = 0;
			RichTextGroup.TextChache.Clear();
		}

		public static void SetPool(Transform poolTransform, Action<GameObject> backPoolCallback)
		{
			RichTextGroup.PoolTransform = poolTransform;
			RichTextGroup.BackPoolCallback = backPoolCallback;
		}

		private static void PushPool(List<GameObject> childrens)
		{
			bool flag = null == RichTextGroup.PoolTransform || childrens == null;
			if (!flag)
			{
				foreach (GameObject gameObject in childrens)
				{
					bool flag2 = gameObject.name == "RichTextItem";
					if (flag2)
					{
						bool flag3 = RichTextGroup.TextItemCacheCount < 30;
						if (flag3)
						{
							RichTextGroup.TextItemCacheCount++;
							RichTextGroup.TextChache.Add(gameObject);
							gameObject.SetActive(false);
							gameObject.transform.SetParent(RichTextGroup.PoolTransform, false);
						}
						else
						{
							Object.Destroy(gameObject);
						}
					}
					else
					{
						bool flag4 = gameObject.name == "RichTextNewLine";
						if (flag4)
						{
							bool flag5 = RichTextGroup.NewLineCacheCount < 30;
							if (flag5)
							{
								RichTextGroup.NewLineCacheCount++;
								RichTextGroup.NewLineCache.Add(gameObject);
								gameObject.SetActive(false);
								gameObject.transform.SetParent(RichTextGroup.PoolTransform, false);
							}
							else
							{
								Object.Destroy(gameObject);
							}
						}
						else
						{
							bool flag6 = RichTextGroup.BackPoolCallback != null;
							if (flag6)
							{
								RichTextGroup.BackPoolCallback(gameObject);
							}
							else
							{
								Object.Destroy(gameObject);
							}
						}
					}
				}
				childrens.Clear();
			}
		}

		private static GameObject PopPool(string type)
		{
			GameObject gameObject = null;
			bool flag = type == "RichTextItem" && RichTextGroup.TextItemCacheCount > 0;
			if (flag)
			{
				RichTextGroup.TextItemCacheCount--;
				gameObject = RichTextGroup.TextChache[RichTextGroup.TextItemCacheCount];
				gameObject.SetActive(true);
				gameObject.transform.SetLocalPosition(0f, 0f, 0f);
				RichTextGroup.TextChache.RemoveAt(RichTextGroup.TextItemCacheCount);
			}
			else
			{
				bool flag2 = type == "RichTextNewLine" && RichTextGroup.NewLineCacheCount > 0;
				if (flag2)
				{
					RichTextGroup.NewLineCacheCount--;
					gameObject = RichTextGroup.NewLineCache[RichTextGroup.NewLineCacheCount];
					gameObject.SetActive(true);
					gameObject.transform.SetLocalPosition(0f, 0f, 0f);
					RichTextGroup.NewLineCache.RemoveAt(RichTextGroup.NewLineCacheCount);
				}
			}
			return gameObject;
		}

		public void Clear()
		{
			bool flag = null != RichTextGroup.PoolTransform;
			if (flag)
			{
				RichTextGroup.PushPool(this.childrens);
			}
			else
			{
				foreach (GameObject gameObject in this.childrens)
				{
					gameObject.transform.SetParent(null, false);
					Object.Destroy(gameObject);
				}
				this.childrens.Clear();
			}
			bool flag2 = this.flowLayout == null;
			if (flag2)
			{
				this.flowLayout = base.GetComponent<FlowLayoutGroup>();
			}
			this.flowLayout.LastRowWidth = 0f;
			this.lastRowWidth = 0f;
		}

		public void AddText(string text)
		{
			bool flag = this.rectTransform == null;
			if (flag)
			{
				this.rectTransform = base.GetComponent<RectTransform>();
			}
			bool flag2 = this.flowLayout == null;
			if (flag2)
			{
				this.flowLayout = base.GetComponent<FlowLayoutGroup>();
			}
			Font font = this.textPrefab.font;
			font.RequestCharactersInTexture(text, this.textPrefab.fontSize, this.textPrefab.fontStyle);
			float workingRowWidth = this.flowLayout.WorkingRowWidth;
			StringBuilder stringBuilder = new StringBuilder();
			int num = 0;
			string text2 = string.Empty;
			string text3 = string.Empty;
			int i = 0;
			while (i < text.Length)
			{
				char c = text[i];
				bool flag3 = c == '<';
				if (!flag3)
				{
					goto IL_01E9;
				}
				bool flag4 = i + 1 < text.Length && text[i + 1] != '/';
				if (flag4)
				{
					int num2 = text.IndexOf('>', i + 1);
					bool flag5 = num2 >= 0;
					if (flag5)
					{
						text2 = text.Substring(i + 1, num2 - i - 1);
					}
					int num3 = text2.IndexOf('=');
					bool flag6 = num3 >= 0;
					if (flag6)
					{
						text3 = text2.Substring(0, num3);
					}
					else
					{
						text3 = text2;
					}
					stringBuilder.Append('<');
					stringBuilder.Append(text2);
					stringBuilder.Append('>');
					i = num2;
				}
				else
				{
					bool flag7 = i + 1 < text.Length && text[i + 1] == '/';
					if (!flag7)
					{
						goto IL_01E9;
					}
					int num4 = text.IndexOf('>', i + 1);
					bool flag8 = num4 >= 0;
					if (flag8)
					{
						text2 = text.Substring(i + 2, num4 - i - 2);
					}
					stringBuilder.Append("</");
					stringBuilder.Append(text2);
					stringBuilder.Append('>');
					text2 = string.Empty;
					text3 = string.Empty;
					i = num4;
				}
				IL_03C0:
				i++;
				continue;
				IL_01E9:
				bool flag9 = c == '\n';
				if (flag9)
				{
					bool flag10 = !string.IsNullOrEmpty(text3);
					if (flag10)
					{
						stringBuilder.Append("</");
						stringBuilder.Append(text3);
						stringBuilder.Append(">");
					}
					this.AddTextObject(stringBuilder.ToString(), (float)num);
					this.lastRowWidth = 0f;
					this.AddNewLine();
					stringBuilder = new StringBuilder();
					num = 0;
					bool flag11 = !string.IsNullOrEmpty(text2);
					if (flag11)
					{
						stringBuilder.Append("<");
						stringBuilder.Append(text2);
						stringBuilder.Append(">");
					}
					goto IL_03C0;
				}
				CharacterInfo characterInfo;
				bool flag12 = !font.GetCharacterInfo(c, ref characterInfo, this.textPrefab.fontSize, this.textPrefab.fontStyle);
				if (flag12)
				{
					Debug.LogWarningFormat("Can not get character info: {0}, {1}", new object[] { c, font.fontNames });
				}
				float num5 = workingRowWidth - this.lastRowWidth;
				bool flag13 = Mathf.Approximately(num5, 0f);
				if (flag13)
				{
					num5 = workingRowWidth;
					this.lastRowWidth = 0f;
				}
				bool flag14 = (float)(num + characterInfo.advance) >= num5;
				if (flag14)
				{
					bool flag15 = !string.IsNullOrEmpty(text3);
					if (flag15)
					{
						stringBuilder.Append("</");
						stringBuilder.Append(text3);
						stringBuilder.Append(">");
					}
					this.AddTextObject(stringBuilder.ToString(), (float)num);
					this.lastRowWidth = 0f;
					stringBuilder = new StringBuilder();
					num = 0;
					bool flag16 = !string.IsNullOrEmpty(text2);
					if (flag16)
					{
						stringBuilder.Append("<");
						stringBuilder.Append(text2);
						stringBuilder.Append(">");
					}
				}
				stringBuilder.Append(c);
				num += characterInfo.advance;
				goto IL_03C0;
			}
			bool flag17 = stringBuilder.Length > 0;
			if (flag17)
			{
				this.AddTextObject(stringBuilder.ToString(), (float)num);
			}
		}

		public void AddObject(GameObject go)
		{
			bool flag = this.rectTransform == null;
			if (flag)
			{
				this.rectTransform = base.GetComponent<RectTransform>();
			}
			bool flag2 = this.flowLayout == null;
			if (flag2)
			{
				this.flowLayout = base.GetComponent<FlowLayoutGroup>();
			}
			go.transform.SetParent(this.flowLayout.transform, false);
			this.childrens.Add(go);
			RectTransform component = go.gameObject.GetComponent<RectTransform>();
			float num = component.sizeDelta.x;
			Text componentInChildren = go.GetComponentInChildren<Text>();
			bool flag3 = componentInChildren != null;
			if (flag3)
			{
				float textBtnWidth = this.GetTextBtnWidth(componentInChildren, 10f);
				num = Mathf.Max(num, textBtnWidth);
			}
			bool flag4 = num > 0f;
			if (flag4)
			{
				this.UpdateRowLastWidth(num);
			}
		}

		public void AddNewLine()
		{
			bool flag = this.rectTransform == null;
			if (flag)
			{
				this.rectTransform = base.GetComponent<RectTransform>();
			}
			bool flag2 = this.flowLayout == null;
			if (flag2)
			{
				this.flowLayout = base.GetComponent<FlowLayoutGroup>();
			}
			float workingRowWidth = this.flowLayout.WorkingRowWidth;
			float num = this.flowLayout.LastRowWidth;
			float num2 = workingRowWidth - num - 0.001f;
			num2 = Mathf.Max(num2, 0f);
			GameObject gameObject = RichTextGroup.PopPool("RichTextNewLine");
			bool flag3 = null == gameObject;
			if (flag3)
			{
				gameObject = new GameObject("RichTextNewLine", new Type[] { typeof(RectTransform) });
			}
			LayoutElement layoutElement = gameObject.AddComponent<LayoutElement>();
			layoutElement.preferredWidth = num2;
			gameObject.transform.SetParent(this.flowLayout.transform, false);
			this.childrens.Add(gameObject);
		}

		private void AddTextObject(string text, float width)
		{
			bool flag = string.IsNullOrEmpty(text) || width <= 0f;
			if (!flag)
			{
				GameObject gameObject = RichTextGroup.PopPool("RichTextItem");
				Text text2 = null;
				bool flag2 = null != gameObject;
				if (flag2)
				{
					text2 = gameObject.GetComponent<Text>();
				}
				bool flag3 = null == text2;
				if (flag3)
				{
					text2 = Object.Instantiate<Text>(this.textPrefab, base.transform, false);
					text2.name = "RichTextItem";
					gameObject = text2.gameObject;
				}
				else
				{
					text2.rectTransform.sizeDelta = this.textPrefab.rectTransform.sizeDelta;
					text2.fontSize = this.textPrefab.fontSize;
					text2.alignment = this.textPrefab.alignment;
					text2.font = this.textPrefab.font;
				}
				text2.horizontalOverflow = 1;
				text2.verticalOverflow = 1;
				text2.text = text;
				text2.transform.SetParent(this.flowLayout.transform, false);
				this.childrens.Add(gameObject);
				this.UpdateRowLastWidth(width);
			}
		}

		private float GetTextBtnWidth(Text textPre, float off_x = 0f)
		{
			Font font = textPre.font;
			string text = textPre.text;
			font.RequestCharactersInTexture(text, textPre.fontSize, textPre.fontStyle);
			float num = off_x;
			int i = 0;
			while (i < text.Length)
			{
				char c = text[i];
				bool flag = c == '<';
				if (!flag)
				{
					goto IL_00B2;
				}
				bool flag2 = i + 1 < text.Length && text[i + 1] != '/';
				if (flag2)
				{
					int num2 = text.IndexOf('>', i + 1);
					i = num2;
				}
				else
				{
					bool flag3 = i + 1 < text.Length && text[i + 1] == '/';
					if (!flag3)
					{
						goto IL_00B2;
					}
					int num3 = text.IndexOf('>', i + 1);
					i = num3;
				}
				IL_0103:
				i++;
				continue;
				IL_00B2:
				CharacterInfo characterInfo;
				bool flag4 = !font.GetCharacterInfo(c, ref characterInfo, textPre.fontSize, textPre.fontStyle);
				if (flag4)
				{
					Debug.LogWarningFormat("Can not get character info: {0}, {1}", new object[] { c, font.fontNames });
				}
				num += (float)characterInfo.advance;
				goto IL_0103;
			}
			return num;
		}

		private void UpdateRowLastWidth(float width)
		{
			bool flag = this.flowLayout;
			if (flag)
			{
				this.lastRowWidth += width + this.flowLayout.Spacing.x;
				bool flag2 = this.lastRowWidth > this.flowLayout.WorkingRowWidth;
				if (flag2)
				{
					this.lastRowWidth = width;
				}
			}
		}

		private void UpdateLayout()
		{
			bool flag = this.flowLayout;
			if (flag)
			{
			}
		}

		[SerializeField]
		private Text textPrefab;

		private RectTransform rectTransform;

		private FlowLayoutGroup flowLayout;

		private List<GameObject> childrens = new List<GameObject>();

		private float lastRowWidth = 0f;

		private static Transform PoolTransform;

		private static Action<GameObject> BackPoolCallback;

		private static int NewLineCacheCount = 0;

		private static List<GameObject> NewLineCache = new List<GameObject>();

		private static int TextItemCacheCount = 0;

		private static List<GameObject> TextChache = new List<GameObject>();
	}
}
