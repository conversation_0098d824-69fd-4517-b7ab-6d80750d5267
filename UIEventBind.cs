﻿using System;
using UnityEngine;

namespace Nirvana
{
	[ExecuteInEditMode]
	public abstract class UIEventBind : MonoBehaviour
	{
		public UIEventTable EventTable { get; private set; }

		internal Signal FindEvent(string name)
		{
			bool flag = this.EventTable != null;
			Signal signal;
			if (flag)
			{
				Signal eventSignal = this.EventTable.GetEventSignal(name);
				signal = eventSignal;
			}
			else
			{
				signal = null;
			}
			return signal;
		}

		protected void Awake()
		{
			this.RefreshEventTable();
		}

		private void RefreshEventTable()
		{
			bool flag = this.eventTable == null;
			if (flag)
			{
				this.eventTable = this.GetComponentInParentHard<UIEventTable>();
			}
			this.EventTable = this.eventTable;
		}

		[SerializeField]
		[Tooltip("The event table for this bind.")]
		private UIEventTable eventTable;
	}
}
