﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class WaitSpawnGameObject : CustomYieldInstruction
	{
		public WaitSpawnGameObject(GameObjectCache cache, InstantiateQueue instantiateQueue, int instantiatePriority)
		{
			this.cache = cache;
			this.instantiateQueue = instantiateQueue;
			this.instantiatePriority = instantiatePriority;
		}

		~WaitSpawnGameObject()
		{
			this.cache.Loading = false;
		}

		public string Error { get; private set; }

		public GameObject Instance
		{
			get
			{
				bool flag = !this.cache.HasPrefab;
				GameObject gameObject;
				if (flag)
				{
					gameObject = null;
				}
				else
				{
					bool flag2 = this.instantiateQueue == null && this.instance == null;
					if (flag2)
					{
						this.instance = this.cache.Spawn(null);
					}
					this.cache.Loading = false;
					gameObject = this.instance;
				}
				return gameObject;
			}
		}

		public override bool keepWaiting
		{
			get
			{
				bool flag = !string.IsNullOrEmpty(this.cache.Error);
				bool flag2;
				if (flag)
				{
					this.cache.Loading = false;
					this.Error = this.cache.Error;
					flag2 = false;
				}
				else
				{
					bool flag3 = !this.cache.HasPrefab;
					if (flag3)
					{
						flag2 = true;
					}
					else
					{
						bool flag4 = this.instantiateQueue != null;
						if (flag4)
						{
							bool flag5 = !this.instantiating;
							if (flag5)
							{
								this.instantiating = true;
								this.cache.Spawn(this.instantiateQueue, this.instantiatePriority, delegate(GameObject go)
								{
									this.instance = go;
								});
							}
							bool flag6 = this.instance == null;
							if (flag6)
							{
								return true;
							}
						}
						flag2 = false;
					}
				}
				return flag2;
			}
		}

		private GameObjectCache cache;

		private GameObject instance;

		private InstantiateQueue instantiateQueue;

		private int instantiatePriority;

		private bool instantiating;
	}
}
