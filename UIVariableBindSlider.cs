﻿using System;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Slider")]
	[RequireComponent(typeof(Slider))]
	public sealed class UIVariableBindSlider : UIVariableBind
	{
		public float TweenSpeed
		{
			get
			{
				return this.tweenSpeed;
			}
			set
			{
				this.tweenSpeed = value;
			}
		}

		public override void BindVariables()
		{
			Assert.IsNull<UIVariable>(this.valueVariable);
			bool flag = !string.IsNullOrEmpty(this.valueBind);
			if (flag)
			{
				this.valueVariable = base.FindVariable(this.valueBind);
				bool flag2 = this.valueVariable == null;
				if (flag2)
				{
					UIVariableBindSlider.logger.LogWarning("{0} can not find a variable {1}", new object[] { base.name, this.valueBind });
				}
				else
				{
					this.valueVariable.OnValueInitialized += this.OnValueInitialized;
					this.valueVariable.OnValueChanged += this.OnValueChanged;
					this.OnValueInitialized();
				}
			}
		}

		public override void UnbindVariables()
		{
			bool flag = this.valueVariable != null;
			if (flag)
			{
				this.valueVariable.OnValueInitialized -= this.OnValueInitialized;
				this.valueVariable.OnValueChanged -= this.OnValueChanged;
				this.valueVariable = null;
			}
		}

		private void OnValueInitialized()
		{
			bool flag = this.slider == null;
			if (flag)
			{
				this.slider = base.GetComponent<Slider>();
			}
			bool flag2 = this.slider != null;
			if (flag2)
			{
				float @float = this.valueVariable.GetFloat();
				this.targetValue = @float;
				this.slider.value = @float;
				this.playingTween = false;
			}
		}

		private void OnValueChanged()
		{
			bool flag = this.slider == null;
			if (flag)
			{
				this.slider = base.GetComponent<Slider>();
			}
			bool flag2 = this.slider != null;
			if (flag2)
			{
				float @float = this.valueVariable.GetFloat();
				bool flag3 = this.tweenSpeed > 0f && Application.isPlaying;
				if (flag3)
				{
					this.targetValue = @float;
					this.playingTween = true;
				}
				else
				{
					this.targetValue = @float;
					this.slider.value = @float;
					this.playingTween = false;
				}
			}
		}

		private void Update()
		{
			bool flag = this.playingTween && this.tweenSpeed > 0f && !Mathf.Approximately(this.slider.value, this.targetValue);
			if (flag)
			{
				switch (this.tweenType)
				{
				case UIVariableBindSlider.TweenType.IncreaseOnly:
					this.UpdateIncreaseOnly();
					break;
				case UIVariableBindSlider.TweenType.DecreaseOnly:
					this.UpdateDecreaseOnly();
					break;
				case UIVariableBindSlider.TweenType.DoubleWay:
					this.UpdateDoubleWay();
					break;
				}
			}
		}

		private void UpdateIncreaseOnly()
		{
			bool flag = this.targetValue > this.slider.value;
			if (flag)
			{
				this.UpdateDoubleWay();
			}
			else
			{
				float num = this.tweenSpeed * Time.deltaTime;
				float num2 = this.slider.value + num;
				bool flag2 = num2 >= this.slider.maxValue;
				if (flag2)
				{
					this.slider.value = this.slider.minValue;
				}
				else
				{
					this.slider.value = num2;
				}
			}
		}

		private void UpdateDecreaseOnly()
		{
			bool flag = this.targetValue < this.slider.value;
			if (flag)
			{
				this.UpdateDoubleWay();
			}
			else
			{
				float num = this.tweenSpeed * Time.deltaTime;
				float num2 = this.slider.value - num;
				bool flag2 = num2 <= this.slider.minValue;
				if (flag2)
				{
					this.slider.value = this.slider.maxValue;
				}
				else
				{
					this.slider.value = num2;
				}
			}
		}

		private void UpdateDoubleWay()
		{
			float num = this.targetValue - this.slider.value;
			float num2 = this.tweenSpeed * Time.deltaTime;
			bool flag = num2 > Mathf.Abs(num);
			if (flag)
			{
				this.slider.value = this.targetValue;
				this.playingTween = false;
			}
			else
			{
				bool flag2 = num > 0f;
				if (flag2)
				{
					this.slider.value += num2;
				}
				else
				{
					this.slider.value -= num2;
				}
			}
		}

		private new void Awake()
		{
			base.Awake();
			this.slider = base.GetComponent<Slider>();
			bool flag = this.slider == null;
			if (flag)
			{
			}
		}

		private static Logger logger = LogSystem.GetLogger("UIVariableBindSlider");

		[SerializeField]
		[VariableName(UIVariableType.Float)]
		private string valueBind;

		[SerializeField]
		public float tweenSpeed = 0f;

		[SerializeField]
		public UIVariableBindSlider.TweenType tweenType = UIVariableBindSlider.TweenType.IncreaseOnly;

		private Slider slider;

		private UIVariable valueVariable;

		private float targetValue;

		private bool playingTween = false;

		public enum TweenType
		{
			IncreaseOnly,
			DecreaseOnly,
			DoubleWay
		}
	}
}
