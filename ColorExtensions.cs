﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class ColorExtensions
	{
		public static Color[] SetAlpha(this Color[] colors, float alpha)
		{
			for (int i = 0; i < colors.Length; i++)
			{
				colors[i].a = alpha;
			}
			return colors;
		}

		public static Color SetAlpha(this Color color, float alpha)
		{
			color.a = alpha;
			return color;
		}

		public static Color Clamp(this Color c)
		{
			for (int i = 0; i < 4; i++)
			{
				bool flag = float.IsNaN(c[i]) || float.IsNegativeInfinity(c[i]);
				if (flag)
				{
					c[i] = 0f;
				}
				else
				{
					bool flag2 = float.IsPositiveInfinity(c[i]);
					if (flag2)
					{
						c[i] = 1f;
					}
					else
					{
						c[i] = Mathf.Clamp(c[i], 0f, 1f);
					}
				}
			}
			return c;
		}

		public static float Luminance(this Color c)
		{
			return 0.3f * c.r + 0.59f * c.g + 0.11f * c.b;
		}

		public static Color LerpRGB(Color a, Color b, float t)
		{
			return new Color(Mathf.Lerp(a.r, b.r, t), <PERSON><PERSON><PERSON>(a.g, b.g, t), <PERSON><PERSON><PERSON>(a.b, b.b, t), a.a);
		}
	}
}
