﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class RendererMaterialCache : Singleton<RendererMaterialCache>
	{
		internal Material GetMaterial(Material origin, int renderQueue, ShaderKeywords keywords)
		{
			RendererMaterialCache.CacheKey cacheKey;
			cacheKey.MaterialID = origin.GetInstanceID();
			cacheKey.RenderQueue = renderQueue;
			cacheKey.Keywords = keywords;
			WeakReference weakReference;
			bool flag = this.cache.TryGetValue(cacheKey, out weakReference);
			if (flag)
			{
				bool isAlive = weakReference.IsAlive;
				if (isAlive)
				{
					return (Material)weakReference.Target;
				}
				this.cache.Remove(cacheKey);
			}
			Material material = RendererMaterialCache.CreateMaterial(origin, renderQueue, keywords);
			this.cache.Add(cacheKey, new WeakReference(material));
			return material;
		}

		private static Material CreateMaterial(Material origin, int renderQueue, ShaderKeywords keywords)
		{
			Material material = new Material(origin);
			material.hideFlags = 52;
			bool flag = renderQueue != -1;
			if (flag)
			{
				material.renderQueue = renderQueue;
			}
			foreach (int num in keywords)
			{
				string keywordName = ShaderKeywords.GetKeywordName(num);
				material.EnableKeyword(keywordName);
			}
			return material;
		}

		private Dictionary<RendererMaterialCache.CacheKey, WeakReference> cache = new Dictionary<RendererMaterialCache.CacheKey, WeakReference>(new RendererMaterialCache.CacheKeyComparer());

		private struct CacheKey
		{
			public int MaterialID;

			public int RenderQueue;

			public ShaderKeywords Keywords;
		}

		private class CacheKeyComparer : IEqualityComparer<RendererMaterialCache.CacheKey>
		{
			public bool Equals(RendererMaterialCache.CacheKey x, RendererMaterialCache.CacheKey y)
			{
				bool flag = x.MaterialID != y.MaterialID;
				bool flag2;
				if (flag)
				{
					flag2 = false;
				}
				else
				{
					bool flag3 = x.RenderQueue != y.RenderQueue;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						bool flag4 = !x.Keywords.Equals(y.Keywords);
						flag2 = !flag4;
					}
				}
				return flag2;
			}

			public int GetHashCode(RendererMaterialCache.CacheKey obj)
			{
				int num = obj.MaterialID.GetHashCode();
				int num2 = 397 * num;
				int renderQueue = obj.RenderQueue;
				num = num2 ^ renderQueue.GetHashCode();
				return (397 * num) ^ obj.Keywords.GetHashCode();
			}
		}
	}
}
