﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	internal static class UIVertexListPool
	{
		internal static List<UIVertex> Get()
		{
			bool flag = UIVertexListPool.list != null;
			List<UIVertex> list2;
			if (flag)
			{
				List<UIVertex> list = UIVertexListPool.list;
				UIVertexListPool.list = null;
				list2 = list;
			}
			else
			{
				list2 = new List<UIVertex>();
			}
			return list2;
		}

		internal static void Release(List<UIVertex> l)
		{
			bool flag = UIVertexListPool.list == null;
			if (flag)
			{
				UIVertexListPool.list = l;
			}
		}

		private static List<UIVertex> list;
	}
}
