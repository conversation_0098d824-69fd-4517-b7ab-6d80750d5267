﻿using System;
using UnityEngine;

namespace Nirvana
{
	internal static class DiagnosticsGUI
	{
		private static Texture2D CloseTexture
		{
			get
			{
				bool flag = DiagnosticsGUI.closeTexture == null;
				if (flag)
				{
					DiagnosticsGUI.closeTexture = TextureMaker.Cross(22, 4, Color.white, Color.clear);
				}
				return DiagnosticsGUI.closeTexture;
			}
		}

		private static Texture2D[] AnchorTab
		{
			get
			{
				bool flag = DiagnosticsGUI.anchorTab == null;
				if (flag)
				{
					DiagnosticsGUI.anchorTab = new Texture2D[]
					{
						TextureMaker.Monochromatic(16, Color.clear),
						TextureMaker.CornerTopLeft(16, Color.white, Color.clear),
						TextureMaker.CornerTopRight(16, Color.white, Color.clear),
						TextureMaker.CornerBottomLeft(16, Color.white, Color.clear),
						TextureMaker.CornerBottomRight(16, Color.white, Color.clear)
					};
				}
				return DiagnosticsGUI.anchorTab;
			}
		}

		private static Texture2D ChartBarTexture
		{
			get
			{
				bool flag = DiagnosticsGUI.chartBarTexture == null;
				if (flag)
				{
					DiagnosticsGUI.chartBarTexture = TextureMaker.Gray(1f);
				}
				return DiagnosticsGUI.chartBarTexture;
			}
		}

		public static int Selection(int index, string label, string[] options)
		{
			GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
			GUILayout.Label(label, Array.Empty<GUILayoutOption>());
			int num = GUILayout.Toolbar(index, options, DiagnosticsSkin.BenchTabbar, Array.Empty<GUILayoutOption>());
			GUILayout.EndHorizontal();
			return num;
		}

		public static bool Toggle(bool toggle, string label)
		{
			GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
			GUILayout.Label(label, Array.Empty<GUILayoutOption>());
			int num = GUILayout.Toolbar(toggle ? 0 : 1, DiagnosticsGUI.ToggleTab, DiagnosticsSkin.BenchToggle, new GUILayoutOption[] { GUILayout.Width(200f) });
			GUILayout.EndHorizontal();
			return num == 0;
		}

		public static float SliderLabel(float sliderValue, string label, float sliderMinValue, float sliderMaxValue)
		{
			GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
			GUILayout.Label(label, new GUILayoutOption[] { GUILayout.Width(200f) });
			sliderValue = GUILayout.HorizontalSlider(sliderValue, sliderMinValue, sliderMaxValue, DiagnosticsSkin.BenchSlider, DiagnosticsSkin.BenchSliderThumb, Array.Empty<GUILayoutOption>());
			GUILayout.Space(10f);
			GUILayout.Label(string.Format("{0:F2}", sliderValue), new GUILayoutOption[] { GUILayout.ExpandWidth(false) });
			GUILayout.EndHorizontal();
			return sliderValue;
		}

		public static bool CloseButton()
		{
			return GUILayout.Button(DiagnosticsGUI.CloseTexture, DiagnosticsSkin.CloseButton, Array.Empty<GUILayoutOption>());
		}

		public static void ChartPillar(float length, float maxLength)
		{
			GUILayout.BeginVertical(new GUILayoutOption[] { GUILayout.Height(maxLength) });
			GUILayout.Space(maxLength - length);
			GUILayout.Label(string.Empty, DiagnosticsSkin.ChartPillar, new GUILayoutOption[] { GUILayout.Height(length) });
			GUILayout.EndVertical();
		}

		public static void Progress(float length, float maxLength, float height)
		{
			GUILayout.BeginHorizontal(DiagnosticsSkin.ProgressBackground, new GUILayoutOption[]
			{
				GUILayout.Width(maxLength),
				GUILayout.Height(height)
			});
			GUILayout.Label(string.Empty, DiagnosticsSkin.Progress, new GUILayoutOption[]
			{
				GUILayout.Width(length),
				GUILayout.Height(height)
			});
			GUILayout.Space(maxLength - length);
			GUILayout.EndHorizontal();
		}

		internal static DiagnosticsDockAnchor Anchor(DiagnosticsDockAnchor anchor, string label)
		{
			GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
			GUILayout.Label(label, Array.Empty<GUILayoutOption>());
			Texture[] array = DiagnosticsGUI.AnchorTab;
			int num = GUILayout.Toolbar((int)anchor, array, DiagnosticsSkin.AnchorToolbar, Array.Empty<GUILayoutOption>());
			GUILayout.EndHorizontal();
			return (DiagnosticsDockAnchor)num;
		}

		private static readonly string[] ToggleTab = new string[] { "ON", "OFF" };

		private static Texture2D closeTexture;

		private static Texture2D[] anchorTab;

		private static Texture2D chartBarTexture;
	}
}
