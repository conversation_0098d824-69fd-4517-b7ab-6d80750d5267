﻿using System;
using UnityEngine;

namespace Nirvana
{
	public abstract class AbstractSceneCell
	{
		public float HeightLT
		{
			get
			{
				return this.heightLT;
			}
			set
			{
				this.heightLT = value;
			}
		}

		public float HeightRT
		{
			get
			{
				return this.heightRT;
			}
			set
			{
				this.heightRT = value;
			}
		}

		public float HeightLB
		{
			get
			{
				return this.heightLB;
			}
			set
			{
				this.heightLB = value;
			}
		}

		public float HeightRB
		{
			get
			{
				return this.heightRB;
			}
			set
			{
				this.heightRB = value;
			}
		}

		[SerializeField]
		[Tooltip("The height of left top corner.")]
		private float heightLT;

		[SerializeField]
		[Tooltip("The height of right top corner.")]
		private float heightRT;

		[SerializeField]
		[Tooltip("The height of left bottom corner.")]
		private float heightLB;

		[SerializeField]
		[Tooltip("The height of right bottom corner.")]
		private float heightRB;
	}
}
