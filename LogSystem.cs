﻿using System;
using System.Collections.Generic;

namespace Nirvana
{
	public static class LogSystem
	{
		public static bool IsLogAct(LogActType logType)
		{
			return (LogSystem.logFlag & (1 << (int)logType)) != 0;
		}

		public static void SetLogAct(LogActType logType, bool act)
		{
			if (act)
			{
				LogSystem.logFlag |= 1 << (int)logType;
			}
			else
			{
				LogSystem.logFlag &= ~(1 << (int)logType);
			}
		}

		public static void UnActAllLog()
		{
			LogSystem.logFlag = 0;
		}

		public static Logger GetLogger(string moduleName)
		{
			Dictionary<string, Logger> dictionary = LogSystem.loggers;
			Logger logger2;
			lock (dictionary)
			{
				Logger logger;
				bool flag2 = LogSystem.loggers.TryGetValue(moduleName, out logger);
				if (flag2)
				{
					logger2 = logger;
				}
				else
				{
					logger = new Logger(LogSystem.destination, moduleName);
					LogSystem.loggers.Add(moduleName, logger);
					logger2 = logger;
				}
			}
			return logger2;
		}

		public static void AddAppender(ILogAppender appender)
		{
			LogSystem.destination.AddAppender(appender);
		}

		public static bool RemoveAppender(ILogAppender appender)
		{
			return LogSystem.destination.RemoveAppender(appender);
		}

		public static void ClearAppenders()
		{
			LogSystem.destination.ClearAppenders();
		}

		private static LogDestination destination = new LogDestination();

		private static Dictionary<string, Logger> loggers = new Dictionary<string, Logger>(StringComparer.Ordinal);

		private static int logFlag = 0;
	}
}
