﻿using System;
using System.Collections.Generic;
using System.IO;
using NGettext;
using UnityEngine;

namespace Nirvana
{
	public static class I18N
	{
		public static I18NCatalog Catalog { get; set; }

		public static SystemLanguage Language
		{
			get
			{
				bool flag = I18N.language == 42;
				if (flag)
				{
					I18N.language = Application.systemLanguage;
				}
				return I18N.language;
			}
			set
			{
				bool flag = I18N.language != value;
				if (flag)
				{
					I18N.language = value;
					I18N.OnLanguageChanged();
				}
			}
		}

		public static LinkedListNode<Action> ListenLanguageChanged(Action action)
		{
			return I18N.onLanguageChanged.AddLast(action);
		}

		public static void UnlistenLanguageChanged(LinkedListNode<Action> handle)
		{
			I18N.onLanguageChanged.Remove(handle);
		}

		public static void LoadMO(byte[] bytes)
		{
			using (MemoryStream memoryStream = new MemoryStream(bytes))
			{
				I18N.catalog = new Catalog(memoryStream);
			}
		}

		public static string GetString(string text)
		{
			bool flag = I18N.catalog != null;
			string text2;
			if (flag)
			{
				text2 = I18N.catalog.GetString(text);
			}
			else
			{
				text2 = text;
			}
			return text2;
		}

		public static string GetString(string text, params object[] args)
		{
			bool flag = I18N.catalog != null;
			string text2;
			if (flag)
			{
				text2 = I18N.catalog.GetString(text, args);
			}
			else
			{
				text2 = string.Format(text, args);
			}
			return text2;
		}

		public static string GetPluralString(string text, string pluralText, long n)
		{
			bool flag = I18N.catalog != null;
			string text2;
			if (flag)
			{
				text2 = I18N.catalog.GetPluralString(text, pluralText, n);
			}
			else
			{
				text2 = text;
			}
			return text2;
		}

		public static string GetPluralString(string text, string pluralText, long n, params object[] args)
		{
			bool flag = I18N.catalog != null;
			string text2;
			if (flag)
			{
				text2 = I18N.catalog.GetPluralString(text, pluralText, n, args);
			}
			else
			{
				text2 = string.Format(text, args);
			}
			return text2;
		}

		public static string GetParticularString(string context, string text)
		{
			bool flag = I18N.catalog != null;
			string text2;
			if (flag)
			{
				text2 = I18N.catalog.GetParticularString(context, text);
			}
			else
			{
				text2 = text;
			}
			return text2;
		}

		public static string GetParticularString(string context, string text, params object[] args)
		{
			bool flag = I18N.catalog != null;
			string text2;
			if (flag)
			{
				text2 = I18N.catalog.GetParticularString(context, text, args);
			}
			else
			{
				text2 = string.Format(text, args);
			}
			return text2;
		}

		public static string GetParticularPluralString(string context, string text, string pluralText, long n)
		{
			bool flag = I18N.catalog != null;
			string text2;
			if (flag)
			{
				text2 = I18N.catalog.GetParticularPluralString(context, text, pluralText, n);
			}
			else
			{
				text2 = text;
			}
			return text2;
		}

		public static string GetParticularPluralString(string context, string text, string pluralText, long n, params object[] args)
		{
			bool flag = I18N.catalog != null;
			string text2;
			if (flag)
			{
				text2 = I18N.catalog.GetParticularPluralString(context, text, pluralText, n, args);
			}
			else
			{
				text2 = string.Format(text, args);
			}
			return text2;
		}

		private static void OnLanguageChanged()
		{
			bool flag = I18N.Catalog == null;
			if (flag)
			{
				I18N.logger.LogError("Can not find i18n catalog.");
				I18N.NotifyChanged();
			}
			else
			{
				bool flag2;
				AssetID assetID = I18N.Catalog.FindCatalog(I18N.language, out flag2);
				bool flag3 = !flag2;
				if (flag3)
				{
					I18N.logger.LogError("Can not find the language {0} in catalog table.", new object[] { I18N.language });
					I18N.NotifyChanged();
				}
				else
				{
					bool isEmpty = assetID.IsEmpty;
					if (isEmpty)
					{
						I18N.catalog = null;
						I18N.NotifyChanged();
					}
					else
					{
						I18N.LoadCatalogAsset(I18N.Language, assetID);
					}
				}
			}
		}

		private static void LoadCatalogAsset(SystemLanguage language, AssetID asset)
		{
			AssetManager.LoadObject(asset, typeof(TextAsset), delegate(Object obj)
			{
				bool flag = language == I18N.Language;
				if (flag)
				{
					I18N.OnCatalogAssetLoaded(asset, (TextAsset)obj);
				}
			});
		}

		private static void OnCatalogAssetLoaded(AssetID asset, TextAsset catalogAsset)
		{
			bool flag = catalogAsset == null;
			if (flag)
			{
				I18N.logger.LogError("Load catalog table {0} failed.", new object[] { asset });
				I18N.NotifyChanged();
			}
			else
			{
				I18N.LoadMO(catalogAsset.bytes);
				I18N.NotifyChanged();
			}
		}

		private static void NotifyChanged()
		{
			LinkedListNode<Action> next;
			for (LinkedListNode<Action> linkedListNode = I18N.onLanguageChanged.First; linkedListNode != null; linkedListNode = next)
			{
				next = linkedListNode.Next;
				Action value = linkedListNode.Value;
				try
				{
					value();
				}
				catch (Exception ex)
				{
					I18N.logger.LogError(ex.ToString());
				}
			}
		}

		private static Logger logger = LogSystem.GetLogger("I18N");

		private static ICatalog catalog;

		private static SystemLanguage language = 42;

		private static LinkedList<Action> onLanguageChanged = new LinkedList<Action>();
	}
}
