﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("UI/Radar Chart Polygon")]
	public sealed class RadarChartPolygon : Graphic
	{
		public void SetVolume(int index, float value)
		{
			bool flag = this.volumes.Length < index;
			if (flag)
			{
				Array.Resize<float>(ref this.volumes, index + 1);
			}
			this.volumes[index] = value;
			this.SetVerticesDirty();
		}

		public float GetVolume(int index)
		{
			bool flag = this.volumes.Length - 1 < index;
			float num;
			if (flag)
			{
				num = 0f;
			}
			else
			{
				float num2 = this.volumes[index];
				num = ((num2 > this.maxVolume) ? this.maxVolume : num2);
			}
			return num;
		}

		protected override void OnPopulateMesh(VertexHelper vh)
		{
			vh.Clear();
			bool flag = this.volumes == null || this.maxVolume == 0f;
			if (!flag)
			{
				UIVertex simpleVert = UIVertex.simpleVert;
				simpleVert.color = this.color;
				Vector2 vector = this.CreatePos(0.5f, 0.5f);
				simpleVert.position = vector;
				vh.AddVert(simpleVert);
				int num = this.verticesCount;
				float num2 = 360f / (float)num;
				for (int i = 0; i < num; i++)
				{
					float num3 = (90f - num2 * (float)i) * 0.017453292f;
					float num4 = 0.5f * this.GetVolume(i);
					float num5 = 0.5f + Mathf.Cos(num3) * num4;
					float num6 = 0.5f + Mathf.Sin(num3) * num4;
					Vector2 vector2 = this.CreatePos(num5, num6);
					simpleVert.position = vector2;
					vh.AddVert(simpleVert);
					vh.AddTriangle(0, i + 1, (i + 1) % num + 1);
				}
			}
		}

		private Vector2 CreatePos(float x, float y)
		{
			Vector2 pivot = base.rectTransform.pivot;
			Rect rect = base.rectTransform.rect;
			Vector2 zero = Vector2.zero;
			zero.x -= pivot.x;
			zero.y -= pivot.y;
			zero.x += x;
			zero.y += y;
			zero.x *= rect.width;
			zero.y *= rect.height;
			return zero;
		}

		private const float Radius = 0.5f;

		[SerializeField]
		[Range(3f, 30f)]
		private int verticesCount = 5;

		[SerializeField]
		[Range(0f, 1f)]
		private float maxVolume = 1f;

		[SerializeField]
		private float[] volumes;
	}
}
