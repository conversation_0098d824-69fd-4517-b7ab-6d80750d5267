﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	[DisallowMultipleComponent]
	public sealed class Scheduler : MonoBehaviour
	{
		private Scheduler()
		{
		}

		private static Scheduler Instance
		{
			get
			{
				Scheduler.CheckInstance();
				return Scheduler.instance;
			}
		}

		public static void Clear()
		{
			Scheduler.frameTasks.Clear();
			Scheduler.executing.Clear();
			Scheduler.postTasks.Clear();
			Scheduler.nextFrameTasks.Clear();
			Scheduler.delayTasks.Clear();
		}

		public static LinkedListNode<Action> AddFrameListener(Action action)
		{
			return Scheduler.frameTasks.AddLast(action);
		}

		public static void RemoveFrameListener(LinkedListNode<Action> handle)
		{
			Scheduler.frameTasks.Remove(handle);
		}

		public static Coroutine RunCoroutine(IEnumerator coroutine)
		{
			return Scheduler.Instance.StartCoroutine(coroutine);
		}

		public static void StopShcCoroutine(Coroutine coroutine)
		{
			Scheduler.Instance.StopCoroutine(coroutine);
		}

		public static void PostTask(Action task)
		{
			List<Action> list = Scheduler.postTasks;
			lock (list)
			{
				Scheduler.postTasks.Add(task);
			}
		}

		public static void Delay(Action task)
		{
			Scheduler.nextFrameTasks.Add(task);
		}

		public static void Delay(Action task, float time)
		{
			Scheduler.DelayTime delayTime = default(Scheduler.DelayTime);
			delayTime.Task = task;
			delayTime.Time = Time.realtimeSinceStartup + time;
			Scheduler.delayTasks.Add(delayTime);
		}

		[RuntimeInitializeOnLoadMethod]
		private static void CheckInstance()
		{
			bool flag = Scheduler.instance == null && Application.isPlaying;
			if (flag)
			{
				GameObject gameObject = new GameObject("Scheduler", new Type[] { typeof(Scheduler) });
				Object.DontDestroyOnLoad(gameObject);
				Scheduler.instance = gameObject.GetComponent<Scheduler>();
			}
		}

		private void Awake()
		{
			Object.DontDestroyOnLoad(this);
		}

		private void OnDestroy()
		{
			Scheduler.frameTasks.Clear();
			Scheduler.executing.Clear();
			Scheduler.postTasks.Clear();
			Scheduler.nextFrameTasks.Clear();
			Scheduler.delayTasks.Clear();
		}

		private void Update()
		{
			LinkedListNode<Action> next;
			for (LinkedListNode<Action> linkedListNode = Scheduler.frameTasks.First; linkedListNode != null; linkedListNode = next)
			{
				next = linkedListNode.Next;
				Action value = linkedListNode.Value;
				try
				{
					value();
				}
				catch (Exception ex)
				{
					Scheduler.logger.LogError(ex.ToString());
				}
			}
			List<Action> list = Scheduler.postTasks;
			lock (list)
			{
				bool flag2 = Scheduler.postTasks.Count > 0;
				if (flag2)
				{
					for (int i = 0; i < Scheduler.postTasks.Count; i++)
					{
						Scheduler.executing.Add(Scheduler.postTasks[i]);
					}
					Scheduler.postTasks.Clear();
				}
			}
			bool flag3 = Scheduler.nextFrameTasks.Count > 0;
			if (flag3)
			{
				for (int j = 0; j < Scheduler.nextFrameTasks.Count; j++)
				{
					Scheduler.executing.Add(Scheduler.nextFrameTasks[j]);
				}
				Scheduler.nextFrameTasks.Clear();
			}
			bool flag4 = Scheduler.delayTasks.Count > 0;
			if (flag4)
			{
				float now = Time.realtimeSinceStartup;
				Scheduler.delayTasks.RemoveAll(delegate(Scheduler.DelayTime task)
				{
					bool flag5 = now >= task.Time;
					bool flag6;
					if (flag5)
					{
						Scheduler.executing.Add(task.Task);
						flag6 = true;
					}
					else
					{
						flag6 = false;
					}
					return flag6;
				});
			}
			this.Executing();
		}

		private void Executing()
		{
			for (int i = 0; i < Scheduler.executing.Count; i++)
			{
				Action action = Scheduler.executing[i];
				try
				{
					action();
				}
				catch (Exception ex)
				{
					Scheduler.logger.LogError(ex.ToString());
				}
			}
			Scheduler.executing.Clear();
		}

		private static Scheduler instance;

		private static Logger logger = LogSystem.GetLogger("Scheduler");

		private static LinkedList<Action> frameTasks = new LinkedList<Action>();

		private static List<Action> executing = new List<Action>();

		private static List<Action> postTasks = new List<Action>();

		private static List<Action> nextFrameTasks = new List<Action>();

		private static List<Scheduler.DelayTime> delayTasks = new List<Scheduler.DelayTime>();

		private struct DelayTime
		{
			public float Time;

			public Action Task;
		}
	}
}
