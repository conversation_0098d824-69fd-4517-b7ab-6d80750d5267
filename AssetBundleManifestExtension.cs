﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class AssetBundleManifestExtension
	{
		public static string CalculateVersion(this AssetBundleManifest manifest)
		{
			int num = 397;
			string[] allAssetBundles = manifest.GetAllAssetBundles();
			foreach (string text in allAssetBundles)
			{
				Hash128 assetBundleHash = manifest.GetAssetBundleHash(text);
				num = (397 * num) ^ assetBundleHash.GetHashCode();
			}
			string[] allAssetBundlesWithVariant = manifest.GetAllAssetBundlesWithVariant();
			foreach (string text2 in allAssetBundlesWithVariant)
			{
				Hash128 assetBundleHash2 = manifest.GetAssetBundleHash(text2);
				num = (397 * num) ^ assetBundleHash2.GetHashCode();
			}
			return num.ToString("X4");
		}
	}
}
