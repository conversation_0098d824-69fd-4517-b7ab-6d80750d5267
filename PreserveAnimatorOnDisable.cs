﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	[RequireComponent(typeof(Animator))]
	public sealed class PreserveAnimatorOnDisable : MonoBehaviour
	{
		private void Start()
		{
			this.animator = base.GetComponent<Animator>();
		}

		private void OnDisable()
		{
			bool flag = this.animator == null || this.animator.runtimeAnimatorController == null;
			if (!flag)
			{
				foreach (AnimatorControllerParameter animatorControllerParameter in this.animator.parameters)
				{
					PreserveAnimatorOnDisable.AnimParam animParam = default(PreserveAnimatorOnDisable.AnimParam);
					animParam.Type = animatorControllerParameter.type;
					animParam.NameHash = animatorControllerParameter.nameHash;
					switch (animatorControllerParameter.type)
					{
					case 1:
						animParam.Data = this.animator.GetFloat(animatorControllerParameter.nameHash);
						break;
					case 3:
						animParam.Data = this.animator.GetInteger(animatorControllerParameter.nameHash);
						break;
					case 4:
						animParam.Data = this.animator.GetBool(animatorControllerParameter.nameHash);
						break;
					}
					this.parameters.Add(animParam);
				}
				int layerCount = this.animator.layerCount;
				for (int j = 0; j < layerCount; j++)
				{
					AnimatorStateInfo currentAnimatorStateInfo = this.animator.GetCurrentAnimatorStateInfo(j);
					this.layerState.Add(currentAnimatorStateInfo.shortNameHash);
				}
			}
		}

		private void OnEnable()
		{
			foreach (PreserveAnimatorOnDisable.AnimParam animParam in this.parameters)
			{
				switch (animParam.Type)
				{
				case 1:
					this.animator.SetFloat(animParam.NameHash, (float)animParam.Data);
					break;
				case 3:
					this.animator.SetInteger(animParam.NameHash, (int)animParam.Data);
					break;
				case 4:
					this.animator.SetBool(animParam.NameHash, (bool)animParam.Data);
					break;
				}
			}
			this.parameters.Clear();
			int num = 0;
			foreach (int num2 in this.layerState)
			{
				this.animator.Play(num2, num++);
			}
			this.layerState.Clear();
		}

		private Animator animator;

		private List<PreserveAnimatorOnDisable.AnimParam> parameters = new List<PreserveAnimatorOnDisable.AnimParam>();

		private List<int> layerState = new List<int>();

		private struct AnimParam
		{
			public AnimatorControllerParameterType Type;

			public int NameHash;

			public object Data;
		}
	}
}
