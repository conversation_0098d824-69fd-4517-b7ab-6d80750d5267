﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Render/Sorting Order Overrider")]
	public sealed class SortingOrderOverrider : MonoBehaviour
	{
		public int SortingOrder
		{
			get
			{
				return this.sortingOrder;
			}
			set
			{
				this.sortingOrder = value;
				this.UpdateSortingOrder();
			}
		}

		private void Awake()
		{
			this.renderers = base.GetComponentsInChildren<Renderer>(true);
			this.UpdateSortingOrder();
		}

		private void UpdateSortingOrder()
		{
			bool flag = this.renderers == null;
			if (!flag)
			{
				foreach (Renderer renderer in this.renderers)
				{
					renderer.sortingOrder = this.sortingOrder;
				}
			}
		}

		[SerializeField]
		[Tooltip("The sorting order you want to override.")]
		private int sortingOrder;

		private Renderer[] renderers;
	}
}
