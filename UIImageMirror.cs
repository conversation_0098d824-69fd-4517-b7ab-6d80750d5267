﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Image Mirror")]
	[RequireComponent(typeof(RectTransform))]
	public sealed class UIImageMirror : BaseMeshEffect, ILayoutElement
	{
		public UIImageMirror.MirrorModeType MirrorMode
		{
			get
			{
				return this.mirrorMode;
			}
			set
			{
				bool flag = this.mirrorMode != value;
				if (flag)
				{
					this.mirrorMode = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public float flexibleHeight
		{
			get
			{
				return -1f;
			}
		}

		public float flexibleWidth
		{
			get
			{
				return -1f;
			}
		}

		public int layoutPriority
		{
			get
			{
				return 0;
			}
		}

		public float minHeight
		{
			get
			{
				return 0f;
			}
		}

		public float minWidth
		{
			get
			{
				return 0f;
			}
		}

		public float preferredHeight
		{
			get
			{
				Image image = this.GetImage();
				bool flag = image == null;
				float num;
				if (flag)
				{
					num = 0f;
				}
				else
				{
					UIImageMirror.MirrorModeType mirrorModeType = this.mirrorMode;
					UIImageMirror.MirrorModeType mirrorModeType2 = mirrorModeType;
					if (mirrorModeType2 - UIImageMirror.MirrorModeType.Vertical > 1)
					{
						num = image.preferredHeight;
					}
					else
					{
						num = 2f * image.preferredHeight;
					}
				}
				return num;
			}
		}

		public float preferredWidth
		{
			get
			{
				Image image = this.GetImage();
				bool flag = image == null;
				float num;
				if (flag)
				{
					num = 0f;
				}
				else
				{
					UIImageMirror.MirrorModeType mirrorModeType = this.mirrorMode;
					UIImageMirror.MirrorModeType mirrorModeType2 = mirrorModeType;
					if (mirrorModeType2 != UIImageMirror.MirrorModeType.Horizontal && mirrorModeType2 != UIImageMirror.MirrorModeType.Quad)
					{
						num = image.preferredWidth;
					}
					else
					{
						num = 2f * image.preferredWidth;
					}
				}
				return num;
			}
		}

		public void CalculateLayoutInputHorizontal()
		{
		}

		public void CalculateLayoutInputVertical()
		{
		}

		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive() || vh.currentVertCount <= 0;
			if (!flag)
			{
				RectTransform rectTransform = base.transform as RectTransform;
				this.xy = rectTransform.rect.size;
				List<UIVertex> list = UIVertexListPool.Get();
				vh.GetUIVertexStream(list);
				List<UIVertex> list2 = this.Modify(list);
				bool flag2 = list2 != null;
				if (flag2)
				{
					vh.Clear();
					vh.AddUIVertexTriangleStream(list2);
				}
				UIVertexListPool.Release(list);
			}
		}

		protected override void OnEnable()
		{
			base.OnEnable();
			RectTransform rectTransform = base.transform as RectTransform;
			this.xy = rectTransform.sizeDelta;
			bool flag = base.graphic != null;
			if (flag)
			{
				base.graphic.SetVerticesDirty();
			}
		}

		private Image GetImage()
		{
			bool flag = this.image == null;
			if (flag)
			{
				this.image = base.GetComponent<Image>();
			}
			return this.image;
		}

		private List<UIVertex> Modify(List<UIVertex> verts)
		{
			Image image = this.GetImage();
			bool flag = image.type == 0;
			List<UIVertex> list;
			if (flag)
			{
				list = this.ModifySimple(verts);
			}
			else
			{
				list = verts;
			}
			return list;
		}

		private List<UIVertex> ModifySimple(List<UIVertex> verts)
		{
			int num = 0;
			int num2 = verts.Count;
			bool flag = this.mirrorMode == UIImageMirror.MirrorModeType.Horizontal;
			if (flag)
			{
				int num3 = verts.Count * 2;
				bool flag2 = verts.Capacity < num3;
				if (flag2)
				{
					verts.Capacity = num3;
				}
				this.ApplyMirrorDouble(verts, num, num2, 0f, 0f, -this.xy.x / 2f, 0f, false);
				num = num2;
				num2 = verts.Count;
				this.ApplyMirrorDouble(verts, num, num2, this.xy.x, 0f, -this.xy.x / 2f, 0f, true);
			}
			else
			{
				bool flag3 = this.mirrorMode == UIImageMirror.MirrorModeType.Vertical;
				if (flag3)
				{
					int num4 = verts.Count * 2;
					bool flag4 = verts.Capacity < num4;
					if (flag4)
					{
						verts.Capacity = num4;
					}
					this.ApplyMirrorDouble(verts, num, num2, 0f, 0f, 0f, this.xy.y / 2f, false);
					num = num2;
					num2 = verts.Count;
					this.ApplyMirrorDouble(verts, num, num2, 0f, -this.xy.y, 0f, this.xy.y / 2f, true);
				}
				else
				{
					int num5 = verts.Count * 4;
					bool flag5 = verts.Capacity < num5;
					if (flag5)
					{
						verts.Capacity = num5;
					}
					this.ApplyMirrorDouble(verts, num, num2, 0f, 0f, -this.xy.x / 2f, this.xy.y / 2f, false);
					num = num2;
					num2 = verts.Count;
					this.ApplyMirrorDouble(verts, num, num2, this.xy.x, 0f, -this.xy.x / 2f, this.xy.y / 2f, false);
					num = num2;
					num2 = verts.Count;
					this.ApplyMirrorDouble(verts, num, num2, 0f, -this.xy.y, -this.xy.x / 2f, this.xy.y / 2f, false);
					num = num2;
					num2 = verts.Count;
					this.ApplyMirrorDouble(verts, num, num2, this.xy.x, -this.xy.y, -this.xy.x / 2f, this.xy.y / 2f, true);
				}
			}
			return verts;
		}

		private void ApplyMirrorDouble(List<UIVertex> verts, int start, int end, float x1, float y1, float x2, float y2, bool self = false)
		{
			int num = verts.Count * 2;
			bool flag = verts.Capacity < num;
			if (flag)
			{
				verts.Capacity = num;
			}
			for (int i = start; i < end; i++)
			{
				UIVertex uivertex = verts[i];
				bool flag2 = !self;
				if (flag2)
				{
					verts.Add(uivertex);
				}
				Vector3 position = uivertex.position;
				int num2 = i % 6;
				switch (num2)
				{
				case 0:
				case 1:
				case 5:
					position.x += x1;
					break;
				case 2:
				case 3:
				case 4:
					position.x += x2;
					break;
				}
				switch (num2)
				{
				case 0:
				case 4:
				case 5:
					position.y += y2;
					break;
				case 1:
				case 2:
				case 3:
					position.y += y1;
					break;
				}
				uivertex.position = position;
				verts[i] = uivertex;
			}
		}

		[SerializeField]
		[Tooltip("The mirror type.")]
		private UIImageMirror.MirrorModeType mirrorMode = UIImageMirror.MirrorModeType.Horizontal;

		private Image image;

		private Vector2 xy;

		public enum MirrorModeType
		{
			Horizontal,
			Vertical,
			Quad
		}
	}
}
