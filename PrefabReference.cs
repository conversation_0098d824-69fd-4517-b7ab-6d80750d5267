﻿using System;
using UnityEngine;

namespace Nirvana
{
	public class PrefabReference
	{
		~PrefabReference()
		{
			this.TryRelease(true);
		}

		public void SetPrefab(GameObject prefab)
		{
			bool flag = this.prefab == prefab;
			if (!flag)
			{
				bool flag2 = null != this.prefab;
				if (flag2)
				{
					this.TryRelease(false);
				}
				this.prefab = prefab;
				this.TryRetain();
			}
		}

		private void TryRetain()
		{
			bool flag = null != this.prefab && !this.isRetained;
			if (flag)
			{
				this.isRetained = Singleton<PrefabPool>.Instance.Retain(this.prefab);
			}
		}

		private void TryRelease(bool finilized = false)
		{
			bool flag = this.isRetained && null != this.prefab;
			if (flag)
			{
				this.isRetained = false;
				if (finilized)
				{
					ReferenceDict.ReleasePrefab(this.prefab);
				}
				else
				{
					Singleton<PrefabPool>.Instance.Free(this.prefab, false);
				}
				this.prefab = null;
			}
		}

		private GameObject prefab;

		private bool isRetained = false;
	}
}
