﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	[DisallowMultipleComponent]
	public sealed class SdkScheduler : MonoBehaviour
	{
		private SdkScheduler()
		{
			SdkScheduler.frameTasks.Clear();
			SdkScheduler.executing.Clear();
			SdkScheduler.postTasks.Clear();
			SdkScheduler.nextFrameTasks.Clear();
			SdkScheduler.delayTasks.Clear();
		}

		private static SdkScheduler Instance
		{
			get
			{
				SdkScheduler.CheckInstance();
				return SdkScheduler.instance;
			}
		}

		public static LinkedListNode<Action> AddFrameListener(Action action)
		{
			return SdkScheduler.frameTasks.AddLast(action);
		}

		public static void RemoveFrameListener(LinkedListNode<Action> handle)
		{
			SdkScheduler.frameTasks.Remove(handle);
		}

		public static Coroutine RunCoroutine(IEnumerator coroutine)
		{
			return SdkScheduler.Instance.StartCoroutine(coroutine);
		}

		public static void PostTask(Action task)
		{
			List<Action> list = SdkScheduler.postTasks;
			List<Action> list2 = list;
			lock (list2)
			{
				SdkScheduler.postTasks.Add(task);
			}
		}

		public static void Delay(Action task)
		{
			SdkScheduler.nextFrameTasks.Add(task);
		}

		public static void Delay(Action task, float time)
		{
			SdkScheduler.DelayTime delayTime = default(SdkScheduler.DelayTime);
			delayTime.Task = task;
			delayTime.Time = Time.realtimeSinceStartup + time;
			SdkScheduler.delayTasks.Add(delayTime);
		}

		[RuntimeInitializeOnLoadMethod]
		private static void CheckInstance()
		{
			bool flag = SdkScheduler.instance == null && Application.isPlaying;
			if (flag)
			{
				GameObject gameObject = new GameObject("Scheduler", new Type[] { typeof(SdkScheduler) });
				Object.DontDestroyOnLoad(gameObject);
				SdkScheduler.instance = gameObject.GetComponent<SdkScheduler>();
			}
		}

		private void Awake()
		{
			Object.DontDestroyOnLoad(this);
		}

		private void OnDestroy()
		{
			SdkScheduler.frameTasks.Clear();
			SdkScheduler.executing.Clear();
			SdkScheduler.postTasks.Clear();
			SdkScheduler.nextFrameTasks.Clear();
			SdkScheduler.delayTasks.Clear();
		}

		private void Update()
		{
			LinkedListNode<Action> next;
			for (LinkedListNode<Action> linkedListNode = SdkScheduler.frameTasks.First; linkedListNode != null; linkedListNode = next)
			{
				next = linkedListNode.Next;
				Action value = linkedListNode.Value;
				try
				{
					value();
				}
				catch
				{
					SdkScheduler.frameTasks.Remove(linkedListNode);
				}
			}
			List<Action> list = SdkScheduler.postTasks;
			List<Action> list2 = list;
			lock (list2)
			{
				bool flag2 = SdkScheduler.postTasks.Count > 0;
				if (flag2)
				{
					for (int i = 0; i < SdkScheduler.postTasks.Count; i++)
					{
						SdkScheduler.executing.Add(SdkScheduler.postTasks[i]);
					}
					SdkScheduler.postTasks.Clear();
				}
			}
			bool flag3 = SdkScheduler.nextFrameTasks.Count > 0;
			if (flag3)
			{
				for (int j = 0; j < SdkScheduler.nextFrameTasks.Count; j++)
				{
					SdkScheduler.executing.Add(SdkScheduler.nextFrameTasks[j]);
				}
				SdkScheduler.nextFrameTasks.Clear();
			}
			bool flag4 = SdkScheduler.delayTasks.Count > 0;
			if (flag4)
			{
				float now = Time.realtimeSinceStartup;
				SdkScheduler.delayTasks.RemoveAll(delegate(SdkScheduler.DelayTime task)
				{
					bool flag5 = now >= task.Time;
					bool flag6;
					if (flag5)
					{
						SdkScheduler.executing.Add(task.Task);
						flag6 = true;
					}
					else
					{
						flag6 = false;
					}
					return flag6;
				});
			}
			this.Executing();
		}

		private void Executing()
		{
			for (int i = 0; i < SdkScheduler.executing.Count; i++)
			{
				Action action = SdkScheduler.executing[i];
				try
				{
					action();
				}
				catch
				{
				}
			}
			SdkScheduler.executing.Clear();
		}

		private static SdkScheduler instance;

		private static LinkedList<Action> frameTasks = new LinkedList<Action>();

		private static List<Action> executing = new List<Action>();

		private static List<Action> postTasks = new List<Action>();

		private static List<Action> nextFrameTasks = new List<Action>();

		private static List<SdkScheduler.DelayTime> delayTasks = new List<SdkScheduler.DelayTime>();

		private struct DelayTime
		{
			public float Time;

			public Action Task;
		}
	}
}
