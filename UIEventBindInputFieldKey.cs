﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Event Bind InputField Key")]
	[RequireComponent(typeof(InputField))]
	public sealed class UIEventBindInputFieldKey : UIEventBind
	{
		private new void Awake()
		{
			base.Awake();
			this.inputField = base.GetComponent<InputField>();
			bool flag = this.keyEvents != null;
			if (flag)
			{
				foreach (UIEventBindInputFieldKey.FocusKeyEvent focusKeyEvent in this.keyEvents)
				{
					focusKeyEvent.RefreshBind(this);
				}
			}
		}

		private void Update()
		{
			bool isFocused = this.inputField.isFocused;
			if (isFocused)
			{
				foreach (UIEventBindInputFieldKey.FocusKeyEvent focusKeyEvent in this.keyEvents)
				{
					focusKeyEvent.Update();
				}
			}
		}

		[SerializeField]
		public UIEventBindInputFieldKey.FocusKeyEvent[] keyEvents;

		private InputField inputField;

		[Serializable]
		public class FocusKeyEvent
		{
			public void RefreshBind(UIEventBind bind)
			{
				this.eventSignal = bind.FindEvent(this.eventName);
				bool flag = this.eventSignal == null;
				if (flag)
				{
					Debug.LogWarning("Can not find event: " + this.eventName);
				}
			}

			public void Update()
			{
				bool keyUp = Input.GetKeyUp(this.keyCode);
				if (keyUp)
				{
					bool flag = this.eventSignal != null;
					if (flag)
					{
						this.eventSignal.Invoke(Array.Empty<object>());
					}
				}
			}

			[EventName]
			[SerializeField]
			public string eventName;

			[SerializeField]
			public KeyCode keyCode;

			private Signal eventSignal;
		}
	}
}
