﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public abstract class Translatable : MonoBehaviour, ITranslatable
	{
		public string Context
		{
			get
			{
				return this.context;
			}
		}

		public string Text
		{
			get
			{
				return this.text;
			}
		}

		public string PluralText
		{
			get
			{
				return this.pluralText;
			}
		}

		public int Number { get; set; }

		protected abstract void TranslateText();

		protected string GetText()
		{
			bool flag = string.IsNullOrEmpty(this.pluralText);
			string text;
			if (flag)
			{
				text = I18N.GetParticularString(this.context, this.text);
			}
			else
			{
				text = I18N.GetParticularPluralString(this.context, this.text, this.pluralText, (long)this.Number);
			}
			return text;
		}

		private void Awake()
		{
			this.TranslateText();
		}

		private void OnEnable()
		{
			this.listenHandle = I18N.ListenLanguageChanged(new Action(this.TranslateText));
		}

		private void OnDisable()
		{
			bool flag = this.listenHandle != null;
			if (flag)
			{
				I18N.UnlistenLanguageChanged(this.listenHandle);
				this.listenHandle = null;
			}
		}

		[SerializeField]
		[Tooltip("The context of this text.")]
		private string context;

		[SerializeField]
		[Tooltip("The text for translate.")]
		[TextArea(3, 5)]
		private string text;

		[SerializeField]
		[Tooltip("The plural text for translate.")]
		[TextArea(3, 5)]
		private string pluralText;

		private LinkedListNode<Action> listenHandle;
	}
}
