﻿using System;
using UnityEngine;

namespace Nirvana
{
	internal static class DiagnosticsSkin
	{
		static DiagnosticsSkin()
		{
			DiagnosticsSkin.BenchPanel.normal.background = TextureMaker.Gray(0.114f);
			DiagnosticsSkin.BenchTitle = new GUIStyle(GUI.skin.box)
			{
				border = new RectOffset(0, 0, 0, 0),
				margin = new RectOffset(0, 0, 0, 10),
				padding = new RectOffset(0, 0, 5, 5)
			};
			DiagnosticsSkin.BenchTitle.normal.background = TextureMaker.Gray(0.215f);
			DiagnosticsSkin.BenchTitleLabel = new GUIStyle(GUI.skin.label)
			{
				alignment = 4,
				fontStyle = 1,
				fontSize = 16,
				border = new RectOffset(0, 0, 0, 0),
				margin = new RectOffset(0, 0, 0, 0),
				padding = new RectOffset(0, 0, 2, 0)
			};
			DiagnosticsSkin.BenchTabbar = new GUIStyle(GUI.skin.button);
			DiagnosticsSkin.BenchTabbar.normal.background = TextureMaker.Gray(0.27f);
			DiagnosticsSkin.BenchTabbar.normal.textColor = new Color(0.6f, 0.6f, 0.6f, 1f);
			DiagnosticsSkin.BenchTabbar.hover.background = TextureMaker.Gray(0.35f);
			DiagnosticsSkin.BenchTabbar.hover.textColor = new Color(0.6f, 0.6f, 0.6f, 1f);
			DiagnosticsSkin.BenchTabbar.active.background = TextureMaker.Gray(0.55f);
			DiagnosticsSkin.BenchTabbar.active.textColor = new Color(0.6f, 0.6f, 0.6f, 1f);
			DiagnosticsSkin.BenchTabbar.onNormal.background = TextureMaker.Gray(0.44f);
			DiagnosticsSkin.BenchTabbar.onNormal.textColor = new Color(0.95f, 0.95f, 0.95f, 1f);
			DiagnosticsSkin.BenchTabbar.onHover.background = TextureMaker.Gray(0.44f);
			DiagnosticsSkin.BenchTabbar.onHover.textColor = new Color(0.95f, 0.95f, 0.95f, 1f);
			DiagnosticsSkin.BenchTabbar.onActive.background = TextureMaker.Gray(0.44f);
			DiagnosticsSkin.BenchTabbar.onActive.textColor = new Color(0.95f, 0.95f, 0.95f, 1f);
			DiagnosticsSkin.BenchContent = new GUIStyle(GUI.skin.box);
			DiagnosticsSkin.BenchButton = new GUIStyle(GUI.skin.button);
			DiagnosticsSkin.BenchButton.normal.background = TextureMaker.Gray(0.27f);
			DiagnosticsSkin.BenchButton.normal.textColor = new Color(0.95f, 0.95f, 0.95f, 1f);
			DiagnosticsSkin.BenchButton.hover.background = TextureMaker.Gray(0.35f);
			DiagnosticsSkin.BenchButton.hover.textColor = new Color(0.95f, 0.95f, 0.95f, 1f);
			DiagnosticsSkin.BenchButton.active.background = TextureMaker.Gray(0.55f);
			DiagnosticsSkin.BenchButton.active.textColor = new Color(0.95f, 0.95f, 0.95f, 1f);
			DiagnosticsSkin.BenchToggle = new GUIStyle(GUI.skin.button);
			DiagnosticsSkin.BenchToggle.normal.background = TextureMaker.Gray(0.27f);
			DiagnosticsSkin.BenchToggle.normal.textColor = new Color(0.6f, 0.6f, 0.6f, 1f);
			DiagnosticsSkin.BenchToggle.hover.background = TextureMaker.Gray(0.35f);
			DiagnosticsSkin.BenchToggle.hover.textColor = new Color(0.6f, 0.6f, 0.6f, 1f);
			DiagnosticsSkin.BenchToggle.active.background = TextureMaker.Gray(0.55f);
			DiagnosticsSkin.BenchToggle.active.textColor = new Color(0.6f, 0.6f, 0.6f, 1f);
			DiagnosticsSkin.BenchToggle.onNormal.background = TextureMaker.Gray(0.8f);
			DiagnosticsSkin.BenchToggle.onNormal.textColor = new Color(0.45f, 0.45f, 0.45f, 1f);
			DiagnosticsSkin.BenchToggle.onHover.background = TextureMaker.Gray(0.9f);
			DiagnosticsSkin.BenchToggle.onHover.textColor = new Color(0.45f, 0.45f, 0.45f, 1f);
			DiagnosticsSkin.BenchToggle.onActive.background = TextureMaker.Gray(1f);
			DiagnosticsSkin.BenchToggle.onActive.textColor = new Color(0.45f, 0.45f, 0.45f, 1f);
			DiagnosticsSkin.BenchSlider = new GUIStyle(GUI.skin.horizontalSlider);
			DiagnosticsSkin.BenchSlider.normal.background = TextureMaker.Gray(0.33f);
			DiagnosticsSkin.BenchSliderThumb = new GUIStyle(GUI.skin.horizontalSliderThumb);
			DiagnosticsSkin.BenchSliderThumb.normal.background = TextureMaker.Gray(0.77f);
			DiagnosticsSkin.BenchSliderThumb.hover.background = TextureMaker.Gray(0.87f);
			DiagnosticsSkin.BenchSliderThumb.active.background = TextureMaker.Gray(0.97f);
			DiagnosticsSkin.CloseButton = new GUIStyle(GUI.skin.button)
			{
				fixedHeight = 22f,
				fixedWidth = 22f
			};
			DiagnosticsSkin.CloseButton.normal.background = TextureMaker.Gray(0.27f);
			DiagnosticsSkin.CloseButton.normal.textColor = new Color(0.95f, 0.95f, 0.95f, 1f);
			DiagnosticsSkin.CloseButton.hover.background = TextureMaker.Gray(0.35f);
			DiagnosticsSkin.CloseButton.hover.textColor = new Color(0.95f, 0.95f, 0.95f, 1f);
			DiagnosticsSkin.CloseButton.active.background = TextureMaker.Gray(0.55f);
			DiagnosticsSkin.CloseButton.active.textColor = new Color(0.95f, 0.95f, 0.95f, 1f);
			DiagnosticsSkin.AnchorToolbar = new GUIStyle(GUI.skin.button)
			{
				border = new RectOffset(0, 0, 0, 0),
				margin = new RectOffset(5, 5, 0, 0),
				padding = new RectOffset(0, 0, 0, 0),
				fixedWidth = 16f,
				fixedHeight = 16f
			};
			DiagnosticsSkin.AnchorToolbar.normal.background = TextureMaker.Gray(0.15f);
			DiagnosticsSkin.AnchorToolbar.hover.background = TextureMaker.Gray(0.25f);
			DiagnosticsSkin.AnchorToolbar.active.background = TextureMaker.Gray(0.35f);
			DiagnosticsSkin.AnchorToolbar.onNormal.background = TextureMaker.Gray(0.4f);
			DiagnosticsSkin.AnchorToolbar.onHover.background = TextureMaker.Gray(0.5f);
			DiagnosticsSkin.AnchorToolbar.onActive.background = TextureMaker.Gray(0.6f);
			DiagnosticsSkin.ChartPillar = new GUIStyle(GUI.skin.label)
			{
				imagePosition = 2,
				border = new RectOffset(0, 0, 0, 0),
				margin = new RectOffset(2, 2, 0, 0),
				padding = new RectOffset(0, 0, 0, 0)
			};
			DiagnosticsSkin.ChartPillar.normal.background = TextureMaker.Gray(1f);
			DiagnosticsSkin.ProgressBackground = new GUIStyle(GUI.skin.label)
			{
				imagePosition = 2,
				border = new RectOffset(0, 0, 0, 0),
				margin = new RectOffset(2, 2, 0, 0),
				padding = new RectOffset(0, 0, 0, 0)
			};
			DiagnosticsSkin.ProgressBackground.normal.background = TextureMaker.Gray(0.25f);
			DiagnosticsSkin.Progress = new GUIStyle(GUI.skin.label)
			{
				imagePosition = 2,
				border = new RectOffset(0, 0, 0, 0),
				margin = new RectOffset(0, 0, 0, 0),
				padding = new RectOffset(0, 0, 0, 0)
			};
			DiagnosticsSkin.Progress.normal.background = TextureMaker.Gray(0.5f);
			DiagnosticsSkin.MemoryTitleLabel = new GUIStyle(GUI.skin.label)
			{
				fontSize = 16,
				fontStyle = 1
			};
			DiagnosticsSkin.MemoryTotalLabel = new GUIStyle(GUI.skin.label)
			{
				fontSize = 12,
				alignment = 5
			};
			DiagnosticsSkin.MemoryUsedLabel = new GUIStyle(GUI.skin.label)
			{
				fontSize = 12
			};
			DiagnosticsSkin.LogIcon = new GUIStyle(GUI.skin.label)
			{
				imagePosition = 2,
				contentOffset = new Vector2(2f, 2f),
				fixedHeight = 16f,
				fixedWidth = 16f
			};
			DiagnosticsSkin.LogItemEvenLine = new GUIStyle
			{
				border = new RectOffset(0, 0, 0, 0)
			};
			DiagnosticsSkin.LogItemEvenLine.normal.background = TextureMaker.Gray(0.2f);
			DiagnosticsSkin.LogItemOddLine = new GUIStyle
			{
				border = new RectOffset(0, 0, 0, 0)
			};
			DiagnosticsSkin.LogItemOddLine.normal.background = TextureMaker.Gray(0.1f);
		}

		public static GUIStyle BenchPanel { get; private set; } = new GUIStyle(GUI.skin.window)
		{
			padding = new RectOffset(0, 0, 0, 0)
		};

		public static GUIStyle BenchTitle { get; private set; }

		public static GUIStyle BenchTitleLabel { get; private set; }

		public static GUIStyle BenchTabbar { get; private set; }

		public static GUIStyle BenchContent { get; private set; }

		public static GUIStyle BenchButton { get; private set; }

		public static GUIStyle BenchToggle { get; private set; }

		public static GUIStyle BenchSlider { get; private set; }

		public static GUIStyle BenchSliderThumb { get; private set; }

		public static GUIStyle CloseButton { get; private set; }

		public static GUIStyle AnchorToolbar { get; private set; }

		public static GUIStyle ChartPillar { get; private set; }

		public static GUIStyle ProgressBackground { get; private set; }

		public static GUIStyle Progress { get; private set; }

		public static GUIStyle MemoryTitleLabel { get; private set; }

		public static GUIStyle MemoryTotalLabel { get; private set; }

		public static GUIStyle MemoryUsedLabel { get; private set; }

		public static GUIStyle LogIcon { get; private set; }

		public static GUIStyle LogItemEvenLine { get; private set; }

		public static GUIStyle LogItemOddLine { get; private set; }
	}
}
