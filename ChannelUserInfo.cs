﻿using System;

namespace Nirvana
{
	public sealed class ChannelUserInfo
	{
		public int ZoneID { get; set; }

		public string ZoneName { get; set; }

		public int RoleID { get; set; }

		public string RoleName { get; set; }

		public int RoleLevel { get; set; }

		public int Currency { get; set; }

		public int Diamond { get; set; }

		public int VIP { get; set; }

		public string GuildName { get; set; }

		public string UserID { get; set; }

		public long CreateTime { get; set; }

		public string ProductName { get; set; }

		public string ProductDesc { get; set; }

		public string Ratio { get; set; }

		public string paramStr { get; set; }

		public string RoleCapability { get; set; }

		internal string GetJson()
		{
			return string.Format("{{\"ZoneID\": {0}, \"ZoneName\": \"{1}\", \"RoleID\": {2}, \"RoleName\": \"{3}\", \"RoleLevel\": {4}, \"Currency\": {5}, \"Diamond\": {6}, \"VIP\": {7}, \"GuildName\": \"{8}\", \"UserID\": \"{9}\", \"CreateTime\": \"{10}\",\"ProductName\": \"{11}\", \"ProductDesc\": \"{12}\", \"Ratio\": \"{13}\", \"paramStr\": \"{14}\", \"roleCapability\": \"{15}\"}}", new object[]
			{
				this.ZoneID, this.ZoneName, this.RoleID, this.RoleName, this.RoleLevel, this.Currency, this.Diamond, this.VIP, this.GuildName, this.UserID,
				this.CreateTime, this.ProductName, this.ProductDesc, this.Ratio, this.paramStr, this.RoleCapability
			});
		}
	}
}
