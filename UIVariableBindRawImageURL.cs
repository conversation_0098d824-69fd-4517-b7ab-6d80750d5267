﻿using System;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind RawImage URL")]
	[RequireComponent(typeof(LoadRawImageURL))]
	public sealed class UIVariableBindRawImageURL : UIVariableBind
	{
		public override void BindVariables()
		{
			Assert.IsNull<UIVariable>(this.urlVariable);
			bool flag = !string.IsNullOrEmpty(this.urlBind);
			if (flag)
			{
				this.urlVariable = base.FindVariable(this.urlBind);
				bool flag2 = this.urlVariable == null;
				if (flag2)
				{
					UIVariableBindRawImageURL.logger.LogWarning("{0} can not find a variable {1}", new object[] { base.name, this.urlBind });
				}
				else
				{
					this.urlVariable.OnValueInitialized += this.OnURLChanged;
					this.urlVariable.OnValueChanged += this.OnURLChanged;
					this.OnURLChanged();
				}
			}
		}

		public override void UnbindVariables()
		{
			bool flag = this.urlVariable != null;
			if (flag)
			{
				this.urlVariable.OnValueInitialized -= this.OnURLChanged;
				this.urlVariable.OnValueChanged -= this.OnURLChanged;
				this.urlVariable = null;
			}
		}

		private void OnURLChanged()
		{
			bool flag = this.loadRawImage == null;
			if (flag)
			{
				this.loadRawImage = base.GetComponent<LoadRawImageURL>();
			}
			bool flag2 = this.loadRawImage != null;
			if (flag2)
			{
				string @string = this.urlVariable.GetString();
				this.loadRawImage.URL = @string;
			}
		}

		private static Logger logger = LogSystem.GetLogger("UIVariableBindRawImageURL");

		[SerializeField]
		[VariableName(UIVariableType.String)]
		private string urlBind;

		private LoadRawImageURL loadRawImage;

		private UIVariable urlVariable;
	}
}
