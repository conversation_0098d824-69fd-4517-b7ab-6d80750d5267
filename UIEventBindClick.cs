﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Event Bind Click")]
	public sealed class UIEventBindClick : UIEventBind, IPointerClickHandler, IEventSystemHandler
	{
		private Signal ClickSignal
		{
			get
			{
				bool flag = this.clickSignal == null;
				if (flag)
				{
					this.clickSignal = base.FindEvent(this.eventName);
				}
				return this.clickSignal;
			}
		}

		public void OnPointerClick(PointerEventData eventData)
		{
			bool flag = this.selectable != null && !this.selectable.interactable;
			if (!flag)
			{
				Signal signal = this.ClickSignal;
				bool flag2 = signal != null;
				if (flag2)
				{
					signal.Invoke(Array.Empty<object>());
				}
			}
		}

		private new void Awake()
		{
			base.Awake();
			this.selectable = base.GetComponent<Selectable>();
		}

		[SerializeField]
		[EventName]
		public string eventName;

		private Selectable selectable;

		private Signal clickSignal;
	}
}
