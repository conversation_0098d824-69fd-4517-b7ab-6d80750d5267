﻿using System;
using UnityEngine;

namespace Nirvana
{
	[CreateAssetMenu(fileName = "I18NFont", menuName = "Nirvana/I18N/I18NFont")]
	public sealed class I18NFont : ScriptableObject
	{
		public AssetID FindFont(SystemLanguage language)
		{
			bool flag = this.entries == null;
			AssetID assetID;
			if (flag)
			{
				assetID = AssetID.Empty;
			}
			else
			{
				foreach (I18NFont.FontEntry fontEntry in this.entries)
				{
					bool flag2 = fontEntry.Language == language;
					if (flag2)
					{
						return fontEntry.Asset;
					}
				}
				assetID = AssetID.Empty;
			}
			return assetID;
		}

		[SerializeField]
		private I18NFont.FontEntry[] entries;

		[Serializable]
		private struct FontEntry
		{
			[SerializeField]
			[Tooltip("The target language.")]
			public SystemLanguage Language;

			[SerializeField]
			[Tooltip("The font asset.")]
			[AssetType(typeof(Font))]
			public AssetID Asset;
		}
	}
}
