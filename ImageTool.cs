﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class ImageTool
	{
		public static bool IsPhotoPurview()
		{
			return ImageTool.GetImageTool().CallStatic<bool>("isPhotoPurview", new object[0]);
		}

		public static void RequestPhotoPurview(Action<bool, string> callBack)
		{
			ImageTool.imageToolListener.CompleteDelegate = callBack;
			ImageTool.GetImageTool().CallStatic("requestPhotoPurview", new object[] { ImageTool.imageToolListener });
		}

		public static void AddToPhoto(string filePath, Action<bool, string> callBack)
		{
			ImageTool.imageToolListener.CompleteDelegate = callBack;
			ImageTool.GetImageTool().CallStatic("addToPhoto", new object[]
			{
				filePath,
				ImageTool.imageToolListener
			});
		}

		private static AndroidJavaClass GetImageTool()
		{
			bool flag = ImageTool.imageToolClass == null;
			if (flag)
			{
				ImageTool.imageToolClass = new AndroidJavaClass("com.winunet.and.PictureTool");
			}
			return ImageTool.imageToolClass;
		}

		private static AndroidJavaClass imageToolClass;

		private static ImageTool.ImageToolListener imageToolListener = new ImageTool.ImageToolListener();

		private class ImageToolListener : AndroidJavaProxy
		{
			public ImageToolListener()
				: base("com.winunet.and.AndCallBack")
			{
			}

			public Action<bool, string> CompleteDelegate { get; set; }

			private void onCallback(bool status, string json)
			{
				SdkScheduler.PostTask(delegate
				{
					Action<bool, string> completeDelegate = this.CompleteDelegate;
					this.CompleteDelegate = null;
					bool flag = completeDelegate != null;
					if (flag)
					{
						completeDelegate(status, json);
					}
				});
			}
		}
	}
}
