﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Cylinder Text")]
	[RequireComponent(typeof(Text))]
	public sealed class UICylinderText : BaseMeshEffect
	{
		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive();
			if (!flag)
			{
				int currentVertCount = vh.currentVertCount;
				bool flag2 = !this.IsActive() || currentVertCount == 0;
				if (!flag2)
				{
					for (int i = 0; i < vh.currentVertCount; i++)
					{
						UIVertex uivertex = default(UIVertex);
						vh.PopulateUIVertex(ref uivertex, i);
						float x = uivertex.position.x;
						uivertex.position.z = -this.radius * Mathf.Cos(x / this.radius);
						uivertex.position.x = this.radius * Mathf.Sin(x / this.radius);
						vh.SetUIVertex(uivertex, i);
					}
				}
			}
		}

		protected override void Awake()
		{
			base.Awake();
			this.OnRectTransformDimensionsChange();
		}

		protected override void OnEnable()
		{
			base.OnEnable();
			this.OnRectTransformDimensionsChange();
		}

		[SerializeField]
		private float radius = 220f;
	}
}
