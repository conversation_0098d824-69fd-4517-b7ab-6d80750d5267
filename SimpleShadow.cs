﻿using System;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.Rendering;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Render/SimpleShadow")]
	public sealed class SimpleShadow : MonoBehaviour
	{
		public Material ShadowMaterial
		{
			get
			{
				return this.shadowMaterial;
			}
			set
			{
				bool flag = this.shadowMaterial != value;
				if (flag)
				{
					this.shadowMaterial = value;
					this.RemoveFromShadowMesh();
					this.AddToShadowMesh();
				}
			}
		}

		public Vector2 ShadowSize
		{
			get
			{
				return this.shadowSize;
			}
			set
			{
				this.shadowSize = value;
			}
		}

		public int GroundMask
		{
			get
			{
				return this.groundMask;
			}
			set
			{
				this.groundMask = value;
			}
		}

		public float Offset
		{
			get
			{
				return this.offset;
			}
			set
			{
				this.offset = value;
			}
		}

		public float ScaleDistance
		{
			get
			{
				return this.scaleDistance;
			}
			set
			{
				this.scaleDistance = value;
			}
		}

		internal bool Dirty { get; set; }

		internal bool Visible { get; private set; }

		internal void UpdateShadowMesh(Vector3[] vertices, Vector2[] uvs, int index)
		{
			Transform transform = base.transform;
			float num = Mathf.Abs(transform.position.y - this.groundPos.y);
			Vector3 vector = this.groundPos;
			Vector3 forward = transform.forward;
			Vector3 right = transform.right;
			float num2 = Mathf.Min(1f, 1f / (1f + this.scaleDistance * num));
			Vector2 vector2 = this.shadowSize * num2;
			int num3 = 4 * index;
			vertices[num3] = vector + forward * vector2.y * 0.5f + -right * vector2.x * 0.5f;
			vertices[num3 + 1] = vector + forward * vector2.y * 0.5f + right * vector2.x * 0.5f;
			vertices[num3 + 2] = vector + -forward * vector2.y * 0.5f + -right * vector2.x * 0.5f;
			vertices[num3 + 3] = vector + -forward * vector2.y * 0.5f + right * vector2.x * 0.5f;
			uvs[num3] = new Vector2(0f, 0f);
			uvs[num3 + 1] = new Vector2(1f, 0f);
			uvs[num3 + 2] = new Vector2(0f, 1f);
			uvs[num3 + 3] = new Vector2(1f, 1f);
		}

		private void OnEnable()
		{
			this.RemoveFromShadowMesh();
			this.AddToShadowMesh();
		}

		private void OnDisable()
		{
			this.RemoveFromShadowMesh();
		}

		private void Update()
		{
			Camera main = Camera.main;
			bool flag = main == null || !main.isActiveAndEnabled;
			if (!flag)
			{
				bool flag2 = this.checkRealtimeShadow;
				if (flag2)
				{
					bool flag3 = QualitySettings.shadows > 0;
					bool flag4 = flag3 && this.relativeRenderer != null;
					if (flag4)
					{
						ShadowCastingMode shadowCastingMode = this.relativeRenderer.shadowCastingMode;
						flag3 = shadowCastingMode > 0;
					}
					bool flag5 = flag3;
					if (flag5)
					{
						this.RemoveFromShadowMesh();
						return;
					}
					bool flag6 = !this.shadowID.IsValid;
					if (flag6)
					{
						this.AddToShadowMesh();
					}
				}
				bool flag7 = false;
				Transform transform = base.transform;
				bool flag8 = this.lastPosition != transform.position || this.lastRotation != transform.rotation;
				if (flag8)
				{
					flag7 = true;
					this.Dirty = true;
					this.lastPosition = transform.position;
					this.lastRotation = transform.rotation;
				}
				bool flag9 = this.staticEnvironment;
				if (flag9)
				{
					bool flag10 = flag7;
					if (flag10)
					{
						this.CheckGround(transform.position, main);
					}
				}
				else
				{
					this.CheckGround(transform.position, main);
				}
			}
		}

		private void CheckGround(Vector3 position, Camera camera)
		{
			Ray ray;
			ray..ctor(position + 10000f * Vector3.up, Vector3.down);
			int num = Physics.RaycastNonAlloc(ray, SimpleShadow.hits, float.PositiveInfinity, this.groundMask);
			bool flag = num > 0 && num <= SimpleShadow.hits.Length;
			if (flag)
			{
				float num2 = float.NegativeInfinity;
				for (int i = 0; i < num; i++)
				{
					RaycastHit raycastHit = SimpleShadow.hits[i];
					bool flag2 = num2 < raycastHit.point.y;
					if (flag2)
					{
						num2 = raycastHit.point.y;
					}
				}
				Vector3 vector;
				vector..ctor(position.x, num2 + this.offset, position.z);
				bool flag3 = this.groundPos != vector;
				if (flag3)
				{
					this.groundPos = vector;
					this.Dirty = true;
				}
				GeometryUtilityHelper.ExtractPlanes(SimpleShadow.planes, camera);
				Vector3 vector2;
				vector2..ctor(this.shadowSize.x, 0.1f, this.shadowSize.y);
				Bounds bounds;
				bounds..ctor(this.groundPos, vector2);
				bool flag4 = GeometryUtility.TestPlanesAABB(SimpleShadow.planes, bounds);
				bool flag5 = this.Visible != flag4;
				if (flag5)
				{
					this.Visible = flag4;
					this.Dirty = true;
				}
			}
			else
			{
				this.RemoveFromShadowMesh();
			}
		}

		private void AddToShadowMesh()
		{
			Assert.IsFalse(this.shadowID.IsValid);
			bool flag = this.shadowMaterial != null;
			if (flag)
			{
				this.shadowID = Singleton<SimpleShadowManager>.Instance.AddShadow(this);
			}
		}

		private void RemoveFromShadowMesh()
		{
			bool isValid = this.shadowID.IsValid;
			if (isValid)
			{
				Singleton<SimpleShadowManager>.Instance.RemoveShadow(this.shadowID);
				this.shadowID.Reset();
			}
		}

		public void RawSetShadowMaterial(Material material)
		{
			this.shadowMaterial = material;
		}

		private static RaycastHit[] hits = new RaycastHit[8];

		private static Plane[] planes = new Plane[6];

		[SerializeField]
		[Tooltip("The material for this shadow.")]
		private Material shadowMaterial;

		[SerializeField]
		[Tooltip("Get the shadow size.")]
		private Vector2 shadowSize = new Vector2(0.5f, 0.5f);

		[SerializeField]
		[Tooltip("The raycast mask.")]
		[LayerMask]
		private int groundMask = -1;

		[SerializeField]
		[Tooltip("The offset for height.")]
		private float offset = 0.01f;

		[SerializeField]
		[Tooltip("The distance scale.")]
		[Range(0.1f, 5f)]
		private float scaleDistance = 1f;

		[SerializeField]
		[Tooltip("Whether treat the environment is static, used for optimize.")]
		private bool staticEnvironment = false;

		[SerializeField]
		[Tooltip("Whether to check realtime shadow and disable self.")]
		private bool checkRealtimeShadow = true;

		[SerializeField]
		[Tooltip("The relative renderer for realtime shadow check.")]
		private Renderer relativeRenderer;

		private SimpleShadowManager.ShadowID shadowID;

		private Vector3 groundPos;

		private Vector3 lastPosition;

		private Quaternion lastRotation;
	}
}
