﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class TextureCombiner : Singleton<TextureCombiner>
	{
		public void Combine(Texture2D[] textures, out Texture2D atlas, out Rect[] rects)
		{
			foreach (Texture2D texture2D in textures)
			{
				this.key.Add(texture2D.name);
			}
			this.key.Sort();
			TextureCombiner.AtlasCache atlasCache;
			bool flag = this.cache.TryGetValue(this.key, out atlasCache);
			if (flag)
			{
				bool isAlive = atlasCache.Atlas.IsAlive;
				if (isAlive)
				{
					atlas = (Texture2D)atlasCache.Atlas.Target;
					rects = atlasCache.AtlasRects;
					this.key.Clear();
					return;
				}
				this.cache.Remove(this.key);
			}
			Texture2D texture2D2 = textures[0];
			TextureFormat format = texture2D2.format;
			bool flag2 = texture2D2.mipmapCount > 0;
			atlas = new Texture2D(0, 0, format, flag2);
			atlas.name = "Combined Atlas";
			atlas.hideFlags = 52;
			rects = atlas.PackTextures(textures, 0, 4096, true);
			this.cache.Add(new List<string>(this.key), new TextureCombiner.AtlasCache(atlas, rects));
			this.key.Clear();
		}

		private Dictionary<List<string>, TextureCombiner.AtlasCache> cache = new Dictionary<List<string>, TextureCombiner.AtlasCache>(ListComparer<string>.Default);

		private List<string> key = new List<string>();

		private struct AtlasCache
		{
			public AtlasCache(Texture2D atlas, Rect[] rects)
			{
				this.Atlas = new WeakReference(atlas);
				this.AtlasRects = rects;
			}

			public WeakReference Atlas;

			public Rect[] AtlasRects;
		}
	}
}
