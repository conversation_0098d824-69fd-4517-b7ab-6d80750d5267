﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Depth Effect")]
	[RequireComponent(typeof(RectTransform))]
	public sealed class UIDepthEffect : BaseMeshEffect
	{
		public Color EffectColor
		{
			get
			{
				return this.effectColor;
			}
			set
			{
				bool flag = this.effectColor != value;
				if (flag)
				{
					this.effectColor = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public Vector2 DirectionAndDepth
		{
			get
			{
				return this.directionAndDepth;
			}
			set
			{
				bool flag = this.directionAndDepth != value;
				if (flag)
				{
					this.directionAndDepth = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public Vector2 DepthPerspectiveStrength
		{
			get
			{
				return this.depthPerspectiveStrength;
			}
			set
			{
				bool flag = this.depthPerspectiveStrength != value;
				if (flag)
				{
					this.depthPerspectiveStrength = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public bool OnlyInitialCharactersGenerateDepth
		{
			get
			{
				return this.onlyInitialCharactersGenerateDepth;
			}
			set
			{
				bool flag = this.onlyInitialCharactersGenerateDepth != value;
				if (flag)
				{
					this.onlyInitialCharactersGenerateDepth = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public bool UseGraphicAlpha
		{
			get
			{
				return this.useGraphicAlpha;
			}
			set
			{
				bool flag = this.useGraphicAlpha != value;
				if (flag)
				{
					this.useGraphicAlpha = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive() || vh.currentVertCount <= 0;
			if (!flag)
			{
				List<UIVertex> list = UIVertexListPool.Get();
				vh.GetUIVertexStream(list);
				List<UIVertex> list2 = this.Modify(list);
				bool flag2 = list2 != null;
				if (flag2)
				{
					vh.Clear();
					vh.AddUIVertexTriangleStream(list2);
				}
				UIVertexListPool.Release(list);
			}
		}

		private List<UIVertex> Modify(List<UIVertex> verts)
		{
			int count = verts.Count;
			Text component = base.GetComponent<Text>();
			List<UIVertex> list = new List<UIVertex>();
			bool flag = this.onlyInitialCharactersGenerateDepth;
			if (flag)
			{
				list = verts.GetRange(verts.Count - component.cachedTextGenerator.characterCountVisible * 6, component.cachedTextGenerator.characterCountVisible * 6);
			}
			else
			{
				list = verts;
			}
			bool flag2 = list.Count == 0;
			List<UIVertex> list2;
			if (flag2)
			{
				list2 = null;
			}
			else
			{
				bool flag3 = this.depthPerspectiveStrength.x != 0f || this.depthPerspectiveStrength.y != 0f;
				if (flag3)
				{
					this.topLeftPos = list[0].position;
					this.bottomRightPos = list[list.Count - 1].position;
					for (int i = 0; i < list.Count; i++)
					{
						bool flag4 = list[i].position.x < this.topLeftPos.x;
						if (flag4)
						{
							this.topLeftPos.x = list[i].position.x;
						}
						bool flag5 = list[i].position.y > this.topLeftPos.y;
						if (flag5)
						{
							this.topLeftPos.y = list[i].position.y;
						}
						bool flag6 = list[i].position.x > this.bottomRightPos.x;
						if (flag6)
						{
							this.bottomRightPos.x = list[i].position.x;
						}
						bool flag7 = list[i].position.y < this.bottomRightPos.y;
						if (flag7)
						{
							this.bottomRightPos.y = list[i].position.y;
						}
					}
					this.overallTextSize = new Vector2(this.bottomRightPos.x - this.topLeftPos.x, this.topLeftPos.y - this.bottomRightPos.y);
				}
				int num = 0;
				int num2 = num;
				num = list.Count;
				this.ApplyShadowZeroAlloc(list, this.effectColor, num2, list.Count, this.directionAndDepth.x, this.directionAndDepth.y, 0.25f);
				num2 = num;
				num = list.Count;
				this.ApplyShadowZeroAlloc(list, this.effectColor, num2, list.Count, this.directionAndDepth.x, this.directionAndDepth.y, 0.5f);
				num2 = num;
				num = list.Count;
				this.ApplyShadowZeroAlloc(list, this.effectColor, num2, list.Count, this.directionAndDepth.x, this.directionAndDepth.y, 0.75f);
				num2 = num;
				num = list.Count;
				this.ApplyShadowZeroAlloc(list, this.effectColor, num2, list.Count, this.directionAndDepth.x, this.directionAndDepth.y, 1f);
				bool flag8 = this.onlyInitialCharactersGenerateDepth;
				if (flag8)
				{
					list.RemoveRange(list.Count - component.cachedTextGenerator.characterCountVisible * 6, component.cachedTextGenerator.characterCountVisible * 6);
					list.AddRange(verts);
				}
				bool flag9 = this.HasComponent<UIMaterialEffect>();
				if (flag9)
				{
					for (int j = 0; j < list.Count - count; j++)
					{
						UIVertex uivertex = list[j];
						uivertex.uv1 = new Vector2(0f, 0f);
						list[j] = uivertex;
					}
				}
				list2 = list;
			}
			return list2;
		}

		private void ApplyShadowZeroAlloc(List<UIVertex> verts, Color32 color, int start, int end, float x, float y, float factor)
		{
			for (int i = start; i < end; i++)
			{
				UIVertex uivertex = verts[i];
				verts.Add(uivertex);
				Vector3 position = uivertex.position;
				position.x += x * factor;
				bool flag = this.depthPerspectiveStrength.x != 0f;
				if (flag)
				{
					position.x -= this.depthPerspectiveStrength.x * factor * ((position.x - this.topLeftPos.x) / this.overallTextSize.x - 0.5f);
				}
				position.y += y * factor;
				bool flag2 = this.depthPerspectiveStrength.y != 0f;
				if (flag2)
				{
					position.y += this.depthPerspectiveStrength.y * factor * ((this.topLeftPos.y - position.y) / this.overallTextSize.y - 0.5f);
				}
				uivertex.position = position;
				Color32 color2 = color;
				bool flag3 = this.useGraphicAlpha;
				if (flag3)
				{
					color2.a = color2.a * verts[i].color.a / byte.MaxValue;
				}
				uivertex.color = color2;
				verts[i] = uivertex;
			}
		}

		[SerializeField]
		private Color effectColor = Color.black;

		[SerializeField]
		private Vector2 directionAndDepth = new Vector2(-1f, 1f);

		[SerializeField]
		private Vector2 depthPerspectiveStrength = new Vector2(0f, 0f);

		[SerializeField]
		private bool onlyInitialCharactersGenerateDepth = true;

		[SerializeField]
		private bool useGraphicAlpha = true;

		private Vector2 overallTextSize = Vector2.zero;

		private Vector2 topLeftPos = Vector2.zero;

		private Vector2 bottomRightPos = Vector2.zero;
	}
}
