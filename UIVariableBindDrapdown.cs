﻿using System;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Dropdown")]
	[RequireComponent(typeof(Dropdown))]
	public sealed class UIVariableBindDrapdown : UIVariableBind
	{
		public override void BindVariables()
		{
			Assert.IsNull<UIVariable>(this.valueVariable);
			bool flag = !string.IsNullOrEmpty(this.valueBind);
			if (flag)
			{
				this.valueVariable = base.FindVariable(this.valueBind);
				bool flag2 = this.valueVariable == null;
				if (flag2)
				{
					UIVariableBindDrapdown.logger.LogWarning("{0} can not find a variable {1}", new object[] { base.name, this.valueBind });
				}
				else
				{
					this.valueVariable.OnValueInitialized += this.OnValueChanged;
					this.valueVariable.OnValueChanged += this.OnValueChanged;
					this.OnValueChanged();
				}
			}
		}

		public override void UnbindVariables()
		{
			bool flag = this.valueVariable != null;
			if (flag)
			{
				this.valueVariable.OnValueInitialized -= this.OnValueChanged;
				this.valueVariable.OnValueChanged -= this.OnValueChanged;
				this.valueVariable = null;
			}
		}

		private void OnValueChanged()
		{
			bool flag = this.dropdown == null;
			if (flag)
			{
				this.dropdown = base.GetComponent<Dropdown>();
			}
			bool flag2 = this.dropdown != null;
			if (flag2)
			{
				int integer = this.valueVariable.GetInteger();
				this.dropdown.value = integer;
			}
		}

		private new void Awake()
		{
			base.Awake();
			this.dropdown = base.GetComponent<Dropdown>();
			bool flag = this.dropdown == null;
			if (flag)
			{
			}
		}

		private static Logger logger = LogSystem.GetLogger("UIVariableBindDrapdown");

		[SerializeField]
		[VariableName(UIVariableType.Integer)]
		private string valueBind;

		private Dropdown dropdown;

		private UIVariable valueVariable;
	}
}
