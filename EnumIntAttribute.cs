﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AttributeUsage(AttributeTargets.Field, AllowMultiple = true)]
	public sealed class EnumIntAttribute : PropertyAttribute
	{
		public EnumIntAttribute(string label, int value)
		{
			this.label = label;
			this.value = value;
		}

		public string Label
		{
			get
			{
				return this.label;
			}
		}

		public int Value
		{
			get
			{
				return this.value;
			}
		}

		private string label;

		private int value;
	}
}
