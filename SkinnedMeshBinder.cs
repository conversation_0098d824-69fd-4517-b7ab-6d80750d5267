﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class SkinnedMeshBinder
	{
		public void AddRenderer(SkinnedMeshRenderer renderer, Transform[] bones, Transform root)
		{
			SkinnedMeshBinder.CombineItem combineItem = default(SkinnedMeshBinder.CombineItem);
			combineItem.Renderer = renderer;
			combineItem.Bones = bones;
			combineItem.Root = root;
			this.combineItems.Add(combineItem);
		}

		public void AddRenderer(SkinnedMeshRenderer renderer, Transform root)
		{
			SkinnedMeshBinder.CombineItem combineItem = default(SkinnedMeshBinder.CombineItem);
			combineItem.Renderer = renderer;
			combineItem.Bones = renderer.bones;
			combineItem.Root = root;
			this.combineItems.Add(combineItem);
		}

		public void Bind(Transform rootBone)
		{
			bool flag = this.boneTable == null;
			if (flag)
			{
				this.boneTable = new Dictionary<string, Transform>(StringComparer.Ordinal);
			}
			this.boneTable.Clear();
			this.BuildBoneTable(rootBone);
			foreach (SkinnedMeshBinder.CombineItem combineItem in this.combineItems)
			{
				SkinnedMeshRenderer renderer = combineItem.Renderer;
				Transform[] array = new Transform[combineItem.Bones.Length];
				for (int i = 0; i < combineItem.Bones.Length; i++)
				{
					Transform transform = combineItem.Bones[i];
					Transform transform2 = null;
					bool flag2 = !this.boneTable.TryGetValue(transform.name, out transform2);
					if (flag2)
					{
						SkinnedMeshBinder.logger.LogWarning("Can not find the bone: " + transform.name);
					}
					array[i] = transform2;
				}
				renderer.bones = array;
				renderer.rootBone = rootBone;
			}
		}

		public void CombineSkinedMeshes(GameObject target)
		{
			int num = 0;
			foreach (SkinnedMeshBinder.CombineItem combineItem in this.combineItems)
			{
				num += combineItem.Renderer.sharedMesh.subMeshCount;
			}
			List<Transform> list = new List<Transform>(this.boneTable.Values);
			List<BoneWeight> list2 = new List<BoneWeight>();
			List<Texture2D> list3 = new List<Texture2D>();
			List<CombineInstance> list4 = new List<CombineInstance>();
			Material material = null;
			int[] array = new int[num];
			int i = 0;
			while (i < this.combineItems.Count)
			{
				SkinnedMeshBinder.CombineItem combineItem2 = this.combineItems[i];
				SkinnedMeshRenderer renderer = combineItem2.Renderer;
				foreach (BoneWeight boneWeight in renderer.sharedMesh.boneWeights)
				{
					BoneWeight boneWeight2 = boneWeight;
					Transform transform = renderer.bones[boneWeight2.boneIndex0];
					Transform transform2 = renderer.bones[boneWeight2.boneIndex1];
					Transform transform3 = renderer.bones[boneWeight2.boneIndex2];
					Transform transform4 = renderer.bones[boneWeight2.boneIndex3];
					boneWeight2.boneIndex0 = list.IndexOf(transform);
					boneWeight2.boneIndex1 = list.IndexOf(transform2);
					boneWeight2.boneIndex2 = list.IndexOf(transform3);
					boneWeight2.boneIndex3 = list.IndexOf(transform4);
					list2.Add(boneWeight2);
				}
				bool flag = renderer.sharedMaterial.mainTexture != null;
				if (flag)
				{
					bool flag2 = material == null;
					if (flag2)
					{
						material = renderer.sharedMaterial;
					}
					list3.Add(renderer.sharedMaterial.mainTexture as Texture2D);
				}
				CombineInstance combineInstance = default(CombineInstance);
				combineInstance.mesh = renderer.sharedMesh;
				array[i] = combineInstance.mesh.vertexCount;
				bool flag3 = combineItem2.Root != null;
				if (!flag3)
				{
					combineInstance.transform = renderer.transform.localToWorldMatrix;
					goto IL_027A;
				}
				Transform transform5;
				bool flag4 = this.boneTable.TryGetValue(combineItem2.Root.name, out transform5);
				if (flag4)
				{
					Vector3 position = transform5.position;
					Quaternion rotation = renderer.transform.rotation;
					combineInstance.transform = Matrix4x4.TRS(position, rotation, Vector3.one);
					goto IL_027A;
				}
				SkinnedMeshBinder.logger.LogError("The bone {0} is not existed.", new object[] { combineItem2.Root.name });
				IL_0292:
				i++;
				continue;
				IL_027A:
				list4.Add(combineInstance);
				Object.Destroy(renderer.gameObject);
				goto IL_0292;
			}
			Matrix4x4[] array2 = new Matrix4x4[list.Count];
			for (int k = 0; k < list.Count; k++)
			{
				Transform transform6 = list[k];
				Matrix4x4 worldToLocalMatrix = transform6.worldToLocalMatrix;
				array2[k] = worldToLocalMatrix;
			}
			SkinnedMeshRenderer skinnedMeshRenderer = target.AddComponent<SkinnedMeshRenderer>();
			skinnedMeshRenderer.sharedMesh = new Mesh();
			skinnedMeshRenderer.sharedMesh.name = "Combined Skin Mesh";
			skinnedMeshRenderer.sharedMesh.CombineMeshes(list4.ToArray(), true, true);
			skinnedMeshRenderer.sharedMesh.hideFlags = 52;
			Texture2D texture2D = null;
			Rect[] array3 = null;
			Singleton<TextureCombiner>.Instance.Combine(list3.ToArray(), out texture2D, out array3);
			Vector2[] uv = skinnedMeshRenderer.sharedMesh.uv;
			Vector2[] array4 = new Vector2[uv.Length];
			int num2 = 0;
			int num3 = 0;
			for (int l = 0; l < array4.Length; l++)
			{
				bool flag5 = l >= array[num2] + num3;
				if (flag5)
				{
					num3 += array[num2];
					num2++;
				}
				Rect rect = array3[num2];
				array4[l].x = uv[l].x * rect.width + rect.x;
				array4[l].y = uv[l].y * rect.height + rect.y;
			}
			Material material2 = new Material(material);
			material2.mainTexture = texture2D;
			skinnedMeshRenderer.sharedMesh.uv = array4;
			skinnedMeshRenderer.sharedMaterial = material2;
			skinnedMeshRenderer.bones = list.ToArray();
			skinnedMeshRenderer.sharedMesh.boneWeights = list2.ToArray();
			skinnedMeshRenderer.sharedMesh.bindposes = array2;
			skinnedMeshRenderer.sharedMesh.RecalculateBounds();
		}

		private void BuildBoneTable(Transform bone)
		{
			this.boneTable.Add(bone.name, bone);
			foreach (object obj in bone)
			{
				Transform transform = (Transform)obj;
				this.BuildBoneTable(transform);
			}
		}

		private static Logger logger = LogSystem.GetLogger("SkinnedMeshBinder");

		private List<SkinnedMeshBinder.CombineItem> combineItems = new List<SkinnedMeshBinder.CombineItem>();

		private Dictionary<string, Transform> boneTable = null;

		private struct CombineItem
		{
			public SkinnedMeshRenderer Renderer;

			public Transform[] Bones;

			public Transform Root;
		}
	}
}
