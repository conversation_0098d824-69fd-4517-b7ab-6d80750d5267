﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Animator/Animator Synchronizer")]
	[RequireComponent(typeof(Animator))]
	public sealed class AnimatorSynchronizer : MonoBehaviour
	{
		public Animator Source
		{
			get
			{
				return this.source;
			}
			set
			{
				this.source = value;
			}
		}

		private void Awake()
		{
			this.animator = base.GetComponent<Animator>();
			bool flag = this.booleanParams != null;
			if (flag)
			{
				this.booleanParamKeys = new int[this.booleanParams.Length];
				for (int i = 0; i < this.booleanParamKeys.Length; i++)
				{
					this.booleanParamKeys[i] = Animator.StringToHash(this.booleanParams[i]);
				}
				this.booleanParams = null;
			}
			bool flag2 = this.integerParams != null;
			if (flag2)
			{
				this.integerParamKeys = new int[this.integerParams.Length];
				for (int j = 0; j < this.integerParamKeys.Length; j++)
				{
					this.integerParamKeys[j] = Animator.StringToHash(this.integerParams[j]);
				}
				this.integerParams = null;
			}
			bool flag3 = this.floatParams != null;
			if (flag3)
			{
				this.floatParamKeys = new int[this.floatParams.Length];
				for (int k = 0; k < this.floatParamKeys.Length; k++)
				{
					this.floatParamKeys[k] = Animator.StringToHash(this.floatParams[k]);
				}
				this.floatParams = null;
			}
		}

		private void Update()
		{
			bool flag = this.source == null || this.animator == null;
			if (!flag)
			{
				bool flag2 = this.booleanParamKeys != null;
				if (flag2)
				{
					foreach (int num in this.booleanParamKeys)
					{
						bool @bool = this.source.GetBool(num);
						this.animator.SetBool(num, @bool);
					}
				}
				bool flag3 = this.integerParamKeys != null;
				if (flag3)
				{
					foreach (int num2 in this.integerParamKeys)
					{
						int integer = this.source.GetInteger(num2);
						this.animator.SetInteger(num2, integer);
					}
				}
				bool flag4 = this.floatParamKeys != null;
				if (flag4)
				{
					foreach (int num3 in this.floatParamKeys)
					{
						float @float = this.source.GetFloat(num3);
						this.animator.SetFloat(num3, @float);
					}
				}
			}
		}

		[SerializeField]
		[Tooltip("The source animator")]
		private Animator source;

		[SerializeField]
		private string[] booleanParams;

		[SerializeField]
		private string[] integerParams;

		[SerializeField]
		private string[] floatParams;

		private Animator animator;

		private int[] booleanParamKeys;

		private int[] integerParamKeys;

		private int[] floatParamKeys;
	}
}
