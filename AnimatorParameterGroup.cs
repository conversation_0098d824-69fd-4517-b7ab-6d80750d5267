﻿using System;
using System.Collections;
using UnityEngine;

namespace Nirvana
{
	public sealed class AnimatorParameterGroup : MonoBehaviour
	{
		public Animator GetAnimator()
		{
			return this.animator;
		}

		public void SetParameters()
		{
			bool flag = this.animator == null;
			if (!flag)
			{
				foreach (AnimatorParameterGroup.Parameter parameter in this.parameters)
				{
					AnimatorControllerParameterType parameterType = parameter.ParameterType;
					AnimatorControllerParameterType animatorControllerParameterType = parameterType;
					switch (animatorControllerParameterType)
					{
					case 1:
						this.animator.SetFloat(parameter.ID, parameter.FloatValue);
						break;
					case 2:
						break;
					case 3:
						this.animator.SetInteger(parameter.ID, parameter.IntegerValue);
						break;
					case 4:
						this.animator.SetBool(parameter.ID, parameter.BooleanValue);
						break;
					default:
						if (animatorControllerParameterType == 9)
						{
							bool booleanValue = parameter.BooleanValue;
							if (booleanValue)
							{
								this.animator.SetTrigger(parameter.ID);
							}
							else
							{
								this.animator.ResetTrigger(parameter.ID);
							}
						}
						break;
					}
				}
			}
		}

		private void Start()
		{
			bool flag = this.activeOnInitialize && this.animator != null;
			if (flag)
			{
				bool isInitialized = this.animator.isInitialized;
				if (isInitialized)
				{
					this.SetParameters();
				}
				else
				{
					base.StartCoroutine(this.SetupParameter());
				}
			}
		}

		private IEnumerator SetupParameter()
		{
			for (;;)
			{
				bool isInitialized = this.animator.isInitialized;
				if (isInitialized)
				{
					break;
				}
				yield return 0;
			}
			this.SetParameters();
			yield break;
			yield break;
		}

		[SerializeField]
		private Animator animator;

		[SerializeField]
		private bool activeOnInitialize = true;

		[SerializeField]
		private AnimatorParameterGroup.Parameter[] parameters;

		[Serializable]
		private struct Parameter
		{
			[SerializeField]
			public string Name;

			[SerializeField]
			public int ID;

			[SerializeField]
			public AnimatorControllerParameterType ParameterType;

			[SerializeField]
			public bool BooleanValue;

			[SerializeField]
			public int IntegerValue;

			[SerializeField]
			public float FloatValue;
		}
	}
}
