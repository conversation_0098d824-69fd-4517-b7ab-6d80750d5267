﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Event Bind Slider")]
	[RequireComponent(typeof(Slider))]
	public sealed class UIEventBindSlider : UIEventBind
	{
		private Signal ValueChangedSignal
		{
			get
			{
				bool flag = this.valueChangedSignal == null;
				if (flag)
				{
					this.valueChangedSignal = base.FindEvent(this.eventName);
				}
				return this.valueChangedSignal;
			}
		}

		private new void Awake()
		{
			base.Awake();
			this.slider = base.GetComponent<Slider>();
		}

		private void OnEnable()
		{
			this.slider.onValueChanged.AddListener(new UnityAction<float>(this.OnValueChanged));
		}

		private void OnDisable()
		{
			this.slider.onValueChanged.RemoveListener(new UnityAction<float>(this.OnValueChanged));
		}

		private void OnValueChanged(float value)
		{
			Signal signal = this.ValueChangedSignal;
			bool flag = signal != null;
			if (flag)
			{
				signal.Invoke(new object[] { value });
			}
		}

		[SerializeField]
		[EventName]
		public string eventName;

		private Slider slider;

		private Signal valueChangedSignal;
	}
}
