﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class WebView
	{
		public static void Open(string url)
		{
			WebView.GetWebView().CallStatic("open", new object[] { url });
		}

		private static AndroidJavaObject GetWebView()
		{
			bool flag = WebView.webView == null;
			if (flag)
			{
				WebView.webView = new AndroidJavaClass("com.winunet.and.WebViewerOnGame");
			}
			return WebView.webView;
		}

		private static AndroidJavaClass webView;
	}
}
