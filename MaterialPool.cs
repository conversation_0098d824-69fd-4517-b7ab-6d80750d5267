﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class MaterialPool : Singleton<MaterialPool>
	{
		public MaterialPool()
		{
			this.sweepChecker = delegate(AssetID assetID, MaterialCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						float num = cache.LastFreeTime + cache.ReleaseAfterFree;
						bool flag4 = Time.realtimeSinceStartup > num;
						if (flag4)
						{
							Material @object = cache.GetObject();
							bool flag5 = @object != null;
							if (flag5)
							{
								this.lookup.Remove(@object);
								AssetManager.UnloadAsseBundle(assetID.BundleName);
							}
							flag2 = true;
						}
						else
						{
							flag2 = false;
						}
					}
				}
				return flag2;
			};
			Scheduler.AddFrameListener(new Action(this.SweepCache));
		}

		public float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				foreach (KeyValuePair<AssetID, MaterialCache> keyValuePair in this.caches)
				{
					keyValuePair.Value.DefaultReleaseAfterFree = value;
				}
			}
		}

		public void Load(AssetID assetID, Action<Material> complete)
		{
			Scheduler.RunCoroutine(this.LoadImpl(assetID, complete));
		}

		public WaitLoadMaterial Load(AssetID assetID)
		{
			MaterialCache materialCache;
			bool flag = this.caches.TryGetValue(assetID, out materialCache);
			WaitLoadMaterial waitLoadMaterial;
			if (flag)
			{
				materialCache.Retain();
				waitLoadMaterial = new WaitLoadMaterial(materialCache);
			}
			else
			{
				materialCache = new MaterialCache(assetID, this.lookup);
				materialCache.LoadObject(assetID);
				materialCache.Retain();
				this.caches.Add(assetID, materialCache);
				waitLoadMaterial = new WaitLoadMaterial(materialCache);
			}
			return waitLoadMaterial;
		}

		public void Free(Material obj, bool destroy = false)
		{
			bool flag = obj == null;
			if (flag)
			{
				MaterialPool.logger.LogError("Try to free a null Material.");
			}
			else
			{
				MaterialCache materialCache;
				bool flag2 = !this.lookup.TryGetValue(obj, out materialCache);
				if (flag2)
				{
					MaterialPool.logger.LogWarning("Try to free an instance {0} not allocated by this pool.", new object[] { obj.name });
				}
				else
				{
					materialCache.Release();
					bool flag3 = destroy && materialCache.ReferenceCount == 0;
					if (flag3)
					{
						AssetID assetID = materialCache.AssetID;
						Material @object = materialCache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						this.caches.Remove(assetID);
					}
				}
			}
		}

		public void Clear()
		{
			this.caches.Clear();
			this.lookup.Clear();
		}

		public void ClearAllUnused()
		{
			this.caches.RemoveAll(delegate(AssetID assetID, MaterialCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						Material @object = cache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						flag2 = true;
					}
				}
				return flag2;
			});
		}

		private IEnumerator LoadImpl(AssetID assetID, Action<Material> complete)
		{
			WaitLoadMaterial waitLoad = this.Load(assetID);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			bool flag = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag)
			{
				MaterialPool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private void SweepCache()
		{
			this.caches.RemoveAll(this.sweepChecker);
		}

		private static Logger logger = LogSystem.GetLogger("MaterialPool");

		private Dictionary<AssetID, MaterialCache> caches = new Dictionary<AssetID, MaterialCache>();

		private Dictionary<Material, MaterialCache> lookup = new Dictionary<Material, MaterialCache>();

		private float defaultReleaseAfterFree = 30f;

		private Func<AssetID, MaterialCache, bool> sweepChecker;
	}
}
