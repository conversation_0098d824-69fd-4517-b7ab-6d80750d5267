﻿using System;
using System.Text;
using UnityEngine;

namespace Nirvana
{
	public static class AnimationCurveExtensions
	{
		public static string ToData(this AnimationCurve curve)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append(curve.preWrapMode);
			stringBuilder.Append(',');
			stringBuilder.Append(curve.postWrapMode);
			stringBuilder.Append(',');
			stringBuilder.Append(curve.length);
			foreach (Keyframe keyframe in curve.keys)
			{
				stringBuilder.Append(',');
				stringBuilder.Append(keyframe.time);
				stringBuilder.Append(',');
				stringBuilder.Append(keyframe.value);
				stringBuilder.Append(',');
				stringBuilder.Append(keyframe.inTangent);
				stringBuilder.Append(',');
				stringBuilder.Append(keyframe.outTangent);
				stringBuilder.Append(',');
				stringBuilder.Append(keyframe.tangentMode);
			}
			return stringBuilder.ToString();
		}

		public static AnimationCurve TryParseFromData(string text)
		{
			string[] array = text.Split(new char[] { ',' });
			bool flag = array.Length < 3;
			AnimationCurve animationCurve;
			if (flag)
			{
				animationCurve = null;
			}
			else
			{
				int num;
				bool flag2 = !int.TryParse(array[0], out num);
				if (flag2)
				{
					animationCurve = null;
				}
				else
				{
					int num2;
					bool flag3 = !int.TryParse(array[1], out num2);
					if (flag3)
					{
						animationCurve = null;
					}
					else
					{
						int num3;
						bool flag4 = !int.TryParse(array[2], out num3);
						if (flag4)
						{
							animationCurve = null;
						}
						else
						{
							bool flag5 = array.Length != 5 * num3 + 3;
							if (flag5)
							{
								animationCurve = null;
							}
							else
							{
								Keyframe[] array2 = new Keyframe[num3];
								for (int i = 0; i < num3; i++)
								{
									int num4 = 5 * i + 3;
									float num5;
									bool flag6 = !float.TryParse(array[num4], out num5);
									if (flag6)
									{
										return null;
									}
									float num6;
									bool flag7 = !float.TryParse(array[num4 + 1], out num6);
									if (flag7)
									{
										return null;
									}
									float num7;
									bool flag8 = !float.TryParse(array[num4 + 2], out num7);
									if (flag8)
									{
										return null;
									}
									float num8;
									bool flag9 = !float.TryParse(array[num4 + 3], out num8);
									if (flag9)
									{
										return null;
									}
									int num9;
									bool flag10 = !int.TryParse(array[num4 + 4], out num9);
									if (flag10)
									{
										return null;
									}
									Keyframe keyframe;
									keyframe..ctor(num5, num6, num7, num8);
									keyframe.tangentMode = num9;
									array2[i] = keyframe;
								}
								animationCurve = new AnimationCurve(array2)
								{
									preWrapMode = num,
									postWrapMode = num2
								};
							}
						}
					}
				}
			}
			return animationCurve;
		}
	}
}
