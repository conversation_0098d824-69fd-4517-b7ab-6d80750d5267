﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class WaitLoadMaterial : CustomYieldInstruction
	{
		internal WaitLoadMaterial(MaterialCache cache)
		{
			this.cache = cache;
		}

		public Material LoadedObject { get; private set; }

		public string Error { get; private set; }

		public override bool keepWaiting
		{
			get
			{
				bool flag = !string.IsNullOrEmpty(this.cache.Error);
				bool flag2;
				if (flag)
				{
					this.Error = this.cache.Error;
					flag2 = false;
				}
				else
				{
					bool flag3 = this.cache.HasLoaded();
					if (flag3)
					{
						this.LoadedObject = this.cache.GetObject();
						flag2 = false;
					}
					else
					{
						flag2 = true;
					}
				}
				return flag2;
			}
		}

		private MaterialCache cache;
	}
}
