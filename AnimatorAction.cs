﻿using System;
using UnityEngine;

namespace Nirvana
{
	[Serializable]
	internal struct AnimatorAction
	{
		internal string EventName
		{
			get
			{
				return this.eventName;
			}
		}

		internal string EventParam
		{
			get
			{
				return this.eventParam;
			}
		}

		internal void TriggerAction(Animator animator, AnimatorStateInfo stateInfo)
		{
			AnimatorEventDispatcher component = animator.GetComponent<AnimatorEventDispatcher>();
			bool flag = component != null;
			if (flag)
			{
				component.DispatchEvent(this.eventName, this.eventParam, stateInfo);
			}
		}

		[SerializeField]
		[Tooltip("The event name.")]
		private string eventName;

		[SerializeField]
		[Tooltip("The event integer parameter.")]
		private string eventParam;
	}
}
