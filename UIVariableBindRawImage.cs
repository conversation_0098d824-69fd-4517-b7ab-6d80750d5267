﻿using System;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind RawImage")]
	[RequireComponent(typeof(RawImage))]
	public sealed class UIVariableBindRawImage : UIVariableBind
	{
		public override void BindVariables()
		{
			Assert.IsNull<UIVariable>(this.textureVariable);
			bool flag = !string.IsNullOrEmpty(this.textureBind);
			if (flag)
			{
				this.textureVariable = base.FindVariable(this.textureBind);
				bool flag2 = this.textureVariable == null;
				if (flag2)
				{
					UIVariableBindRawImage.logger.LogWarning("{0} can not find a variable {1}", new object[] { base.name, this.textureBind });
				}
				else
				{
					this.textureVariable.OnValueInitialized += this.OnTextureChanged;
					this.textureVariable.OnValueChanged += this.OnTextureChanged;
					this.isDirty = true;
				}
			}
		}

		public override void UnbindVariables()
		{
			bool flag = this.textureVariable != null;
			if (flag)
			{
				this.textureVariable.OnValueInitialized -= this.OnTextureChanged;
				this.textureVariable.OnValueChanged -= this.OnTextureChanged;
				this.textureVariable = null;
			}
			bool flag2 = this.texture != null;
			if (flag2)
			{
				bool isPlaying = Application.isPlaying;
				if (isPlaying)
				{
					Singleton<TexturePool>.Instance.Free(this.texture, this.isRealtimeUnload);
				}
				this.texture = null;
			}
		}

		private void OnTextureChanged()
		{
			this.isDirty = true;
		}

		private void ChangeSprite(AssetID assetID)
		{
			bool flag = this.textureAsset.Equals(assetID);
			if (!flag)
			{
				bool flag2 = this.texture != null;
				if (flag2)
				{
					bool isPlaying = Application.isPlaying;
					if (isPlaying)
					{
						Singleton<TexturePool>.Instance.Free(this.texture, this.isRealtimeUnload);
					}
					this.texture = null;
				}
				this.textureAsset = assetID;
				bool isEmpty = assetID.IsEmpty;
				if (isEmpty)
				{
					this.rawImage.texture = null;
				}
				else
				{
					this.LoadFromPool(assetID);
				}
			}
		}

		private void LoadFromPool(AssetID assetID)
		{
			Singleton<TexturePool>.Instance.Load(assetID, delegate(Texture texture)
			{
				bool flag = this.rawImage == null;
				if (flag)
				{
					Singleton<TexturePool>.Instance.Free(texture, this.isRealtimeUnload);
				}
				else
				{
					this.texture = texture;
					bool flag2 = this.texture == null;
					if (flag2)
					{
						string text = string.Format("Load texture {0} failed.", assetID);
						Debug.LogError(text, this.gameObject);
					}
					bool flag3 = this.textureAsset.Equals(assetID);
					if (flag3)
					{
						this.rawImage.texture = texture;
						this.rawImage.enabled = true;
						bool flag4 = this.autoFitNativeSize;
						if (flag4)
						{
							this.rawImage.SetNativeSize();
						}
					}
				}
			}, this.isSync);
		}

		private new void Awake()
		{
			base.Awake();
			this.rawImage = base.GetComponent<RawImage>();
			this.rawImage.enabled = false;
		}

		private void Update()
		{
			bool flag = this.isDirty;
			if (flag)
			{
				this.isDirty = false;
				AssetID asset = this.textureVariable.GetAsset();
				bool flag2 = !this.textureAsset.Equals(asset);
				if (flag2)
				{
					this.ChangeSprite(asset);
				}
			}
		}

		private static Logger logger = LogSystem.GetLogger("UIVariableBindRawImage");

		[SerializeField]
		[VariableName(UIVariableType.Asset)]
		private string textureBind;

		[SerializeField]
		public bool autoFitNativeSize = false;

		[SerializeField]
		public bool isSync = false;

		[SerializeField]
		public bool isRealtimeUnload = false;

		private RawImage rawImage;

		private Texture texture;

		private AssetID textureAsset;

		private UIVariable textureVariable;

		private bool isDirty = false;
	}
}
