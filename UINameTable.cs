﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/UI Name Table")]
	public sealed class UINameTable : MonoBehaviour
	{
		public Dictionary<string, GameObject> Lookup
		{
			get
			{
				bool flag = this.lookup == null;
				if (flag)
				{
					this.lookup = new Dictionary<string, GameObject>(StringComparer.Ordinal);
					bool flag2 = this.binds != null;
					if (flag2)
					{
						foreach (UINameTable.BindPair bindPair in this.binds)
						{
							this.lookup.Add(bindPair.Name, bindPair.Widget);
						}
						this.binds = null;
					}
				}
				return this.lookup;
			}
		}

		public GameObject Find(string key)
		{
			GameObject gameObject;
			bool flag = this.Lookup.TryGetValue(key, out gameObject);
			GameObject gameObject2;
			if (flag)
			{
				gameObject2 = gameObject;
			}
			else
			{
				gameObject2 = null;
			}
			return gameObject2;
		}

		public bool Add(string key, GameObject obj)
		{
			bool flag = this.lookup.ContainsKey(key);
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				this.lookup.Add(key, obj);
				UINameTable.BindPair bindPair = default(UINameTable.BindPair);
				bindPair.Name = key;
				bindPair.Widget = obj;
				this.binds.Add(bindPair);
				flag2 = true;
			}
			return flag2;
		}

		public int GetCount()
		{
			return this.binds.Count;
		}

		public UINameTable.BindPair GetBindPair(int index)
		{
			return this.binds[index];
		}

		[SerializeField]
		[Tooltip("The bind list.")]
		public List<UINameTable.BindPair> binds = new List<UINameTable.BindPair>();

		private Dictionary<string, GameObject> lookup;

		[Serializable]
		public struct BindPair
		{
			[Tooltip("The name of this bind.")]
			public string Name;

			[Tooltip("The widget of this UI.")]
			public GameObject Widget;
		}
	}
}
