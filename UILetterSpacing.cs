﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Letter Spacing")]
	[RequireComponent(typeof(Text))]
	public sealed class UILetterSpacing : BaseMeshEffect
	{
		public float Spacing
		{
			get
			{
				return this.spacing;
			}
			set
			{
				bool flag = this.spacing != value;
				if (flag)
				{
					this.spacing = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive() || vh.currentVertCount <= 0;
			if (!flag)
			{
				Text component = base.GetComponent<Text>();
				bool flag2 = component == null;
				if (flag2)
				{
					Debug.LogWarning("LetterSpacing: Missing Text component");
				}
				else
				{
					List<UIVertex> list = UIVertexListPool.Get();
					vh.GetUIVertexStream(list);
					List<UIVertex> list2 = this.Modify(list, component, this.spacing);
					bool flag3 = list2 != null;
					if (flag3)
					{
						vh.Clear();
						vh.AddUIVertexTriangleStream(list2);
					}
					UIVertexListPool.Release(list);
				}
			}
		}

		private List<UIVertex> Modify(List<UIVertex> verts, Text text, float spacing)
		{
			List<string> list = new List<string>();
			for (int i = 0; i < text.cachedTextGenerator.lineCount; i++)
			{
				int startCharIdx = text.cachedTextGenerator.lines[i].startCharIdx;
				int num = ((i < text.cachedTextGenerator.lineCount - 1) ? text.cachedTextGenerator.lines[i + 1].startCharIdx : text.text.Length);
				list.Add(text.text.Substring(startCharIdx, num - startCharIdx));
			}
			float num2 = 0f;
			switch (text.alignment)
			{
			case 0:
			case 3:
			case 6:
				num2 = 0f;
				break;
			case 1:
			case 4:
			case 7:
				num2 = 0.5f;
				break;
			case 2:
			case 5:
			case 8:
				num2 = 1f;
				break;
			}
			float num3 = spacing * (float)text.fontSize;
			IEnumerator enumerator = null;
			Match match = null;
			bool flag = true;
			int num4 = 0;
			int num5 = 0;
			while (num5 < list.Count && flag)
			{
				string text2 = list[num5];
				int num6 = text2.Length;
				bool flag2 = num6 > text.cachedTextGenerator.characterCountVisible - num4;
				if (flag2)
				{
					num6 = text.cachedTextGenerator.characterCountVisible - num4;
					text2 = text2.Substring(0, num6) + " ";
					num6++;
				}
				bool supportRichText = text.supportRichText;
				if (supportRichText)
				{
					enumerator = this.GetRegexMatchedTags(text2, out num6).GetEnumerator();
					match = null;
					bool flag3 = enumerator.MoveNext();
					if (flag3)
					{
						match = (Match)enumerator.Current;
					}
				}
				bool flag4 = list[num5].Length > 0 && (list[num5][list[num5].Length - 1] == ' ' || list[num5][list[num5].Length - 1] == '\n');
				float num7 = (float)(-(float)(num6 - 1 - (flag4 ? 1 : 0))) * num3 * num2;
				float num8 = 0f;
				int num9 = 0;
				while (num9 < text2.Length && flag)
				{
					bool supportRichText2 = text.supportRichText;
					if (supportRichText2)
					{
						bool flag5 = match != null && match.Index == num9;
						if (flag5)
						{
							num9 += match.Length - 1;
							num4 += match.Length - 1;
							num8 -= 1f;
							match = null;
							bool flag6 = enumerator.MoveNext();
							if (flag6)
							{
								match = (Match)enumerator.Current;
							}
						}
					}
					bool flag7 = num4 * 6 + 5 >= verts.Count;
					if (flag7)
					{
						flag = false;
						break;
					}
					for (int j = 0; j < 6; j++)
					{
						UIVertex uivertex = verts[num4 * 6 + j];
						uivertex.position += Vector3.right * (num3 * num8 + num7);
						verts[num4 * 6 + j] = uivertex;
					}
					num4++;
					num8 += 1f;
					num9++;
				}
				num5++;
			}
			return verts;
		}

		private MatchCollection GetRegexMatchedTags(string text, out int lengthWithoutTags)
		{
			MatchCollection matchCollection = Regex.Matches(text, "<b>|</b>|<i>|</i>|<size=.*?>|</size>|<color=.*?>|</color>|<material=.*?>|</material>");
			lengthWithoutTags = 0;
			int num = 0;
			foreach (object obj in matchCollection)
			{
				Match match = (Match)obj;
				num += match.Length;
			}
			lengthWithoutTags = text.Length - num;
			return matchCollection;
		}

		private const string RegexTags = "<b>|</b>|<i>|</i>|<size=.*?>|</size>|<color=.*?>|</color>|<material=.*?>|</material>";

		private const int N = 6;

		[SerializeField]
		private float spacing = 0f;
	}
}
