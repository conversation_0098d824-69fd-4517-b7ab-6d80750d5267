﻿using System;
using System.IO;
using UnityEngine.SceneManagement;

namespace Nirvana
{
	internal sealed class WaitLoadLevelFullSync : WaitLoadLevel
	{
		internal WaitLoadLevelFullSync(AssetBundleManager assetBundleManager, string assetbundleName, string levelName, LoadSceneMode loadMode)
		{
			this.assetBundleManager = assetBundleManager;
			this.assetBundleName = assetbundleName;
			this.levelName = Path.GetFileNameWithoutExtension(levelName);
			this.loadMode = loadMode;
			this.complete = false;
		}

		public override float Progress
		{
			get
			{
				return 1f;
			}
		}

		public override bool keepWaiting
		{
			get
			{
				return base.Error == null && !this.complete;
			}
		}

		internal override bool Update()
		{
			AssetBundleItem assetBundle = this.assetBundleManager.GetAssetBundle(this.assetBundleName);
			bool flag = assetBundle == null;
			bool flag2;
			if (flag)
			{
				flag2 = true;
			}
			else
			{
				bool flag3 = !string.IsNullOrEmpty(assetBundle.Error);
				if (flag3)
				{
					base.Error = assetBundle.Error;
					flag2 = false;
				}
				else
				{
					SceneManager.LoadScene(this.levelName, this.loadMode);
					this.complete = true;
					flag2 = false;
				}
			}
			return flag2;
		}

		private AssetBundleManager assetBundleManager;

		private string assetBundleName;

		private string levelName;

		private LoadSceneMode loadMode;

		private bool complete;
	}
}
