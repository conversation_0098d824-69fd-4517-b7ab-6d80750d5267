﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class AnimatorTimelineBehaviour : StateMachineBehaviour
	{
		public AnimatorTimelineBehaviour.TimelineEvent[] TimelineEvents
		{
			get
			{
				return this.timelineEvents;
			}
		}

		public override void OnStateEnter(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
		{
			for (int i = 0; i < this.timelineEvents.Length; i++)
			{
				this.timelineEvents[i].HasTriggered = false;
				this.timelineEvents[i].LoopCount = 0;
			}
		}

		public override void OnStateUpdate(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
		{
			for (int i = 0; i < this.timelineEvents.Length; i++)
			{
				AnimatorTimelineBehaviour.TimelineEvent timelineEvent = this.timelineEvents[i];
				int num = (int)Math.Floor((double)stateInfo.normalizedTime);
				bool flag = num > timelineEvent.LoopCount;
				if (flag)
				{
					bool hasTriggered = timelineEvent.HasTriggered;
					if (hasTriggered)
					{
						timelineEvent.HasTriggered = false;
					}
					else
					{
						bool flag2 = timelineEvent.NormalizedTime >= 0f && timelineEvent.NormalizedTime <= 1f;
						if (flag2)
						{
							timelineEvent.TriggerAction(animator, stateInfo);
						}
					}
					timelineEvent.LoopCount = num;
				}
				bool flag3 = !timelineEvent.HasTriggered;
				if (flag3)
				{
					float num2 = stateInfo.normalizedTime - (float)num;
					bool flag4 = num2 >= timelineEvent.NormalizedTime;
					if (flag4)
					{
						timelineEvent.TriggerAction(animator, stateInfo);
						timelineEvent.HasTriggered = true;
					}
				}
			}
		}

		[SerializeField]
		[Tooltip("The time line event and actions.")]
		private AnimatorTimelineBehaviour.TimelineEvent[] timelineEvents;

		[Serializable]
		public sealed class TimelineEvent
		{
			public float NormalizedTime
			{
				get
				{
					return this.normalizedTime;
				}
				set
				{
					this.normalizedTime = value;
				}
			}

			public string EventName
			{
				get
				{
					return this.action.EventName;
				}
			}

			public string EventParam
			{
				get
				{
					return this.action.EventParam;
				}
			}

			internal int LoopCount
			{
				get
				{
					return this.loopCount;
				}
				set
				{
					this.loopCount = value;
				}
			}

			internal bool HasTriggered
			{
				get
				{
					return this.hasTriggered;
				}
				set
				{
					this.hasTriggered = value;
				}
			}

			internal void TriggerAction(Animator animator, AnimatorStateInfo stateInfo)
			{
				this.action.TriggerAction(animator, stateInfo);
			}

			[SerializeField]
			[Tooltip("The normalized time when the time line triggered.")]
			private float normalizedTime;

			[SerializeField]
			[Tooltip("The time line event action.")]
			private AnimatorAction action;

			private int loopCount;

			private bool hasTriggered;
		}
	}
}
