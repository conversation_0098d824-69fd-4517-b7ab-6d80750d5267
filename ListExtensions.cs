﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public static class ListExtensions
	{
		public static void Shuffle<T>(this IList<T> array)
		{
			for (int i = 0; i < array.Count; i++)
			{
				int num = Random.Range(0, array.Count);
				T t = array[num];
				array[num] = array[i];
				array[i] = t;
			}
		}

		public static void RemoveDuplicate<T>(this List<T> list)
		{
			Dictionary<T, int> dictionary = new Dictionary<T, int>();
			foreach (T t in list)
			{
				int num = 0;
				bool flag = !dictionary.TryGetValue(t, out num);
				if (flag)
				{
					dictionary.Add(t, 0);
				}
			}
			list.Clear();
			foreach (KeyValuePair<T, int> keyValuePair in dictionary)
			{
				list.Add(keyValuePair.Key);
			}
		}
	}
}
