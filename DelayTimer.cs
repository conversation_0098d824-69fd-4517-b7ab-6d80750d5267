﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class DelayTimer : IDisposable
	{
		public static DelayTimer Delay(float delay, Action task)
		{
			DelayTimer delayTimer = new DelayTimer();
			delayTimer.delayTime = delay;
			delayTimer.task = task;
			delayTimer.Start();
			return delayTimer;
		}

		public void Dispose()
		{
			Scheduler.RemoveFrameListener(this.updateHandle);
			this.updateHandle = null;
		}

		private void Start()
		{
			this.leftTime = this.delayTime;
			this.updateHandle = Scheduler.AddFrameListener(new Action(this.Update));
		}

		private void Update()
		{
			this.leftTime -= Time.deltaTime;
			bool flag = this.leftTime <= 0f;
			if (flag)
			{
				try
				{
					this.task();
				}
				catch (Exception ex)
				{
					DelayTimer.logger.LogError(ex);
				}
				finally
				{
					this.Dispose();
				}
			}
		}

		private static Logger logger = LogSystem.GetLogger("DelayTimer");

		private LinkedListNode<Action> updateHandle;

		private float delayTime;

		private float leftTime;

		private Action task;
	}
}
