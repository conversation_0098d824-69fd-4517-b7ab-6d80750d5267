﻿using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	internal sealed class AssetBundleManager
	{
		internal AssetBundleManager(AssetBundleLoader loader)
		{
			this.loader = loader;
			this.sweepChecker = delegate(string assetBundleName, AssetBundleItem assetBundleItem)
			{
				bool flag = this.unloadedCountInTime > 1;
				bool flag2;
				if (flag)
				{
					flag2 = false;
				}
				else
				{
					bool flag3 = assetBundleItem.Sweep();
					bool flag4 = flag3;
					if (flag4)
					{
						this.unloadedCountInTime++;
					}
					flag2 = flag3;
				}
				return flag2;
			};
		}

		internal int MaxLoadingCount
		{
			set
			{
				this.maxLoadingCount = value;
			}
		}

		internal string[] ActiveVariants
		{
			get
			{
				return this.activeVariants;
			}
			set
			{
				this.activeVariants = value;
			}
		}

		internal AssetBundleManifest Manifest
		{
			get
			{
				return this.manifest;
			}
			set
			{
				this.manifest = value;
			}
		}

		internal AssetBundleItem GetAssetBundle(string assetBundleName)
		{
			AssetBundleItem assetBundleItem;
			bool flag = !this.assetBundles.TryGetValue(assetBundleName, out assetBundleItem);
			AssetBundleItem assetBundleItem2;
			if (flag)
			{
				assetBundleItem2 = null;
			}
			else
			{
				bool flag2 = assetBundleItem.Error != null;
				if (flag2)
				{
					assetBundleItem2 = assetBundleItem;
				}
				else
				{
					bool flag3 = assetBundleItem.AssetBundle == null;
					if (flag3)
					{
						assetBundleItem2 = null;
					}
					else
					{
						AssetBundleItem[] dependencies = assetBundleItem.Dependencies;
						bool flag4 = dependencies != null;
						if (flag4)
						{
							foreach (AssetBundleItem assetBundleItem3 in dependencies)
							{
								bool flag5 = assetBundleItem3 == null;
								if (!flag5)
								{
									bool flag6 = assetBundleItem3.Error != null;
									if (flag6)
									{
										assetBundleItem.Error = assetBundleItem3.Error;
										return assetBundleItem;
									}
									bool flag7 = assetBundleItem3.AssetBundle == null;
									if (flag7)
									{
										return null;
									}
								}
							}
						}
						assetBundleItem2 = assetBundleItem;
					}
				}
			}
			return assetBundleItem2;
		}

		internal bool LoadLocalManifest(string assetBundleName)
		{
			WaitLoadAssetBundle waitLoadAssetBundle = this.loader.LoadLocal(assetBundleName, true);
			bool flag = waitLoadAssetBundle == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				AssetBundleItem assetBundleItem = new AssetBundleItem(waitLoadAssetBundle, "");
				assetBundleItem.Retain();
				this.assetBundles.Add(assetBundleName, assetBundleItem);
				this.loadingAssetBundles.AddLast(assetBundleItem);
				flag2 = true;
			}
			return flag2;
		}

		internal bool LoadRemoteManifest(string assetBundleName)
		{
			WaitLoadAssetBundle waitLoadAssetBundle = this.loader.LoadRemote(assetBundleName);
			bool flag = waitLoadAssetBundle == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				AssetBundleItem assetBundleItem = new AssetBundleItem(waitLoadAssetBundle, "");
				assetBundleItem.Retain();
				this.assetBundles.Add(assetBundleName, assetBundleItem);
				this.loadingAssetBundles.AddLast(assetBundleItem);
				flag2 = true;
			}
			return flag2;
		}

		internal AssetBundle LoadAssetBundleSync(string assetBundleName)
		{
			AssetBundleItem assetBundleItem = this.LoadAssetBundleSyncIntenal(assetBundleName);
			bool flag = assetBundleItem != null;
			AssetBundle assetBundle;
			if (flag)
			{
				assetBundle = assetBundleItem.AssetBundle;
			}
			else
			{
				assetBundle = null;
			}
			return assetBundle;
		}

		internal bool LoadAssetBundle(string assetBundleName, bool sync)
		{
			AssetBundleItem assetBundleItem = this.LoadAssetBundleInternal(assetBundleName, sync, false);
			return assetBundleItem != null;
		}

		internal bool UnloadAsseBundle(string assetBundle)
		{
			AssetBundleItem assetBundleItem;
			bool flag = this.assetBundles.TryGetValue(assetBundle, out assetBundleItem);
			bool flag3;
			if (flag)
			{
				bool flag2 = assetBundleItem.AssetBundle != null;
				if (flag2)
				{
					assetBundleItem.Release();
				}
				flag3 = true;
			}
			else
			{
				flag3 = false;
			}
			return flag3;
		}

		public void UnloadAllUnusedAssetBundle()
		{
			this.assetBundles.RemoveAll(delegate(string assetBundleName, AssetBundleItem assetBundleItem)
			{
				bool flag = assetBundleItem.RefCount > 0;
				bool flag2;
				if (flag)
				{
					flag2 = false;
				}
				else
				{
					assetBundleItem.Destroy(true);
					flag2 = true;
				}
				return flag2;
			});
		}

		internal void DestroyAsseBundle(string assetBundle, bool unloadAllLoadedObjects = false)
		{
			AssetBundleItem assetBundleItem;
			bool flag = this.assetBundles.TryGetValue(assetBundle, out assetBundleItem);
			if (flag)
			{
				assetBundleItem.Destroy(unloadAllLoadedObjects);
				this.assetBundles.Remove(assetBundle);
			}
		}

		internal bool HasLoadingBundles()
		{
			foreach (AssetBundleItem assetBundleItem in this.loadingAssetBundles)
			{
				bool flag = !assetBundleItem.CheckLoading();
				if (flag)
				{
					return true;
				}
			}
			return false;
		}

		internal void UnloadAll()
		{
			foreach (KeyValuePair<string, AssetBundleItem> keyValuePair in this.assetBundles)
			{
				AssetBundleItem value = keyValuePair.Value;
				bool flag = value.AssetBundle != null;
				if (flag)
				{
					value.AssetBundle.Unload(false);
				}
			}
			this.loadingAssetBundles.Clear();
			this.waitLoadQueue.Clear();
			this.assetBundles.Clear();
		}

		internal string[] GetBundlesWithoutCached(string assetBundleName)
		{
			Assert.IsNotNull<AssetBundleManifest>(this.manifest);
			HashSet<string> hashSet = new HashSet<string>(StringComparer.Ordinal);
			this.GetBundlesWithoutCachedImpl(assetBundleName, hashSet);
			return hashSet.ToArray<string>();
		}

		internal void Update()
		{
			LinkedListNode<AssetBundleItem> next;
			for (LinkedListNode<AssetBundleItem> linkedListNode = this.loadingAssetBundles.First; linkedListNode != null; linkedListNode = next)
			{
				next = linkedListNode.Next;
				AssetBundleItem value = linkedListNode.Value;
				bool flag = value.CheckLoading();
				if (flag)
				{
					this.loadingAssetBundles.Remove(linkedListNode);
				}
			}
			bool flag2 = (double)(Time.time - this.lastCheckSweepTime) >= 0.2;
			if (flag2)
			{
				this.lastCheckSweepTime = Time.time;
				this.unloadedCountInTime = 0;
				this.assetBundles.RemoveAll(this.sweepChecker);
			}
			this.QueueLoad();
		}

		private void GetBundlesWithoutCachedImpl(string bundleName, HashSet<string> bundles)
		{
			bool flag = !bundles.Contains(bundleName);
			if (flag)
			{
				Hash128 assetBundleHash = this.manifest.GetAssetBundleHash(bundleName);
				bool flag2 = !this.loader.IsLocalCached(bundleName, assetBundleHash);
				if (flag2)
				{
					bundles.Add(bundleName);
				}
			}
			foreach (string text in this.manifest.GetAllDependencies(bundleName))
			{
				bool flag3 = !bundles.Contains(text);
				if (flag3)
				{
					Hash128 assetBundleHash2 = this.manifest.GetAssetBundleHash(text);
					bool flag4 = !this.loader.IsLocalCached(text, assetBundleHash2);
					if (flag4)
					{
						bundles.Add(text);
					}
				}
			}
		}

		private AssetBundleItem LoadAssetBundleSyncIntenal(string assetBundleName)
		{
			AssetBundleItem assetBundleItem;
			bool flag = this.assetBundles.TryGetValue(assetBundleName, out assetBundleItem);
			AssetBundleItem assetBundleItem2;
			if (flag)
			{
				assetBundleItem.Retain();
				assetBundleItem2 = assetBundleItem;
			}
			else
			{
				Assert.IsNotNull<AssetBundleManifest>(this.manifest);
				Hash128 assetBundleHash = this.manifest.GetAssetBundleHash(assetBundleName);
				bool flag2 = !assetBundleHash.isValid;
				if (flag2)
				{
					AssetBundleManager.logger.LogError("The bundle {0} is not record in the manifest.", new object[] { assetBundleName });
					assetBundleItem2 = null;
				}
				else
				{
					AssetBundle assetBundle = this.loader.LoadLocalSync(assetBundleName, assetBundleHash);
					bool flag3 = assetBundle == null;
					if (flag3)
					{
						assetBundleItem2 = null;
					}
					else
					{
						assetBundleItem = new AssetBundleItem(assetBundle, assetBundleName);
						this.assetBundles.Add(assetBundleName, assetBundleItem);
						string[] allDependencies = this.manifest.GetAllDependencies(assetBundleName);
						AssetBundleItem[] array = new AssetBundleItem[allDependencies.Length];
						for (int i = 0; i < allDependencies.Length; i++)
						{
							array[i] = this.LoadAssetBundleSyncIntenal(allDependencies[i]);
						}
						assetBundleItem.Dependencies = array;
						assetBundleItem.Retain();
						assetBundleItem2 = assetBundleItem;
					}
				}
			}
			return assetBundleItem2;
		}

		private AssetBundleItem LoadAssetBundleInternal(string assetBundleName, bool sync, bool isFromDependLoad = false)
		{
			AssetBundleItem assetBundleItem;
			bool flag = this.assetBundles.TryGetValue(assetBundleName, out assetBundleItem);
			AssetBundleItem assetBundleItem2;
			if (flag)
			{
				assetBundleItem.Retain();
				assetBundleItem2 = assetBundleItem;
			}
			else
			{
				bool flag2 = this.manifest == null;
				if (flag2)
				{
					AssetBundleManager.logger.LogError("The manifest is null, can not load {0}", new object[] { assetBundleName });
					assetBundleItem2 = null;
				}
				else
				{
					Hash128 assetBundleHash = this.manifest.GetAssetBundleHash(assetBundleName);
					bool flag3 = !assetBundleHash.isValid;
					if (flag3)
					{
						AssetBundleManager.logger.LogError("The bundle {0} is not record in the manifest.", new object[] { assetBundleName });
						assetBundleItem2 = null;
					}
					else
					{
						if (sync)
						{
							WaitLoadAssetBundle waitLoadAssetBundle = this.loader.Load(assetBundleName, assetBundleHash, sync);
							bool flag4 = waitLoadAssetBundle == null || !string.IsNullOrEmpty(waitLoadAssetBundle.Error);
							if (flag4)
							{
								AssetBundleManager.logger.LogError("Load bundle {0} with hash {1} is failed.", new object[] { assetBundleName, assetBundleHash });
								return null;
							}
							assetBundleItem = new AssetBundleItem(waitLoadAssetBundle, assetBundleName);
							this.assetBundles.Add(assetBundleName, assetBundleItem);
							this.loadingAssetBundles.AddLast(assetBundleItem);
						}
						else
						{
							assetBundleItem = new AssetBundleItem(assetBundleName);
							this.assetBundles.Add(assetBundleName, assetBundleItem);
							this.waitLoadQueue.Enqueue(assetBundleItem);
						}
						string[] allDependencies = this.manifest.GetAllDependencies(assetBundleName);
						AssetBundleItem[] array = new AssetBundleItem[allDependencies.Length];
						for (int i = 0; i < allDependencies.Length; i++)
						{
							string text = allDependencies[i];
							array[i] = this.LoadAssetBundleInternal(text, sync, true);
							bool flag5 = array[i] == null;
							if (flag5)
							{
								AssetBundleManager.logger.LogError("Missing dependency bundle {0}.", new object[] { text });
							}
						}
						assetBundleItem.Dependencies = array;
						assetBundleItem.Retain();
						assetBundleItem2 = assetBundleItem;
					}
				}
			}
			return assetBundleItem2;
		}

		private void QueueLoad()
		{
			bool flag = this.waitLoadQueue.Count <= 0;
			if (!flag)
			{
				int num = this.maxLoadingCount + this.waitLoadQueue.Count / 10;
				int num2 = num - this.loadingAssetBundles.Count;
				bool flag2 = num2 <= 0;
				if (!flag2)
				{
					int num3 = 0;
					while (num3 < num2 && this.waitLoadQueue.Count > 0)
					{
						AssetBundleItem assetBundleItem = this.waitLoadQueue.Dequeue();
						string bundleName = assetBundleItem.BundleName;
						Hash128 assetBundleHash = this.manifest.GetAssetBundleHash(bundleName);
						WaitLoadAssetBundle waitLoadAssetBundle = this.loader.Load(bundleName, assetBundleHash, false);
						bool flag3 = waitLoadAssetBundle == null || !string.IsNullOrEmpty(waitLoadAssetBundle.Error);
						if (flag3)
						{
							AssetBundleManager.logger.LogError("Load bundle {0} with hash {1} is failed.", new object[] { bundleName, assetBundleHash });
						}
						else
						{
							assetBundleItem.SetWaitLoadAssetBundle(waitLoadAssetBundle);
							this.loadingAssetBundles.AddLast(assetBundleItem);
						}
						num3++;
					}
				}
			}
		}

		private static Logger logger = LogSystem.GetLogger("AssetBundleManager");

		private AssetBundleLoader loader = null;

		private AssetBundleManifest manifest = null;

		private float lastCheckSweepTime = 0f;

		private int unloadedCountInTime = 0;

		private Func<string, AssetBundleItem, bool> sweepChecker;

		private string[] activeVariants = new string[0];

		private LinkedList<AssetBundleItem> loadingAssetBundles = new LinkedList<AssetBundleItem>();

		private Dictionary<string, AssetBundleItem> assetBundles = new Dictionary<string, AssetBundleItem>(StringComparer.Ordinal);

		private Queue<AssetBundleItem> waitLoadQueue = new Queue<AssetBundleItem>();

		private int maxLoadingCount = 5;
	}
}
