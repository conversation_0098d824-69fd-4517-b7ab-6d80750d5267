﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Layout/Flow Layout Group")]
	public sealed class FlowLayoutGroup : LayoutGroup
	{
		public Vector2 Spacing
		{
			get
			{
				return this.spacing;
			}
		}

		public float WorkingRowWidth
		{
			get
			{
				return this.workingRowWidth;
			}
		}

		public float LastRowWidth { get; set; }

		private bool IsCenterAlign
		{
			get
			{
				return base.childAlignment == 7 || base.childAlignment == 4 || base.childAlignment == 1;
			}
		}

		private bool IsRightAlign
		{
			get
			{
				return base.childAlignment == 8 || base.childAlignment == 5 || base.childAlignment == 2;
			}
		}

		private bool IsMiddleAlign
		{
			get
			{
				return base.childAlignment == 3 || base.childAlignment == 5 || base.childAlignment == 4;
			}
		}

		private bool IsLowerAlign
		{
			get
			{
				return base.childAlignment == 6 || base.childAlignment == 8 || base.childAlignment == 7;
			}
		}

		public override void CalculateLayoutInputHorizontal()
		{
			base.CalculateLayoutInputHorizontal();
			bool flag = this.contentSizeFitter == null;
			if (flag)
			{
				this.contentSizeFitter = base.GetComponent<ContentSizeFitter>();
			}
			bool flag2 = this.contentSizeFitter != null && this.contentSizeFitter.horizontalFit > 0;
			bool flag3 = flag2;
			if (flag3)
			{
				this.CalculateLayoutInputVertical();
			}
			float num = this.GetGreatestMinimumChildWidth() + (float)base.padding.left + (float)base.padding.right;
			bool flag4 = flag2;
			if (flag4)
			{
				num = Mathf.Max(new float[] { num, this.minRowWidth, this.maxRowWidth });
			}
			base.SetLayoutInputForAxis(num, -1f, -1f, 0);
		}

		public override void CalculateLayoutInputVertical()
		{
			this.layoutHeight = this.SetLayout(1, true);
		}

		public override void SetLayoutHorizontal()
		{
			this.SetLayout(0, false);
		}

		public override void SetLayoutVertical()
		{
			this.SetLayout(1, false);
		}

		public void UpdateLayoutHorizontal()
		{
			this.SetLayout(0, true);
		}

		public void UpdateLayoutVertical()
		{
			this.SetLayout(1, true);
		}

		private float SetLayout(int axis, bool layoutInput)
		{
			return this.SetLayout(axis, layoutInput, base.rectChildren);
		}

		private float SetLayout(int axis, bool layoutInput, IList<RectTransform> children)
		{
			float height = base.rectTransform.rect.height;
			bool flag = this.contentSizeFitter == null;
			if (flag)
			{
				this.contentSizeFitter = base.GetComponent<ContentSizeFitter>();
			}
			bool flag2 = this.contentSizeFitter != null && this.contentSizeFitter.horizontalFit > 0;
			float num;
			if (flag2)
			{
				num = this.workingRowWidth;
			}
			else
			{
				num = base.rectTransform.rect.width - (float)base.padding.left - (float)base.padding.right;
			}
			float num2 = (this.IsLowerAlign ? ((float)base.padding.bottom) : ((float)base.padding.top));
			this.LastRowWidth = 0f;
			this.maxRowWidth = 0f;
			float num3 = 0f;
			float num4 = 0f;
			int num5 = 0;
			for (int i = 0; i < children.Count; i++)
			{
				int num6 = (this.IsLowerAlign ? (children.Count - 1 - i) : i);
				RectTransform rectTransform = children[num6];
				float num7 = LayoutUtility.GetPreferredSize(rectTransform, 0);
				float preferredSize = LayoutUtility.GetPreferredSize(rectTransform, 1);
				num7 = Mathf.Min(num7, num);
				bool flag3 = num3 + num7 > num;
				if (flag3)
				{
					num3 -= this.spacing.x;
					bool flag4 = !layoutInput;
					if (flag4)
					{
						float num8 = this.CalculateRowVerticalOffset(height, num2, num4);
						this.LayoutRow(this.rowList, num3, num4, num, (float)base.padding.left, num8, axis, num5++);
					}
					this.rowList.Clear();
					num2 += num4;
					num2 += this.spacing.y;
					num4 = 0f;
					num3 = 0f;
				}
				num3 += num7;
				this.rowList.Add(rectTransform);
				bool flag5 = preferredSize > num4;
				if (flag5)
				{
					num4 = preferredSize;
				}
				bool flag6 = i < children.Count - 1;
				if (flag6)
				{
					num3 += this.spacing.x;
				}
				bool flag7 = this.maxRowWidth < num3;
				if (flag7)
				{
					this.maxRowWidth = num3;
				}
			}
			bool flag8 = !layoutInput;
			if (flag8)
			{
				float num9 = this.CalculateRowVerticalOffset(height, num2, num4);
				num3 -= this.spacing.x;
				this.LayoutRow(this.rowList, num3, num4, num - ((this.rowList.Count > 1) ? this.spacing.x : 0f), (float)base.padding.left, num9, axis, num5);
			}
			this.rowList.Clear();
			this.LastRowWidth = Mathf.Max(num3, 0f);
			num2 += num4;
			num2 += (float)(this.IsLowerAlign ? base.padding.top : base.padding.bottom);
			bool flag9 = layoutInput && axis == 1;
			if (flag9)
			{
				base.SetLayoutInputForAxis(num2, num2, -1f, axis);
			}
			return num2;
		}

		private float CalculateRowVerticalOffset(float groupHeight, float offsetY, float currentRowHeight)
		{
			bool isLowerAlign = this.IsLowerAlign;
			float num;
			if (isLowerAlign)
			{
				num = groupHeight - offsetY - currentRowHeight;
			}
			else
			{
				bool isMiddleAlign = this.IsMiddleAlign;
				if (isMiddleAlign)
				{
					num = groupHeight * 0.5f - this.layoutHeight * 0.5f + offsetY;
				}
				else
				{
					num = offsetY;
				}
			}
			return num;
		}

		private void LayoutRow(IList<RectTransform> contents, float rowWidth, float rowHeight, float maxWidth, float offsetX, float offsetY, int axis, int rowIndex)
		{
			bool flag = this.rowPadding != null && this.rowPadding.Length > rowIndex;
			if (flag)
			{
				offsetX += this.rowPadding[rowIndex];
			}
			float num = offsetX;
			bool isCenterAlign = this.IsCenterAlign;
			if (isCenterAlign)
			{
				RectTransform rectTransform = base.rectTransform;
				num += (rectTransform.sizeDelta.x - rowWidth) * 0.5f;
			}
			else
			{
				bool isRightAlign = this.IsRightAlign;
				if (isRightAlign)
				{
					RectTransform rectTransform2 = base.rectTransform;
					num += rectTransform2.sizeDelta.x - rowWidth;
				}
			}
			for (int i = 0; i < contents.Count; i++)
			{
				int num2 = (this.IsLowerAlign ? (contents.Count - 1 - i) : i);
				RectTransform rectTransform3 = contents[num2];
				float num3 = LayoutUtility.GetPreferredSize(rectTransform3, 0);
				float preferredSize = LayoutUtility.GetPreferredSize(rectTransform3, 1);
				num3 = Mathf.Min(num3, maxWidth);
				float num4 = offsetY;
				bool flag2 = this.IsMiddleAlign || this.rowAlignment == FlowLayoutGroup.RowAnchor.Middle;
				if (flag2)
				{
					num4 += (rowHeight - preferredSize) * 0.5f;
				}
				else
				{
					bool flag3 = this.IsLowerAlign || this.rowAlignment == FlowLayoutGroup.RowAnchor.Lower;
					if (flag3)
					{
						num4 += rowHeight - preferredSize;
					}
				}
				bool flag4 = axis == 0;
				if (flag4)
				{
					base.SetChildAlongAxis(rectTransform3, 0, num, num3);
				}
				else
				{
					base.SetChildAlongAxis(rectTransform3, 1, num4, preferredSize);
				}
				bool flag5 = i < contents.Count - 1;
				if (flag5)
				{
					num += num3 + this.spacing.x;
				}
			}
		}

		private float GetGreatestMinimumChildWidth()
		{
			float num = 0f;
			foreach (RectTransform rectTransform in base.rectChildren)
			{
				float minWidth = LayoutUtility.GetMinWidth(rectTransform);
				num = Mathf.Max(minWidth, num);
			}
			return num;
		}

		[SerializeField]
		private Vector2 spacing;

		[SerializeField]
		private FlowLayoutGroup.RowAnchor rowAlignment = FlowLayoutGroup.RowAnchor.Upper;

		[SerializeField]
		[Tooltip("Used working for ContentSizeFitter with horizontalFit")]
		private float workingRowWidth = 0f;

		[SerializeField]
		private float minRowWidth = 0f;

		[SerializeField]
		private float[] rowPadding;

		private ContentSizeFitter contentSizeFitter;

		private float layoutHeight;

		private float maxRowWidth;

		private IList<RectTransform> rowList = new List<RectTransform>();

		public enum RowAnchor
		{
			Upper,
			Middle,
			Lower
		}
	}
}
