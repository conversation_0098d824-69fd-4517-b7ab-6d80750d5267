﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class TextureCache
	{
		internal TextureCache(AssetID assetID, IDictionary<Texture, TextureCache> lookup)
		{
			this.assetID = assetID;
			this.lookup = lookup;
		}

		public AssetID AssetID
		{
			get
			{
				return this.assetID;
			}
		}

		public int ReferenceCount
		{
			get
			{
				return this.referenceCount;
			}
		}

		public float LastFreeTime
		{
			get
			{
				return this.lastFreeTime;
			}
		}

		public float ReleaseAfterFree
		{
			get
			{
				return this.releaseAfterFree;
			}
		}

		public bool IsSync
		{
			get
			{
				return this.isSync;
			}
			set
			{
				this.isSync = value;
			}
		}

		public bool TextAsset { get; private set; }

		public string Error { get; private set; }

		internal float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				this.releaseAfterFree = this.defaultReleaseAfterFree;
			}
		}

		internal void Retain()
		{
			this.referenceCount++;
		}

		internal void Release()
		{
			this.referenceCount--;
			this.lastFreeTime = Time.time;
			bool flag = this.referenceCount < 0;
			if (flag)
			{
				Debug.LogErrorFormat("[TextureCahce] referenceCount is error {0} {1}", new object[]
				{
					this.assetID.ToString(),
					this.referenceCount
				});
			}
		}

		internal void LoadObject(AssetID assetID)
		{
			Scheduler.RunCoroutine(this.LoadObjectImpl(assetID));
		}

		internal bool HasLoaded()
		{
			return this.cachedObject != null;
		}

		internal Texture GetObject()
		{
			return this.cachedObject;
		}

		private IEnumerator LoadObjectImpl(AssetID assetID)
		{
			WaitLoadObject waitobj = null;
			bool flag = this.isSync;
			if (flag)
			{
				waitobj = AssetManager.LoadObjectSync(assetID, typeof(Object));
			}
			else
			{
				waitobj = AssetManager.LoadObject(assetID, typeof(Object));
			}
			yield return waitobj;
			bool flag2 = !string.IsNullOrEmpty(waitobj.Error);
			if (flag2)
			{
				this.Error = waitobj.Error;
				yield break;
			}
			Object loadedObj = waitobj.GetObject();
			this.cachedObject = loadedObj as Texture;
			bool flag3 = this.cachedObject == null;
			if (flag3)
			{
				this.Error = string.Format("This asset: {0} is not a Texture", assetID);
				yield break;
			}
			this.releaseAfterFree = this.DefaultReleaseAfterFree;
			bool flag4 = this.lookup.ContainsKey(this.cachedObject);
			if (flag4)
			{
				TextureCache.logger.LogWarning("The texture {0} has been loaded.", new object[] { assetID });
				this.lookup[this.cachedObject] = this;
			}
			else
			{
				this.lookup.Add(this.cachedObject, this);
			}
			yield break;
		}

		private static Logger logger = LogSystem.GetLogger("TextureCache");

		private Texture cachedObject;

		private int referenceCount;

		private AssetID assetID;

		private IDictionary<Texture, TextureCache> lookup;

		private float lastFreeTime = -1f;

		private float defaultReleaseAfterFree = 15f;

		private float releaseAfterFree;

		private bool isSync;
	}
}
