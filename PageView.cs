﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class PageView : ListView
	{
		public int ActiveCellsMiddleIndex
		{
			get
			{
				Vector2 normalizedPosition = base.ScrollRect.normalizedPosition;
				Rect rect = base.ScrollView.rect;
				bool flag = this.pageDirection == PageView.PageDirectionEnum.Horizontal;
				int num7;
				if (flag)
				{
					float num = base.ScrollContent.rect.width - base.ScrollView.rect.width;
					float num2 = normalizedPosition.x * num;
					float num3 = (rect.width - this.pageSize.x) / 2f;
					float num4 = num2 + num3;
					float num5 = num4 + base.ScrollView.rect.width;
					float num6 = (num4 + num5) / 2f;
					num7 = Mathf.FloorToInt(this.GetCellIndexAtPositionXStart(num6));
				}
				else
				{
					float num8 = base.ScrollContent.rect.height - base.ScrollView.rect.height;
					float num9 = normalizedPosition.y * num8;
					float num10 = (rect.height - this.pageSize.y) / 2f;
					float num11 = num9 + num10;
					float num12 = num11 + base.ScrollView.rect.height;
					float num13 = (num11 + num12) / 2f;
					num7 = Mathf.FloorToInt(this.GetCellIndexAtPositionYStart(num13));
				}
				return num7;
			}
		}

		protected override Vector2 CalculateContentSize(int cellCount)
		{
			Rect rect = base.ScrollView.rect;
			bool flag = this.pageDirection == PageView.PageDirectionEnum.Horizontal;
			Vector2 vector;
			if (flag)
			{
				float num = this.pageSize.x * (float)cellCount;
				float num2 = this.spacing * (float)Mathf.Max(cellCount - 1, 0);
				float num3 = rect.width - this.pageSize.x;
				float num4 = num + num2 + num3;
				vector = new Vector2(num4, this.pageSize.y);
			}
			else
			{
				float num5 = this.pageSize.y * (float)cellCount;
				float num6 = this.spacing * (float)Mathf.Max(cellCount - 1, 0);
				float num7 = rect.height - this.pageSize.y;
				float num8 = num5 + num6 + num7;
				vector = new Vector2(this.pageSize.x, num8);
			}
			return vector;
		}

		protected override void CalculateCurrentActiveCellRange(Vector2 scrollPosition, out int startIndex, out int endIndex)
		{
			Rect rect = base.ScrollView.rect;
			bool flag = this.pageDirection == PageView.PageDirectionEnum.Horizontal;
			if (flag)
			{
				float num = base.ScrollContent.rect.width - base.ScrollView.rect.width;
				float num2 = scrollPosition.x * num;
				float num3 = (rect.width - this.pageSize.x) / 2f;
				float num4 = num2 + num3;
				float num5 = num4 + base.ScrollView.rect.width;
				startIndex = Mathf.FloorToInt(this.GetCellIndexAtPositionXStart(num4));
				endIndex = Mathf.FloorToInt(this.GetCellIndexAtPositionXEnd(num5));
			}
			else
			{
				float num6 = base.ScrollContent.rect.height - base.ScrollView.rect.height;
				float num7 = scrollPosition.y * num6;
				float num8 = (rect.height - this.pageSize.y) / 2f;
				float num9 = num7 + num8;
				float num10 = num9 + base.ScrollView.rect.height;
				startIndex = Mathf.FloorToInt(this.GetCellIndexAtPositionYStart(num9));
				endIndex = Mathf.FloorToInt(this.GetCellIndexAtPositionYEnd(num10));
			}
		}

		protected override void LayoutCell(GameObject cell, int index)
		{
			Rect rect = base.ScrollView.rect;
			RectTransform rectTransform = (RectTransform)cell.transform;
			rectTransform.anchorMin = Vector2.zero;
			rectTransform.anchorMax = Vector2.zero;
			rectTransform.sizeDelta = this.pageSize;
			bool flag = this.pageDirection == PageView.PageDirectionEnum.Horizontal;
			if (flag)
			{
				float num = (rect.width - this.pageSize.x) / 2f;
				float num2 = (float)index * (this.pageSize.x + this.spacing);
				num2 += 0.5f * this.pageSize.x;
				num2 += num;
				rectTransform.pivot = new Vector2(0.5f, 0.5f);
				rectTransform.anchoredPosition = new Vector2(num2, 0.5f * this.pageSize.y);
			}
			else
			{
				float num3 = (rect.height - this.pageSize.y) / 2f;
				float num4 = (float)index * (this.pageSize.y + this.spacing);
				num4 += 0.5f * this.pageSize.y;
				num4 += num3;
				rectTransform.pivot = new Vector2(0.5f, 0.5f);
				rectTransform.anchoredPosition = new Vector2(0.5f * this.pageSize.y, num4);
			}
		}

		protected override Vector2 GetCellPositionAtIndex(int index)
		{
			bool flag = this.pageDirection == PageView.PageDirectionEnum.Horizontal;
			Vector2 vector;
			if (flag)
			{
				float num = (float)index * (this.pageSize.x + this.spacing);
				vector = new Vector2(num, 0f);
			}
			else
			{
				float num2 = (float)index * (this.pageSize.y + this.spacing);
				vector = new Vector2(0f, num2);
			}
			return vector;
		}

		protected override void UpdateCell(GameObject cell, int index)
		{
			bool flag = this.scaleRange <= 0f;
			if (!flag)
			{
				RectTransform rectTransform = (RectTransform)cell.transform;
				bool flag2 = this.pageDirection == PageView.PageDirectionEnum.Horizontal;
				if (flag2)
				{
					float worldCenterX = rectTransform.GetWorldCenterX();
					float worldCenterX2 = base.ScrollView.GetWorldCenterX();
					float num = Mathf.Abs(worldCenterX - worldCenterX2);
					float num2 = Mathf.Max(this.pageScale, 1f / (1f + num / this.scaleRange));
					rectTransform.localScale = new Vector3(num2, num2, num2);
				}
				else
				{
					float worldCenterY = rectTransform.GetWorldCenterY();
					float worldCenterY2 = base.ScrollView.GetWorldCenterY();
					float num3 = Mathf.Abs(worldCenterY - worldCenterY2);
					float num4 = Mathf.Max(this.pageScale, 1f / (1f + num3 / this.scaleRange));
					rectTransform.localScale = new Vector3(num4, num4, num4);
				}
			}
		}

		protected override void UpdateSnapping()
		{
			bool flag = !this.snapping;
			if (!flag)
			{
				RectTransform scrollContent = base.ScrollContent;
				Rect rect = scrollContent.rect;
				Rect rect2 = base.ScrollView.rect;
				Vector2 normalizedPosition = base.ScrollRect.normalizedPosition;
				bool flag2 = this.pageDirection == PageView.PageDirectionEnum.Horizontal;
				if (flag2)
				{
					float num = rect.width - rect2.width;
					float num2 = normalizedPosition.x * num;
					int num3 = Mathf.RoundToInt(num2 / (this.pageSize.x + this.spacing));
					float num4 = (float)num3 * (this.pageSize.x + this.spacing);
					float num5 = num / 2f - num4;
					Vector2 anchoredPosition = scrollContent.anchoredPosition;
					float num6 = Mathf.Abs(num5 - anchoredPosition.x);
					bool flag3 = num6 > 0.03f * this.snapSpeed;
					if (flag3)
					{
						float num7 = Mathf.Lerp(anchoredPosition.x, num5, Time.deltaTime * this.snapSpeed);
						scrollContent.anchoredPosition = new Vector2(num7, anchoredPosition.y);
					}
					else
					{
						scrollContent.anchoredPosition = new Vector2(num5, anchoredPosition.y);
					}
				}
				else
				{
					float num8 = rect.height - rect2.height;
					float num9 = normalizedPosition.y * num8;
					int num10 = Mathf.RoundToInt(num9 / (this.pageSize.y + this.spacing));
					float num11 = (float)num10 * (this.pageSize.y + this.spacing);
					float num12 = num8 / 2f - num11;
					Vector2 anchoredPosition2 = scrollContent.anchoredPosition;
					float num13 = Mathf.Abs(num12 - anchoredPosition2.y);
					bool flag4 = num13 > 0.03f * this.snapSpeed;
					if (flag4)
					{
						float num14 = Mathf.Lerp(anchoredPosition2.y, num12, Time.deltaTime * this.snapSpeed);
						scrollContent.anchoredPosition = new Vector2(anchoredPosition2.x, num14);
					}
					else
					{
						scrollContent.anchoredPosition = new Vector2(anchoredPosition2.x, num12);
					}
				}
			}
		}

		private float GetCellIndexAtPositionXStart(float position)
		{
			float num = base.ScrollView.rect.width - this.pageSize.x - 0.5f * this.spacing;
			return (position - num) / (this.pageSize.x + this.spacing);
		}

		private float GetCellIndexAtPositionXEnd(float position)
		{
			float num = base.ScrollView.rect.width - this.pageSize.x + 0.5f * this.spacing;
			return (position - num) / (this.pageSize.x + this.spacing) + 1f;
		}

		private float GetCellIndexAtPositionYStart(float position)
		{
			float num = base.ScrollView.rect.height - this.pageSize.y - 0.5f * this.spacing;
			return (position - num) / (this.pageSize.y + this.spacing);
		}

		private float GetCellIndexAtPositionYEnd(float position)
		{
			float num = base.ScrollView.rect.height - this.pageSize.y + 0.5f * this.spacing;
			return (position - num) / (this.pageSize.y + this.spacing) + 1f;
		}

		[SerializeField]
		[Tooltip("The page scroll direction.")]
		private PageView.PageDirectionEnum pageDirection;

		[SerializeField]
		[Tooltip("The size of each page.")]
		private Vector2 pageSize;

		[SerializeField]
		[Tooltip("The space between each page.")]
		private float spacing = 0f;

		[SerializeField]
		[Tooltip("The scale when page not in the center.")]
		private float pageScale = 0.7f;

		[SerializeField]
		[Tooltip("The range for the scale animation.")]
		private float scaleRange = 200f;

		[SerializeField]
		[Tooltip("Whether snapping the page in the center.")]
		private bool snapping;

		[SerializeField]
		[Tooltip("The snap animation speed.")]
		private float snapSpeed = 1f;

		public enum PageDirectionEnum
		{
			Horizontal,
			Vertical
		}
	}
}
