﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class InstantiateQueue
	{
		public InstantiateQueue()
		{
			this.updateHandle = Scheduler.AddFrameListener(new Action(this.Update));
		}

		~InstantiateQueue()
		{
			Scheduler.RemoveFrameListener(this.updateHandle);
			this.updateHandle = null;
		}

		public static InstantiateQueue Global { get; set; }

		public int InstantiateCountPerFrame
		{
			get
			{
				return this.instantiateCountPerFrame;
			}
			set
			{
				this.instantiateCountPerFrame = value;
			}
		}

		internal void Instantiate(GameObject prefab, int priority, Action<GameObject> callback)
		{
			bool flag = this.instantiatedCount < this.InstantiateCountPerFrame;
			if (flag)
			{
				GameObject gameObject = Singleton<PrefabPool>.Instance.Instantiate(prefab);
				this.instantiatedCount++;
				callback(gameObject);
			}
			else
			{
				InstantiateQueue.Task task = new InstantiateQueue.Task
				{
					Prefab = prefab,
					Callback = callback
				};
				this.pendingTasks.Push(task);
			}
		}

		private void Update()
		{
			try
			{
				while (this.pendingTasks.Count > 0)
				{
					bool flag = this.instantiatedCount < this.InstantiateCountPerFrame;
					if (!flag)
					{
						break;
					}
					InstantiateQueue.Task task = this.pendingTasks.Pop();
					GameObject gameObject = Singleton<PrefabPool>.Instance.Instantiate(task.Prefab);
					this.instantiatedCount++;
					task.Callback(gameObject);
				}
			}
			finally
			{
				this.instantiatedCount = 0;
			}
		}

		private LinkedListNode<Action> updateHandle;

		private int instantiatedCount;

		private int instantiateCountPerFrame = 1;

		private PriorityQueue<InstantiateQueue.Task> pendingTasks = new PriorityQueue<InstantiateQueue.Task>(InstantiateQueue.TaskComparer.Default);

		private struct Task
		{
			public GameObject Prefab;

			public Action<GameObject> Callback;

			public int Priority;
		}

		private class TaskComparer : IComparer<InstantiateQueue.Task>
		{
			public static InstantiateQueue.TaskComparer Default
			{
				get
				{
					bool flag = InstantiateQueue.TaskComparer.defaultInstance == null;
					if (flag)
					{
						InstantiateQueue.TaskComparer.defaultInstance = new InstantiateQueue.TaskComparer();
					}
					return InstantiateQueue.TaskComparer.defaultInstance;
				}
			}

			public int Compare(InstantiateQueue.Task x, InstantiateQueue.Task y)
			{
				return x.Priority.CompareTo(y.Priority);
			}

			private static InstantiateQueue.TaskComparer defaultInstance;
		}
	}
}
