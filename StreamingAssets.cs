﻿using System;
using System.IO;
using UnityEngine;

namespace Nirvana
{
	public static class StreamingAssets
	{
		public static string ReadAllText(string filePath)
		{
			string text = Path.Combine(Application.streamingAssetsPath, filePath);
			string text2;
			if (File.Exists(text))
			{
				text2 = File.ReadAllText(text);
			}
			else
			{
				text2 = string.Empty;
			}
			return text2;
		}

		public static bool Existed(string filePath)
		{
			return File.Exists(Path.Combine(Application.streamingAssetsPath, filePath));
		}

		public static string EncryptReadAllText(string filePath, byte[] encryptKey)
		{
			return string.Empty;
		}

		public static byte[] ReadAllByte(string filePath)
		{
			string text = Path.Combine(Application.streamingAssetsPath, filePath);
			if (File.Exists(text))
			{
				return File.ReadAllBytes(text);
			}
			return null;
		}
	}
}
