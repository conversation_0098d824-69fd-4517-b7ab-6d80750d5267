﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class MaterialCache
	{
		internal MaterialCache(AssetID assetID, IDictionary<Material, MaterialCache> lookup)
		{
			this.assetID = assetID;
			this.lookup = lookup;
		}

		public AssetID AssetID
		{
			get
			{
				return this.assetID;
			}
		}

		public int ReferenceCount
		{
			get
			{
				return this.referenceCount;
			}
		}

		public float LastFreeTime
		{
			get
			{
				return this.lastFreeTime;
			}
		}

		public float ReleaseAfterFree
		{
			get
			{
				return this.releaseAfterFree;
			}
		}

		public string Error { get; private set; }

		internal float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				this.releaseAfterFree = this.defaultReleaseAfterFree;
			}
		}

		internal void Retain()
		{
			this.referenceCount++;
		}

		internal void Release()
		{
			this.referenceCount--;
			this.lastFreeTime = Time.realtimeSinceStartup;
		}

		internal void LoadObject(AssetID assetID)
		{
			Scheduler.RunCoroutine(this.LoadObjectImpl(assetID));
		}

		internal bool HasLoaded()
		{
			return this.cachedObject != null;
		}

		internal Material GetObject()
		{
			return this.cachedObject;
		}

		private IEnumerator LoadObjectImpl(AssetID assetID)
		{
			WaitLoadObject waitobj = AssetManager.LoadObject(assetID, typeof(Material));
			yield return waitobj;
			bool flag = !string.IsNullOrEmpty(waitobj.Error);
			if (flag)
			{
				this.Error = waitobj.Error;
				yield break;
			}
			this.cachedObject = waitobj.GetObject() as Material;
			bool flag2 = this.cachedObject == null;
			if (flag2)
			{
				this.Error = string.Format("The asset {0} is not a Material.", assetID);
				yield break;
			}
			this.releaseAfterFree = this.DefaultReleaseAfterFree;
			bool flag3 = this.lookup.ContainsKey(this.cachedObject);
			if (flag3)
			{
				MaterialCache.logger.LogWarning("The material {0} has been loaded.", new object[] { assetID });
				this.lookup[this.cachedObject] = this;
			}
			else
			{
				this.lookup.Add(this.cachedObject, this);
			}
			yield break;
		}

		private static Logger logger = LogSystem.GetLogger("MaterialCache");

		private Material cachedObject;

		private int referenceCount;

		private AssetID assetID;

		private IDictionary<Material, MaterialCache> lookup;

		private float lastFreeTime = -1f;

		private float defaultReleaseAfterFree = 30f;

		private float releaseAfterFree;
	}
}
