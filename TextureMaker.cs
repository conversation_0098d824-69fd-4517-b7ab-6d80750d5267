﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class TextureMaker
	{
		public static Texture2D Gray(float gray)
		{
			return TextureMaker.Monochromatic(new Color(gray, gray, gray, 1f));
		}

		public static Texture2D Gray(int size, float gray)
		{
			return TextureMaker.Monochromatic(size, new Color(gray, gray, gray, 1f));
		}

		public static Texture2D Monochromatic(Color color)
		{
			Texture2D texture2D = new Texture2D(1, 1, 5, false);
			texture2D.filterMode = 0;
			texture2D.wrapMode = 1;
			texture2D.SetPixel(0, 0, color);
			texture2D.Apply();
			return texture2D;
		}

		public static Texture2D Monochromatic(int size, Color color)
		{
			Texture2D texture2D = new Texture2D(size, size, 5, false);
			texture2D.filterMode = 1;
			texture2D.wrapMode = 1;
			for (int i = 0; i < size; i++)
			{
				for (int j = 0; j < size; j++)
				{
					texture2D.SetPixel(i, j, color);
				}
			}
			texture2D.Apply();
			return texture2D;
		}

		public static Texture2D Dot(int size, Color fg, Color bg)
		{
			Texture2D texture2D = new Texture2D(size, size, 5, false);
			texture2D.filterMode = 1;
			texture2D.wrapMode = 1;
			int num = size / 2;
			int num2 = size / 2 * (size / 2);
			for (int i = 0; i < size; i++)
			{
				for (int j = 0; j < size; j++)
				{
					int num3 = i - num;
					int num4 = j - num;
					bool flag = num3 * num3 + num4 * num4 < num2;
					if (flag)
					{
						texture2D.SetPixel(i, j, fg);
					}
					else
					{
						texture2D.SetPixel(i, j, bg);
					}
				}
			}
			texture2D.Apply();
			return texture2D;
		}

		public static Texture2D CornerTopLeft(int size, Color fg, Color bg)
		{
			Texture2D texture2D = new Texture2D(size, size, 5, false);
			texture2D.filterMode = 1;
			texture2D.wrapMode = 1;
			int num = size / 2;
			for (int i = 0; i < size; i++)
			{
				for (int j = 0; j < size; j++)
				{
					bool flag = i < num && j > num;
					if (flag)
					{
						texture2D.SetPixel(i, j, fg);
					}
					else
					{
						texture2D.SetPixel(i, j, bg);
					}
				}
			}
			texture2D.Apply();
			return texture2D;
		}

		public static Texture2D CornerTopRight(int size, Color fg, Color bg)
		{
			Texture2D texture2D = new Texture2D(size, size, 5, false);
			texture2D.filterMode = 1;
			texture2D.wrapMode = 1;
			int num = size / 2;
			for (int i = 0; i < size; i++)
			{
				for (int j = 0; j < size; j++)
				{
					bool flag = i > num && j > num;
					if (flag)
					{
						texture2D.SetPixel(i, j, fg);
					}
					else
					{
						texture2D.SetPixel(i, j, bg);
					}
				}
			}
			texture2D.Apply();
			return texture2D;
		}

		public static Texture2D CornerBottomLeft(int size, Color fg, Color bg)
		{
			Texture2D texture2D = new Texture2D(size, size, 5, false);
			texture2D.filterMode = 1;
			texture2D.wrapMode = 1;
			int num = size / 2;
			for (int i = 0; i < size; i++)
			{
				for (int j = 0; j < size; j++)
				{
					bool flag = i < num && j < num;
					if (flag)
					{
						texture2D.SetPixel(i, j, fg);
					}
					else
					{
						texture2D.SetPixel(i, j, bg);
					}
				}
			}
			texture2D.Apply();
			return texture2D;
		}

		public static Texture2D CornerBottomRight(int size, Color fg, Color bg)
		{
			Texture2D texture2D = new Texture2D(size, size, 5, false);
			texture2D.filterMode = 1;
			texture2D.wrapMode = 1;
			int num = size / 2;
			for (int i = 0; i < size; i++)
			{
				for (int j = 0; j < size; j++)
				{
					bool flag = i > num && j < num;
					if (flag)
					{
						texture2D.SetPixel(i, j, fg);
					}
					else
					{
						texture2D.SetPixel(i, j, bg);
					}
				}
			}
			texture2D.Apply();
			return texture2D;
		}

		public static Texture2D Cross(int size, int thickness, Color fg, Color bg)
		{
			Texture2D texture2D = new Texture2D(size, size, 5, false);
			texture2D.filterMode = 1;
			texture2D.wrapMode = 1;
			for (int i = 0; i < size; i++)
			{
				for (int j = 0; j < size; j++)
				{
					bool flag = Mathf.Abs(i - j) <= thickness || Mathf.Abs(i + j - size) <= thickness;
					if (flag)
					{
						texture2D.SetPixel(i, j, fg);
					}
					else
					{
						texture2D.SetPixel(i, j, bg);
					}
				}
			}
			texture2D.Apply();
			return texture2D;
		}
	}
}
