﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Image Bool")]
	[RequireComponent(typeof(Image))]
	public sealed class UIVariableBindImageBool : UIVariableBindBool
	{
		protected override void OnValueChanged()
		{
			bool flag = this.image == null;
			if (flag)
			{
				this.image = base.GetComponent<Image>();
			}
			bool result = base.GetResult();
			if (result)
			{
				this.image.sprite = this.on;
			}
			else
			{
				this.image.sprite = this.off;
			}
		}

		[SerializeField]
		[Tooltip("The sprite used when the boolean is true.")]
		private Sprite on;

		[SerializeField]
		[Tooltip("The sprite used when the boolean is false.")]
		private Sprite off;

		private Image image;
	}
}
