﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[Obsolete("This class is obsolete, please use Nirvana.ToggleSprite instead.")]
	public sealed class UIToggleSprite : MonoBehaviour
	{
		private void Awake()
		{
			this.toggle = base.GetComponent<Toggle>();
			bool flag = this.toggle != null;
			if (flag)
			{
				this.image = this.toggle.targetGraphic.GetComponent<Image>();
				this.origin = this.image.sprite;
				this.OnValueChanged(this.toggle.isOn);
				this.toggle.onValueChanged.AddListener(new UnityAction<bool>(this.OnValueChanged));
			}
		}

		private void OnDestroy()
		{
			bool flag = this.toggle != null;
			if (flag)
			{
				this.toggle.onValueChanged.RemoveListener(new UnityAction<bool>(this.OnValueChanged));
			}
		}

		private void OnValueChanged(bool isOn)
		{
			bool flag = this.image != null;
			if (flag)
			{
				this.image.sprite = (isOn ? this.toggledSprite : this.origin);
			}
		}

		[SerializeField]
		[Tooltip("The sprite changed when toggled.")]
		private Sprite toggledSprite;

		private Toggle toggle;

		private Image image;

		private Sprite origin;
	}
}
