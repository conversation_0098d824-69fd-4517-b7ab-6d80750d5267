﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	internal sealed class SimpleShadowManager : Singleton<SimpleShadowManager>
	{
		internal SimpleShadowManager.ShadowID AddShadow(SimpleShadow shadow)
		{
			Material shadowMaterial = shadow.ShadowMaterial;
			SimpleShadowMesh simpleShadowMesh;
			bool flag = !this.shadowMeshs.TryGetValue(shadowMaterial, out simpleShadowMesh);
			if (flag)
			{
				GameObject gameObject = new GameObject("ShadowMesh: " + shadowMaterial.name);
				Object.DontDestroyOnLoad(gameObject);
				simpleShadowMesh = gameObject.AddComponent<SimpleShadowMesh>();
				simpleShadowMesh.ShadowMaterial = shadowMaterial;
				this.shadowMeshs.Add(shadowMaterial, simpleShadowMesh);
			}
			LinkedListNode<SimpleShadow> linkedListNode = simpleShadowMesh.AddShadow(shadow);
			return new SimpleShadowManager.ShadowID
			{
				ShadowMesh = simpleShadowMesh,
				Node = linkedListNode
			};
		}

		internal void RemoveShadow(SimpleShadowManager.ShadowID shadowID)
		{
			shadowID.ShadowMesh.RemoveShadow(shadowID.Node);
		}

		private Dictionary<Material, SimpleShadowMesh> shadowMeshs = new Dictionary<Material, SimpleShadowMesh>();

		internal struct ShadowID
		{
			internal bool IsValid
			{
				get
				{
					return this.ShadowMesh != null && this.Node != null;
				}
			}

			internal void Reset()
			{
				this.ShadowMesh = null;
				this.Node = null;
			}

			internal SimpleShadowMesh ShadowMesh;

			internal LinkedListNode<SimpleShadow> Node;
		}
	}
}
