﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	[DisallowMultipleComponent]
	public sealed class GameObjectPool : Singleton<GameObjectPool>
	{
		public GameObjectPool()
		{
			this.sweepAssetChecker = delegate(AssetID k, GameObjectCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					cache.SweepCache();
					bool flag3 = !cache.Loading && cache.CacheCount == 0 && cache.SpawnCount == 0;
					if (flag3)
					{
						cache.ClearPrefab();
						flag2 = true;
					}
					else
					{
						flag2 = false;
					}
				}
				return flag2;
			};
			this.sweepObjectChecker = delegate(GameObject k, GameObjectCache cache)
			{
				bool flag4 = !string.IsNullOrEmpty(cache.Error);
				bool flag5;
				if (flag4)
				{
					flag5 = true;
				}
				else
				{
					cache.SweepCache();
					bool flag6 = !cache.Loading && cache.CacheCount == 0 && cache.SpawnCount == 0;
					if (flag6)
					{
						cache.ClearPrefab();
						flag5 = true;
					}
					else
					{
						flag5 = false;
					}
				}
				return flag5;
			};
			Scheduler.AddFrameListener(new Action(this.SweepCache));
			GameObject gameObject = new GameObject("GameObjectPool");
			gameObject.SetActive(false);
			Object.DontDestroyOnLoad(gameObject);
			this.root = gameObject.transform;
		}

		public float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				foreach (KeyValuePair<AssetID, GameObjectCache> keyValuePair in this.assetCaches)
				{
					keyValuePair.Value.DefaultReleaseAfterFree = value;
				}
				foreach (KeyValuePair<GameObject, GameObjectCache> keyValuePair2 in this.objectCaches)
				{
					keyValuePair2.Value.DefaultReleaseAfterFree = value;
				}
			}
		}

		public int DefaultInstancePoolCount
		{
			get
			{
				return this.defaultInstancePoolCount;
			}
			set
			{
				this.defaultInstancePoolCount = value;
				foreach (KeyValuePair<AssetID, GameObjectCache> keyValuePair in this.assetCaches)
				{
					keyValuePair.Value.DefaultInstancePoolCount = value;
				}
				foreach (KeyValuePair<GameObject, GameObjectCache> keyValuePair2 in this.objectCaches)
				{
					keyValuePair2.Value.DefaultInstancePoolCount = value;
				}
			}
		}

		public WaitSpawnGameObject SpawnAsset(string bundle, string asset)
		{
			return this.SpawnAssetWithQueue(bundle, asset, null, 0);
		}

		public WaitSpawnGameObject SpawnAsset(AssetID assetID)
		{
			return this.SpawnAssetWithQueue(assetID, null, 0);
		}

		public WaitSpawnGameObject SpawnAssetWithQueue(string bundle, string asset, InstantiateQueue instantiateQueue, int instantiatePriority)
		{
			return this.SpawnAssetWithQueue(new AssetID(bundle, asset), instantiateQueue, instantiatePriority);
		}

		public WaitSpawnGameObject SpawnAssetWithQueue(AssetID assetID, InstantiateQueue instantiateQueue, int instantiatePriority)
		{
			Assert.IsFalse(assetID.IsEmpty);
			GameObjectCache gameObjectCache;
			bool flag = !this.assetCaches.TryGetValue(assetID, out gameObjectCache);
			if (flag)
			{
				gameObjectCache = new GameObjectCache(this.lookup);
				gameObjectCache.DefaultReleaseAfterFree = this.defaultReleaseAfterFree;
				gameObjectCache.DefaultInstancePoolCount = this.defaultInstancePoolCount;
				this.assetCaches.Add(assetID, gameObjectCache);
				gameObjectCache.LoadPrefab(assetID);
			}
			return new WaitSpawnGameObject(gameObjectCache, instantiateQueue, instantiatePriority);
		}

		public GameObject Spawn(GameObject prefab, Transform parent)
		{
			Assert.IsNotNull<GameObject>(prefab);
			GameObjectCache gameObjectCache;
			bool flag = !this.objectCaches.TryGetValue(prefab, out gameObjectCache);
			if (flag)
			{
				gameObjectCache = new GameObjectCache(this.lookup);
				gameObjectCache.DefaultReleaseAfterFree = this.defaultReleaseAfterFree;
				gameObjectCache.DefaultInstancePoolCount = this.defaultInstancePoolCount;
				this.objectCaches.Add(prefab, gameObjectCache);
				gameObjectCache.SetPrefab(prefab);
			}
			return gameObjectCache.Spawn(parent);
		}

		public T Spawn<T>(T prefab, Transform parent) where T : Component
		{
			Assert.IsNotNull<T>(prefab);
			GameObject gameObject = this.Spawn(prefab.gameObject, parent);
			T component = gameObject.GetComponent<T>();
			bool flag = component == null;
			T t;
			if (flag)
			{
				GameObjectPool.logger.LogError("Can not load prefab with componet: {0}", new object[] { typeof(T).Name });
				t = default(T);
			}
			else
			{
				t = component;
			}
			return t;
		}

		public void SetDefaultReleaseAfterFree(AssetID assetID, int value)
		{
			GameObjectCache gameObjectCache;
			bool flag = this.assetCaches.TryGetValue(assetID, out gameObjectCache);
			if (flag)
			{
				gameObjectCache.DefaultReleaseAfterFree = (float)value;
			}
		}

		public void Free(GameObject instance, bool destroy = false)
		{
			bool flag = instance == null;
			if (flag)
			{
				GameObjectPool.logger.LogError("Try to free a null GameObject.");
			}
			else
			{
				bool flag2 = this.root == null;
				if (flag2)
				{
					Object.Destroy(instance);
				}
				else
				{
					GameObjectCache gameObjectCache;
					bool flag3 = !this.lookup.TryGetValue(instance, out gameObjectCache);
					if (flag3)
					{
						GameObjectPool.logger.LogWarning("Try to free an instance {0} not allocated by this pool.", new object[] { instance.name });
						Object.Destroy(instance);
					}
					else
					{
						this.lookup.Remove(instance);
						instance.SetActive(false);
						instance.transform.SetParent(this.root);
						gameObjectCache.PutBack(instance, destroy);
					}
				}
			}
		}

		public void Clear()
		{
			foreach (KeyValuePair<AssetID, GameObjectCache> keyValuePair in this.assetCaches)
			{
				keyValuePair.Value.ClearCache();
			}
			this.assetCaches.Clear();
			foreach (KeyValuePair<GameObject, GameObjectCache> keyValuePair2 in this.objectCaches)
			{
				keyValuePair2.Value.ClearCache();
			}
			this.objectCaches.Clear();
			this.lookup.Clear();
		}

		public void ClearAllUnused()
		{
			this.assetCaches.RemoveAll(delegate(AssetID k, GameObjectCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					cache.ClearCache();
					bool flag3 = !cache.Loading && cache.CacheCount == 0 && cache.SpawnCount == 0;
					if (flag3)
					{
						cache.ClearPrefab();
						flag2 = true;
					}
					else
					{
						flag2 = false;
					}
				}
				return flag2;
			});
			this.objectCaches.RemoveAll(delegate(GameObject k, GameObjectCache cache)
			{
				bool flag4 = !string.IsNullOrEmpty(cache.Error);
				bool flag5;
				if (flag4)
				{
					flag5 = true;
				}
				else
				{
					cache.ClearCache();
					bool flag6 = !cache.Loading && cache.CacheCount == 0 && cache.SpawnCount == 0;
					if (flag6)
					{
						cache.ClearPrefab();
						flag5 = true;
					}
					else
					{
						flag5 = false;
					}
				}
				return flag5;
			});
		}

		private void SweepCache()
		{
			this.assetCaches.RemoveAll(this.sweepAssetChecker);
			this.objectCaches.RemoveAll(this.sweepObjectChecker);
		}

		private static Logger logger = LogSystem.GetLogger("GameObjectPool");

		private Dictionary<AssetID, GameObjectCache> assetCaches = new Dictionary<AssetID, GameObjectCache>();

		private Dictionary<GameObject, GameObjectCache> objectCaches = new Dictionary<GameObject, GameObjectCache>();

		private Dictionary<GameObject, GameObjectCache> lookup = new Dictionary<GameObject, GameObjectCache>();

		private Transform root;

		private float defaultReleaseAfterFree = 30f;

		private int defaultInstancePoolCount = 16;

		private Func<AssetID, GameObjectCache, bool> sweepAssetChecker;

		private Func<GameObject, GameObjectCache, bool> sweepObjectChecker;
	}
}
