﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Layout/Radial Layout Group")]
	public sealed class RadialLayoutGroup : LayoutGroup
	{
		public override void CalculateLayoutInputVertical()
		{
		}

		public override void SetLayoutHorizontal()
		{
			this.CalculateRadial();
		}

		public override void SetLayoutVertical()
		{
			this.CalculateRadial();
		}

		private void CalculateRadial()
		{
			this.m_Tracker.Clear();
			bool flag = base.transform.childCount == 0;
			if (!flag)
			{
				float num = (this.maxAngle - this.minAngle) / (float)base.transform.childCount;
				for (int i = 0; i < base.transform.childCount; i++)
				{
					RectTransform rectTransform = (RectTransform)base.transform.GetChild(i);
					float num2 = this.startAngle + (float)i * num;
					Vector3 vector;
					vector..ctor(Mathf.Cos(num2 * 0.017453292f), Mathf.Sin(num2 * 0.017453292f), 0f);
					rectTransform.localPosition = vector * this.distance;
					DrivenTransformProperties drivenTransformProperties = 52998;
					this.m_Tracker.Add(this, rectTransform, drivenTransformProperties);
					rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
					rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
					rectTransform.pivot = new Vector2(0.5f, 0.5f);
				}
			}
		}

		[SerializeField]
		private float distance;

		[SerializeField]
		[Range(0f, 360f)]
		private float minAngle;

		[SerializeField]
		[Range(0f, 360f)]
		private float maxAngle;

		[SerializeField]
		[Range(0f, 360f)]
		private float startAngle;
	}
}
