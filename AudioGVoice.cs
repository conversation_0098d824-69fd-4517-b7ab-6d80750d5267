﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class AudioGVoice
	{
		public static void InitFeesVoice(string appID, string appKey, string openID, string ServerInfo = "", int Language = 0, Action<bool, string, string> complete = null)
		{
			AndroidJavaObject audioGVoice = AudioGVoice.GetAudioGVoice();
			AudioGVoice.audioGVoiceListener.InitDelegate = delegate(bool isSuccess, string msg)
			{
				bool flag = complete != null;
				if (flag)
				{
					complete(isSuccess, openID, msg);
				}
			};
			AudioGVoice.audioGVoiceListener.ErrorDelegate = delegate(string ret, string msg)
			{
				bool flag2 = complete != null;
				if (flag2)
				{
					complete(false, ret, msg);
				}
			};
			audioGVoice.CallStatic("initGvoice", new object[] { appID, appKey, openID, ServerInfo, Language });
		}

		public static void SpeechToText(string _fileID, Action<bool, string, string> complete = null)
		{
			AndroidJavaObject audioGVoice = AudioGVoice.GetAudioGVoice();
			AudioGVoice.audioGVoiceListener.SpeechToTextDelegate = delegate(string fileID, string text)
			{
				bool flag = complete != null;
				if (flag)
				{
					complete(true, fileID, text);
				}
			};
			AudioGVoice.audioGVoiceListener.ErrorDelegate = delegate(string ret, string msg)
			{
				bool flag2 = complete != null;
				if (flag2)
				{
					complete(false, ret, msg);
				}
			};
			audioGVoice.CallStatic("voiceToText", new object[] { _fileID });
		}

		public static void StartRecorder()
		{
			AudioGVoice.GetAudioGVoice().CallStatic("startRecorder", new object[0]);
		}

		public static bool StartRecorder2()
		{
			return AudioGVoice.GetAudioGVoice().CallStatic<bool>("startRecorder2", new object[0]);
		}

		public static void StopRecorder(Action<bool, string, string> complete = null)
		{
			AndroidJavaObject audioGVoice = AudioGVoice.GetAudioGVoice();
			AudioGVoice.audioGVoiceListener.RecorderDelegate = delegate(string fileID, string str)
			{
				bool flag = complete != null;
				if (flag)
				{
					complete(true, fileID, str);
				}
			};
			AudioGVoice.audioGVoiceListener.ErrorDelegate = delegate(string ret, string msg)
			{
				bool flag2 = complete != null;
				if (flag2)
				{
					complete(false, ret, msg);
				}
			};
			audioGVoice.CallStatic("stopRecorder", new object[0]);
		}

		public static void StartPlay(string fileID, Action<bool, string, string> complete = null)
		{
			AndroidJavaObject audioGVoice = AudioGVoice.GetAudioGVoice();
			AudioGVoice.audioGVoiceListener.PlayEndDelegate = delegate
			{
				bool flag = complete != null;
				if (flag)
				{
					complete(true, "0", "0");
				}
			};
			AudioGVoice.audioGVoiceListener.ErrorDelegate = delegate(string ret, string msg)
			{
				bool flag2 = complete != null;
				if (flag2)
				{
					complete(false, ret, msg);
				}
			};
			audioGVoice.CallStatic("startPlay", new object[] { fileID });
		}

		public static void StopPlay()
		{
			AudioGVoice.GetAudioGVoice().CallStatic("stopPlay", new object[0]);
		}

		private static AndroidJavaClass GetAudioGVoice()
		{
			bool flag = AudioGVoice.audioGVoiceClass == null;
			if (flag)
			{
				AudioGVoice.audioGVoiceClass = new AndroidJavaClass("com.winunet.and.SoundGvoice");
				AudioGVoice.audioGVoiceClass.CallStatic("setListener", new object[] { AudioGVoice.audioGVoiceListener });
			}
			return AudioGVoice.audioGVoiceClass;
		}

		private static AndroidJavaClass audioGVoiceClass;

		private static AudioGVoice.AudioGVoiceListener audioGVoiceListener = new AudioGVoice.AudioGVoiceListener();

		private class AudioGVoiceListener : AndroidJavaProxy
		{
			public AudioGVoiceListener()
				: base("com.winunet.and.SoundGvoiceListener")
			{
			}

			public Action<string, string> RecorderDelegate { get; set; }

			public Action PlayEndDelegate { get; set; }

			public Action<string, string> ErrorDelegate { get; set; }

			public Action<string, string> SpeechToTextDelegate { get; set; }

			public Action<bool, string> InitDelegate { get; set; }

			private void onRecorderCallback(string fileID, string str)
			{
				SdkScheduler.PostTask(delegate
				{
					bool flag = this.RecorderDelegate != null;
					if (flag)
					{
						this.RecorderDelegate(fileID, str);
					}
				});
			}

			private void onPlayerCallback()
			{
				SdkScheduler.PostTask(delegate
				{
					bool flag = this.PlayEndDelegate != null;
					if (flag)
					{
						this.PlayEndDelegate();
					}
				});
			}

			private void onErrorCallback(string ret, string msg)
			{
				SdkScheduler.PostTask(delegate
				{
					bool flag = this.ErrorDelegate != null;
					if (flag)
					{
						this.ErrorDelegate(ret, msg);
					}
				});
			}

			private void onSpeechToTextCallback(string fileID, string text)
			{
				SdkScheduler.PostTask(delegate
				{
					bool flag = this.SpeechToTextDelegate != null;
					if (flag)
					{
						this.SpeechToTextDelegate(fileID, text);
					}
				});
			}

			private void onInitCallback(bool isSuccess, string msg)
			{
				SdkScheduler.PostTask(delegate
				{
					bool flag = this.InitDelegate != null;
					if (flag)
					{
						this.InitDelegate(isSuccess, msg);
					}
				});
			}
		}
	}
}
