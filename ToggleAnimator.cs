﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/Toggle Animator")]
	[RequireComponent(typeof(Toggle))]
	public sealed class ToggleAnimator : MonoBehaviour
	{
		private void Awake()
		{
			this.toggle = base.GetComponent<Toggle>();
			Assert.IsNotNull<Toggle>(this.toggle);
			this.OnToggleChanged(this.toggle.isOn);
			this.toggle.onValueChanged.AddListener(new UnityAction<bool>(this.OnToggleChanged));
		}

		private void OnEnable()
		{
			this.OnToggleChanged(this.toggle.isOn);
		}

		private void OnToggleChanged(bool isOn)
		{
			bool flag = this.animator.isInitialized && this.animator.isActiveAndEnabled;
			if (flag)
			{
				this.animator.SetBool(this.stateName, isOn);
			}
			else
			{
				bool isActiveAndEnabled = base.isActiveAndEnabled;
				if (isActiveAndEnabled)
				{
					base.StopAllCoroutines();
					base.StartCoroutine(this.WaitAnimatorAndSetup());
				}
			}
		}

		private IEnumerator WaitAnimatorAndSetup()
		{
			yield return new WaitUntil(() => this.animator.isInitialized && this.animator.isActiveAndEnabled);
			this.animator.SetBool(this.stateName, this.toggle.isOn);
			yield break;
		}

		[SerializeField]
		private Animator animator;

		[SerializeField]
		private string stateName;

		private Toggle toggle;
	}
}
