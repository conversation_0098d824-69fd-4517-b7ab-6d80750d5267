﻿using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace Nirvana
{
	internal sealed class AssetBundleCache
	{
		internal AssetBundleCache()
		{
			string text = StreamingAssets.ReadAllText("file_list.txt");
			string[] array = text.Split(new char[] { '\n' });
			foreach (string text2 in array)
			{
				this.streamingFiles.Add(text2);
			}
		}

		internal string CachePath
		{
			get
			{
				bool flag = string.IsNullOrEmpty(this.cachePath);
				if (flag)
				{
					this.cachePath = Path.Combine(Application.persistentDataPath, "AssetCache");
					bool flag2 = !Directory.Exists(this.cachePath);
					if (flag2)
					{
						Directory.CreateDirectory(this.cachePath);
					}
				}
				return this.cachePath;
			}
		}

		public string LoadVersion()
		{
			string text = this.GetCachePath("version.txt");
			bool flag = File.Exists(text);
			string text2;
			if (flag)
			{
				text2 = File.ReadAllText(text);
			}
			else
			{
				text2 = StreamingAssets.ReadAllText("AssetBundle/version.txt");
			}
			return text2;
		}

		public void SaveVersion(string version)
		{
			string text = Path.Combine(this.CachePath, "version.txt");
			File.WriteAllText(text, version);
		}

		internal bool IsVersionCached(string bundleName, Hash128 hash)
		{
			return !string.IsNullOrEmpty(this.GetLocalPath(bundleName, hash));
		}

		internal void ClearCache()
		{
			bool flag = Directory.Exists(this.CachePath);
			if (flag)
			{
				Directory.Delete(this.CachePath, true);
			}
		}

		internal void DeleteCache(string bundleName)
		{
			string text = this.GetCachePath(bundleName);
			bool flag = File.Exists(text);
			if (flag)
			{
				File.Delete(text);
			}
		}

		internal void DeleteCache(string bundleName, Hash128 hash)
		{
			string text = this.GetCachePath(bundleName, hash);
			bool flag = File.Exists(text);
			if (flag)
			{
				File.Delete(text);
			}
		}

		internal AssetBundle LoadFromCacheSync(string bundleName)
		{
			string localPath = this.GetLocalPath(bundleName);
			bool flag = string.IsNullOrEmpty(localPath);
			AssetBundle assetBundle;
			if (flag)
			{
				assetBundle = null;
			}
			else
			{
				assetBundle = AssetBundle.LoadFromFile(localPath);
			}
			return assetBundle;
		}

		internal AssetBundle LoadFromCacheSync(string bundleName, Hash128 hash, bool ignoreHashCheck)
		{
			string text;
			if (ignoreHashCheck)
			{
				text = this.GetLocalPathIgnoreHash(bundleName, hash);
			}
			else
			{
				text = this.GetLocalPath(bundleName, hash);
			}
			bool flag = string.IsNullOrEmpty(text);
			AssetBundle assetBundle;
			if (flag)
			{
				assetBundle = null;
			}
			else
			{
				assetBundle = AssetBundle.LoadFromFile(text);
			}
			return assetBundle;
		}

		internal AssetBundleCreateRequest LoadFromCache(string bundleName)
		{
			string localPath = this.GetLocalPath(bundleName);
			bool flag = string.IsNullOrEmpty(localPath);
			AssetBundleCreateRequest assetBundleCreateRequest;
			if (flag)
			{
				assetBundleCreateRequest = null;
			}
			else
			{
				assetBundleCreateRequest = AssetBundle.LoadFromFileAsync(localPath);
			}
			return assetBundleCreateRequest;
		}

		internal AssetBundleCreateRequest LoadFromCache(string bundleName, Hash128 hash, bool ignoreHashCheck)
		{
			string text;
			if (ignoreHashCheck)
			{
				text = this.GetLocalPathIgnoreHash(bundleName, hash);
			}
			else
			{
				text = this.GetLocalPath(bundleName, hash);
			}
			bool flag = string.IsNullOrEmpty(text);
			AssetBundleCreateRequest assetBundleCreateRequest;
			if (flag)
			{
				assetBundleCreateRequest = null;
			}
			else
			{
				assetBundleCreateRequest = AssetBundle.LoadFromFileAsync(text);
			}
			return assetBundleCreateRequest;
		}

		internal bool ExistedInStreaming(string filePath)
		{
			filePath = filePath.Replace("\\", "/");
			return this.streamingFiles.Contains(filePath);
		}

		public string GetCachePath(string bundleName)
		{
			return Path.Combine(this.CachePath, bundleName);
		}

		public string GetCachePath(string bundleName, Hash128 hash)
		{
			string text = Path.Combine(this.CachePath, bundleName);
			return text + "-" + hash.ToString();
		}

		private string GetLocalPath(string bundleName)
		{
			string text = this.GetCachePath(bundleName);
			bool flag = File.Exists(text);
			string text2;
			if (flag)
			{
				text2 = text;
			}
			else
			{
				string text3 = Path.Combine("AssetBundle", bundleName);
				bool flag2 = this.ExistedInStreaming(text3);
				if (flag2)
				{
					text2 = Path.Combine(Application.streamingAssetsPath, text3);
				}
				else
				{
					text2 = string.Empty;
				}
			}
			return text2;
		}

		private string GetLocalPath(string bundleName, Hash128 hash)
		{
			string text = this.GetCachePath(bundleName, hash);
			bool flag = File.Exists(text);
			string text2;
			if (flag)
			{
				text2 = text;
			}
			else
			{
				string text3 = bundleName + "-" + hash.ToString();
				string text4 = Path.Combine("AssetBundle", text3);
				bool flag2 = this.ExistedInStreaming(text4);
				if (flag2)
				{
					text2 = Path.Combine(Application.streamingAssetsPath, text4);
				}
				else
				{
					text2 = string.Empty;
				}
			}
			return text2;
		}

		private string GetLocalPathIgnoreHash(string bundleName, Hash128 hash)
		{
			string localPath = this.GetLocalPath(bundleName, hash);
			bool flag = !string.IsNullOrEmpty(localPath);
			string text;
			if (flag)
			{
				text = localPath;
			}
			else
			{
				string empty = string.Empty;
				string text2 = this.GetCachePath(bundleName);
				string fileName = Path.GetFileName(bundleName);
				string directoryName = Path.GetDirectoryName(text2);
				bool flag2 = Directory.Exists(directoryName);
				if (flag2)
				{
					string[] files = Directory.GetFiles(directoryName, string.Format("{0}-*", fileName), SearchOption.TopDirectoryOnly);
					bool flag3 = files.Length != 0;
					if (flag3)
					{
						return files[0];
					}
				}
				string text3 = Path.Combine("AssetBundle", bundleName + "-");
				string text4 = this.FindStreamingFileStartWith(text3);
				bool flag4 = string.IsNullOrEmpty(text4);
				if (flag4)
				{
					text = empty;
				}
				else
				{
					text = Path.Combine(Application.streamingAssetsPath, text4);
				}
			}
			return text;
		}

		private string FindStreamingFileStartWith(string filePrefix)
		{
			filePrefix = filePrefix.Replace("\\", "/");
			foreach (string text in this.streamingFiles)
			{
				bool flag = text.StartsWith(filePrefix);
				if (flag)
				{
					return text;
				}
			}
			return string.Empty;
		}

		private string cachePath;

		private HashSet<string> streamingFiles = new HashSet<string>(StringComparer.Ordinal);
	}
}
