﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class Vector2Extensions
	{
		public static Vector2 Parse(string text)
		{
			Vector2 vector;
			bool flag = !Vector2Extensions.TryParse(text, out vector);
			if (flag)
			{
				string text2 = string.Format("The string {0} can not convert to Rect.", text);
				throw new FormatException(text2);
			}
			return vector;
		}

		public static bool TryParse(string text, out Vector2 v)
		{
			bool flag = text.Length < 2 || text[0] != '(' || text[text.Length - 1] != ')';
			bool flag2;
			if (flag)
			{
				v = Vector2.zero;
				flag2 = false;
			}
			else
			{
				string[] array = text.Substring(1, text.Length - 2).Split(new char[] { ',' });
				bool flag3 = array.Length != 2;
				if (flag3)
				{
					v = Vector2.zero;
					flag2 = false;
				}
				else
				{
					float num = float.Parse(array[0]);
					float num2 = float.Parse(array[1]);
					v = new Vector2(num, num2);
					flag2 = true;
				}
			}
			return flag2;
		}
	}
}
