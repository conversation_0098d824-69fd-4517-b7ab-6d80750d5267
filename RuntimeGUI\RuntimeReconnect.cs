using LuaInterface;
using Nirvana;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

public class RuntimeReconnect : Nirvana.Singleton<RuntimeReconnect>
{
    private Dictionary<string, long> fileModifyTimeDic = new Dictionary<string, long>();
    private string luaDir;
    public void CacheLuaLastWriteTime(string luaFileName)
    {
        this.CalcLuaDir();
        string fileFullPath = this.GetLuaFilePath(luaFileName);
        if (string.IsNullOrEmpty(fileFullPath)) return;

        FileInfo fileInfo = new FileInfo(fileFullPath);
        if (!fileInfo.Exists) return;

        if (!fileModifyTimeDic.ContainsKey(fileInfo.FullName))
        {
            fileModifyTimeDic.Add(fileInfo.FullName, fileInfo.LastWriteTime.ToFileTime());
        }
        else
        {
            fileModifyTimeDic[fileInfo.FullName] = fileInfo.LastWriteTime.ToFileTime();
        }
    }

    public void Reconnect()
    {
        //this.CalcLuaDir();
        //if (string.IsNullOrEmpty(luaDir))
        //{
        //    Debug.LogErrorFormat("[RuntimeReconnect] Reconnect, not exist luadir");
        //    return;
        //}

        //Debug.LogFormat("[RuntimeReconnect] Reconnect, luaDir: {0} {1}", luaDir, fileModifyTimeDic.Count);
        LuaFunction quick_reconnect_fun = GameRoot.Instance.LuaState.GetFunction("ExecuteQuickReconnect");
        //string hotupdateFiles = this.GetHotupdateList();
        //Debug.LogFormat("Reconnect, hotupdate files: {0}", hotupdateFiles);
        quick_reconnect_fun.Call();
    }

    private string GetHotupdateList()
    {
        Dictionary<string, long> dirtyDic = new Dictionary<string, long>();

        foreach (var kv in fileModifyTimeDic)
        {
            string full_path = kv.Key;
            FileInfo fileInfo = new FileInfo(full_path);
            long newTime = fileInfo.LastWriteTime.ToFileTime();
            if (newTime != kv.Value)
            {
                dirtyDic.Add(full_path, newTime);
            }
        }

        string reloadFiles = string.Empty;
        string tempLuaDir = luaDir.Replace("/", "\\");
        foreach (var kv in dirtyDic)
        {
            string fullPath = kv.Key;
            fileModifyTimeDic[fullPath] = kv.Value;

            string path = fullPath.Replace(tempLuaDir + "\\", "");
            path = path.Replace("\\", "/");
            path = path.Replace(".lua", "");
            path = path.Replace("/", ".");
            reloadFiles += path + "|";
        }
  
        return reloadFiles;
    }

    private void GetNeedUpdateList(List<string> hotupdate_file_list)
    {
        List<string> all_lua_files = new List<string>();
        GetAllLuaFiles(new DirectoryInfo(luaDir), all_lua_files);

        string temp_lua_dir = luaDir.Replace("/", "\\");
        foreach (string full_path in all_lua_files)
        {
            string path = full_path.Replace(temp_lua_dir + "\\", "");
            path = path.Replace("\\", "/");
            path = path.Replace(".lua", "");

            hotupdate_file_list.Add(path);
        }
    }

    private void GetAllLuaFiles(FileSystemInfo info, List<string> all_lua_files)
    {
        if (!info.Exists) return;

        DirectoryInfo dir = info as DirectoryInfo;
        if (dir == null) return;

        FileSystemInfo[] files = dir.GetFileSystemInfos();
        for (int i = 0; i < files.Length; i++)
        {
            FileInfo fileInfo = files[i] as FileInfo;
            if (fileInfo != null)
            {
                if (fileInfo.Extension == ".lua")
                {
                    long old_modify_time = 0;
                    long new_modify_time = fileInfo.LastWriteTime.ToFileTime();

                    if (!fileModifyTimeDic.TryGetValue(fileInfo.FullName, out old_modify_time)
                        || old_modify_time != new_modify_time)
                    {
                        string fullPath = fileInfo.FullName.Replace("\\", "/");
                        all_lua_files.Add(fullPath);
                        fileModifyTimeDic[fileInfo.FullName] = new_modify_time;
                    }
                }
            }
            else
            {
                GetAllLuaFiles(files[i], all_lua_files);
            }
        }
    }

    private void CalcLuaDir()
    {
        if (RuntimeGUIMgr.Instance.IsGUIOpening())
        {
            if (RuntimeGUIMgr.Instance.IsUseLocalLuaFile())
            {
                luaDir = string.Format("{0}/Game/Lua", RuntimeGUIMgr.Instance.GetDataPath());
            }

            if (RuntimeGUIMgr.Instance.IsDebugLuaAB())
            {
                luaDir = string.Format("{0}/Lua", Application.persistentDataPath);
            }
        }
    }

    private string GetLuaFilePath(string luaFileName)
    {
        if (string.IsNullOrEmpty(luaDir)) return "";

        string fullPath = string.Format("{0}/{1}.lua", luaDir, luaFileName);
        return fullPath;
    }
}
