﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class ImagePicker
	{
		public static void PickFromCamera(int pickSize, int scaleSize, Action<string, string> complete)
		{
			ImagePicker.GetImagePicker().CallStatic("pickFromCamera", new object[]
			{
				pickSize,
				scaleSize,
				new ImagePicker.PickListener(complete)
			});
		}

		public static void PickFromPhoto(int pickSize, int scaleSize, Action<string, string> complete)
		{
			ImagePicker.GetImagePicker().CallStatic("pickFromPhoto", new object[]
			{
				pickSize,
				scaleSize,
				new ImagePicker.PickListener(complete)
			});
		}

		private static AndroidJavaClass GetImagePicker()
		{
			bool flag = ImagePicker.imagePickerClass == null;
			if (flag)
			{
				ImagePicker.imagePickerClass = new AndroidJavaClass("com.winunet.and.PicturePicker");
			}
			return ImagePicker.imagePickerClass;
		}

		private static AndroidJavaClass imagePickerClass;

		private class PickListener : AndroidJavaProxy
		{
			public PickListener(Action<string, string> callback)
				: base("com.winunet.and.PicturePickListener")
			{
				this.callback = callback;
			}

			private void onPick(AndroidJavaObject pickedPath, AndroidJavaObject scaledPath)
			{
				SdkScheduler.PostTask(delegate
				{
					this.callback((pickedPath != null) ? pickedPath.Call<string>("toString", new object[0]) : null, (scaledPath != null) ? scaledPath.Call<string>("toString", new object[0]) : null);
				});
			}

			private void onPick(string pickedPath, string scaledPath)
			{
				SdkScheduler.PostTask(delegate
				{
					this.callback(pickedPath, scaledPath);
				});
			}

			private Action<string, string> callback;
		}
	}
}
