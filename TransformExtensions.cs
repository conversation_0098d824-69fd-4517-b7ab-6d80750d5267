﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class TransformExtensions
	{
		public static void SetPosition(this Transform transform, float x, float y, float z)
		{
			transform.position = new Vector3(x, y, z);
		}

		public static void SetLocalPosition(this Transform transform, float x, float y, float z)
		{
			transform.localPosition = new Vector3(x, y, z);
		}

		public static void SetLocalScale(this Transform transform, float x, float y, float z)
		{
			transform.localScale = new Vector3(x, y, z);
		}

		public static Transform FindHard(this Transform trans, string path)
		{
			bool flag = path == string.Empty;
			Transform transform;
			if (flag)
			{
				transform = trans;
			}
			else
			{
				Transform transform2 = trans;
				string[] array = path.Split(new char[] { '/' });
				foreach (string text in array)
				{
					bool flag2 = false;
					foreach (object obj in transform2)
					{
						Transform transform3 = (Transform)obj;
						bool flag3 = transform3.name == text;
						if (flag3)
						{
							transform2 = transform3;
							flag2 = true;
							break;
						}
					}
					bool flag4 = !flag2;
					if (flag4)
					{
						transform2 = null;
						break;
					}
				}
				transform = transform2;
			}
			return transform;
		}

		public static Transform FindByName(this Transform trans, string name)
		{
			bool flag = trans.name == name;
			Transform transform;
			if (flag)
			{
				transform = trans;
			}
			else
			{
				for (int i = 0; i < trans.childCount; i++)
				{
					Transform child = trans.GetChild(i);
					Transform transform2 = child.FindByName(name);
					bool flag2 = transform2 != null;
					if (flag2)
					{
						return transform2;
					}
				}
				transform = null;
			}
			return transform;
		}

		public static Transform FindByName(this Transform trans, string name1, string name2)
		{
			bool flag = trans.name.Equals(name1) || trans.name.Equals(name2);
			Transform transform;
			if (flag)
			{
				transform = trans;
			}
			else
			{
				for (int i = 0; i < trans.childCount; i++)
				{
					Transform child = trans.GetChild(i);
					Transform transform2 = child.FindByName(name1, name2);
					bool flag2 = transform2 != null;
					if (flag2)
					{
						return transform2;
					}
				}
				transform = null;
			}
			return transform;
		}

		public static Transform FindByName(this Transform trans, string name1, string name2, string name3)
		{
			bool flag = trans.name.Equals(name1) || trans.name.Equals(name2) || trans.name.Equals(name3);
			Transform transform;
			if (flag)
			{
				transform = trans;
			}
			else
			{
				for (int i = 0; i < trans.childCount; i++)
				{
					Transform child = trans.GetChild(i);
					Transform transform2 = child.FindByName(name1, name2, name3);
					bool flag2 = transform2 != null;
					if (flag2)
					{
						return transform2;
					}
				}
				transform = null;
			}
			return transform;
		}
	}
}
