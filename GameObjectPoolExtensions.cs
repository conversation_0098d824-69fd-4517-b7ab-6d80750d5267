﻿using System;
using System.Collections;
using UnityEngine;

namespace Nirvana
{
	public static class GameObjectPoolExtensions
	{
		public static void SpawnAsset(this GameObjectPool pool, string assetBundle, string assetName, Action<GameObject> complete)
		{
			AssetID assetID = new AssetID(assetBundle, assetName);
			Scheduler.RunCoroutine(GameObjectPoolExtensions.SpawnAssetImpl(pool, assetID, null, 0, complete));
		}

		public static void SpawnAsset(this GameObjectPool pool, AssetID assetID, Action<GameObject> complete)
		{
			Scheduler.RunCoroutine(GameObjectPoolExtensions.SpawnAssetImpl(pool, assetID, null, 0, complete));
		}

		public static void SpawnAssetWithQueue(this GameObjectPool pool, string assetBundle, string assetName, InstantiateQueue instantiateQueue, int instantiatePriority, Action<GameObject> complete)
		{
			AssetID assetID = new AssetID(assetBundle, assetName);
			Scheduler.RunCoroutine(GameObjectPoolExtensions.SpawnAssetImpl(pool, assetID, instantiateQueue, instantiatePriority, complete));
		}

		public static void SpawnAssetWithQueue(this GameObjectPool pool, AssetID assetID, InstantiateQueue instantiateQueue, int instantiatePriority, Action<GameObject> complete)
		{
			Scheduler.RunCoroutine(GameObjectPoolExtensions.SpawnAssetImpl(pool, assetID, instantiateQueue, instantiatePriority, complete));
		}

		private static IEnumerator SpawnAssetImpl(GameObjectPool pool, AssetID assetID, InstantiateQueue instantiateQueue, int instantiatePriority, Action<GameObject> complete)
		{
			WaitSpawnGameObject wait = pool.SpawnAssetWithQueue(assetID, instantiateQueue, instantiatePriority);
			yield return wait;
			bool flag = wait.Error != null;
			if (flag)
			{
				complete(null);
				yield break;
			}
			complete(wait.Instance);
			yield break;
		}
	}
}
