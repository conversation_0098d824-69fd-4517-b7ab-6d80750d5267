﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Gray")]
	[RequireComponent(typeof(UIGrayscale))]
	public sealed class UIVariableBindGray : UIVariableBindBool
	{
		protected override void OnValueChanged()
		{
			bool flag = this.grayscale == null;
			if (flag)
			{
				this.grayscale = base.GetComponent<UIGrayscale>();
			}
			bool result = base.GetResult();
			bool flag2 = result;
			if (flag2)
			{
				this.grayscale.GrayScale = this.enabledGray;
			}
			else
			{
				this.grayscale.GrayScale = this.disabledGray;
			}
		}

		[SerializeField]
		[Range(0f, 255f)]
		private int enabledGray = 0;

		[SerializeField]
		[Range(0f, 255f)]
		private int disabledGray = 255;

		private UIGrayscale grayscale;
	}
}
