﻿using System;
using UnityEngine;

namespace Nirvana
{
	[ExecuteInEditMode]
	public sealed class FollowConstraint : MonoBehaviour
	{
		private void LateUpdate()
		{
			bool flag = this.target != null;
			if (flag)
			{
				Quaternion quaternion = Quaternion.Euler(this.rotation);
				base.transform.rotation = this.target.rotation * quaternion;
				base.transform.position = this.target.position + this.target.rotation * this.offset;
			}
		}

		[SerializeField]
		private Transform target;

		[SerializeField]
		private Vector3 offset;

		[SerializeField]
		private Vector3 rotation;
	}
}
