﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public abstract class AbstractSceneGrid : MonoBehaviour
	{
		public Vector2 CellSize
		{
			get
			{
				return this.cellSize;
			}
			set
			{
				this.cellSize = value;
			}
		}

		public int Row
		{
			get
			{
				return this.row;
			}
		}

		public int Column
		{
			get
			{
				return this.column;
			}
		}

		public virtual int MaxRow
		{
			get
			{
				return 1280;
			}
		}

		public virtual int MaxColumn
		{
			get
			{
				return 1280;
			}
		}

		public abstract AbstractSceneCell[] Cells { get; set; }

		public GameObject GetGridRoot()
		{
			return this.gridRoot ? this.gridRoot.gameObject : null;
		}

		public abstract AbstractSceneCell CreateCell();

		public void Initialize()
		{
			bool flag = this.Cells == null || this.Cells.Length != this.row * this.column;
			if (flag)
			{
				bool flag2 = this.row == 0;
				if (flag2)
				{
					this.row = 32;
				}
				bool flag3 = this.column == 0;
				if (flag3)
				{
					this.column = 32;
				}
				this.ResizeData(this.row, this.column);
			}
			bool flag4 = this.gridRoot == null;
			if (flag4)
			{
				this.gridRoot = base.transform.Find("SceneGridRoot");
				bool flag5 = this.gridRoot != null;
				if (flag5)
				{
					this.gridRoot.hideFlags = 61;
				}
				else
				{
					this.gridRoot = new GameObject("SceneGridRoot")
					{
						hideFlags = 61
					}.transform;
					this.gridRoot.SetParent(base.transform, false);
				}
			}
			this.gridRoot.localPosition = Vector3.zero;
			this.gridRoot.localRotation = Quaternion.identity;
			this.gridRoot.localScale = Vector3.one;
			bool flag6 = this.cellMeshes == null || this.cellVertices == null || this.cellColors == null || this.outlineMeshes == null || this.outlineVertices == null;
			if (flag6)
			{
				this.UpdateMesh();
			}
			this.SetGridVisible(this.editing);
		}

		public void SetGridVisible(bool visible)
		{
			bool flag = this.gridRoot != null;
			if (flag)
			{
				this.gridRoot.gameObject.SetActive(visible);
			}
		}

		public void Resize(int row, int column)
		{
			bool flag = this.Cells == null;
			if (flag)
			{
				this.BuildData(row, column);
			}
			else
			{
				this.ResizeData(row, column);
			}
			this.UpdateMesh();
		}

		public void Resize(int offsetX, int offsetY, int offsetR, int offsetC)
		{
			this.ResizeData(offsetX, offsetY, offsetR, offsetC);
			this.UpdateMesh();
		}

		public void FitToGround()
		{
			bool flag = this.Cells == null;
			if (!flag)
			{
				for (int i = 0; i < this.row; i++)
				{
					for (int j = 0; j < this.column; j++)
					{
						AbstractSceneCell cell = this.GetCell(i, j);
						Vector3 position = base.transform.position;
						cell.HeightLT = this.PickHeight((float)i * this.cellSize.x + position.x, (float)j * this.cellSize.y + position.z) - position.y;
						cell.HeightRT = this.PickHeight((float)(i + 1) * this.cellSize.x + position.x, (float)j * this.cellSize.y + position.z) - position.y;
						cell.HeightLB = this.PickHeight((float)i * this.cellSize.x + position.x, (float)(j + 1) * this.cellSize.y + position.z) - position.y;
						cell.HeightRB = this.PickHeight((float)(i + 1) * this.cellSize.x + position.x, (float)(j + 1) * this.cellSize.y + position.z) - position.y;
					}
				}
				this.UpdateMesh();
			}
		}

		public Vector3 GetCellPositionLT(int row, int column)
		{
			bool flag = row >= 0 && row < this.row && column >= 0 && column < this.column;
			float num;
			if (flag)
			{
				num = this.GetCell(row, column).HeightLT;
			}
			else
			{
				num = this.GetCell(0, 0).HeightLT;
			}
			return new Vector3((float)row * this.cellSize.x, num, (float)column * this.cellSize.y);
		}

		public Vector3 GetCellPositionRT(int row, int column)
		{
			bool flag = row >= 0 && row < this.row && column >= 0 && column < this.column;
			float num;
			if (flag)
			{
				num = this.GetCell(row, column).HeightRT;
			}
			else
			{
				num = this.GetCell(0, 0).HeightRT;
			}
			return new Vector3((float)(row + 1) * this.cellSize.x, num, (float)column * this.cellSize.y);
		}

		public Vector3 GetCellPositionLB(int row, int column)
		{
			bool flag = row >= 0 && row < this.row && column >= 0 && column < this.column;
			float num;
			if (flag)
			{
				num = this.GetCell(row, column).HeightLB;
			}
			else
			{
				num = this.GetCell(0, 0).HeightLB;
			}
			return new Vector3((float)row * this.cellSize.x, num, (float)(column + 1) * this.cellSize.y);
		}

		public Vector3 GetCellPositionRB(int row, int column)
		{
			bool flag = row >= 0 && row < this.row && column >= 0 && column < this.column;
			float num;
			if (flag)
			{
				num = this.GetCell(row, column).HeightRB;
			}
			else
			{
				num = this.GetCell(0, 0).HeightRB;
			}
			return new Vector3((float)(row + 1) * this.cellSize.x, num, (float)(column + 1) * this.cellSize.y);
		}

		public AbstractSceneCell GetCell(int row, int column)
		{
			bool flag = row >= 0 && row < this.row && column >= 0 && column < this.column;
			AbstractSceneCell abstractSceneCell;
			if (flag)
			{
				abstractSceneCell = this.Cells[row * this.column + column];
			}
			else
			{
				abstractSceneCell = null;
			}
			return abstractSceneCell;
		}

		public void FillGridColor(Func<AbstractSceneCell, Color> fill)
		{
			bool flag = this.cellMeshes == null || this.cellColors == null;
			if (flag)
			{
				this.UpdateMesh();
			}
			int num = Mathf.CeilToInt((float)this.row / (float)AbstractSceneGrid.SegmentX);
			int num2 = Mathf.CeilToInt((float)this.column / (float)AbstractSceneGrid.SegmentY);
			for (int i = 0; i < num; i++)
			{
				for (int j = 0; j < num2; j++)
				{
					int num3 = i * num2 + j;
					Color32[] array = this.cellColors[num3];
					Mesh mesh = this.cellMeshes[num3];
					this.FillSegmentColor(i, j, array, mesh, fill);
				}
			}
		}

		public void SetCellColor(int row, int column, Color32 color)
		{
			bool flag = this.cellMeshes == null || this.cellVertices == null || this.cellColors == null || this.outlineMeshes == null || this.outlineVertices == null;
			if (flag)
			{
				this.UpdateMesh();
			}
			int num = Mathf.CeilToInt((float)this.column / (float)AbstractSceneGrid.SegmentY);
			int num2 = Mathf.FloorToInt((float)row / (float)AbstractSceneGrid.SegmentX);
			int num3 = Mathf.FloorToInt((float)column / (float)AbstractSceneGrid.SegmentY);
			int num4 = AbstractSceneGrid.SegmentY * num3;
			int num5 = Mathf.Min(AbstractSceneGrid.SegmentY * num3 + AbstractSceneGrid.SegmentX, this.column);
			int num6 = num5 - num4;
			Color32[] array = this.cellColors[num2 * num + num3];
			Mesh mesh = this.cellMeshes[num2 * num + num3];
			int num7 = row - AbstractSceneGrid.SegmentX * num2;
			int num8 = column - AbstractSceneGrid.SegmentY * num3;
			array[4 * (num7 * num6 + num8)] = color;
			array[4 * (num7 * num6 + num8) + 1] = color;
			array[4 * (num7 * num6 + num8) + 2] = color;
			array[4 * (num7 * num6 + num8) + 3] = color;
			mesh.colors32 = array;
		}

		public void UpdateCellHeight(int row, int column)
		{
			bool flag = this.cellMeshes == null || this.cellVertices == null || this.cellColors == null || this.outlineMeshes == null || this.outlineVertices == null;
			if (flag)
			{
				this.UpdateMesh();
			}
			int num = Mathf.CeilToInt((float)this.column / (float)AbstractSceneGrid.SegmentY);
			int num2 = Mathf.FloorToInt((float)row / (float)AbstractSceneGrid.SegmentX);
			int num3 = Mathf.FloorToInt((float)column / (float)AbstractSceneGrid.SegmentY);
			int num4 = AbstractSceneGrid.SegmentY * num3;
			int num5 = Mathf.Min(AbstractSceneGrid.SegmentY * num3 + AbstractSceneGrid.SegmentX, this.column);
			int num6 = num5 - num4;
			Mesh mesh = this.cellMeshes[num2 * num + num3];
			Vector3[] array = this.cellVertices[num2 * num + num3];
			Mesh mesh2 = this.outlineMeshes[num2 * num + num3];
			Vector3[] array2 = this.outlineVertices[num2 * num + num3];
			int num7 = row - AbstractSceneGrid.SegmentX * num2;
			int num8 = column - AbstractSceneGrid.SegmentY * num3;
			AbstractSceneCell cell = this.GetCell(row, column);
			array[4 * (num7 * num6 + num8)].y = cell.HeightLT;
			array[4 * (num7 * num6 + num8) + 1].y = cell.HeightRT;
			array[4 * (num7 * num6 + num8) + 2].y = cell.HeightRB;
			array[4 * (num7 * num6 + num8) + 3].y = cell.HeightLB;
			mesh.vertices = array;
			array2[4 * (num7 * num6 + num8)].y = cell.HeightLT;
			array2[4 * (num7 * num6 + num8) + 1].y = cell.HeightRT;
			array2[4 * (num7 * num6 + num8) + 2].y = cell.HeightRB;
			array2[4 * (num7 * num6 + num8) + 3].y = cell.HeightLB;
			mesh2.vertices = array2;
		}

		public void UpdateMesh()
		{
			Transform[] array = new Transform[this.gridRoot.childCount];
			for (int i = 0; i < this.gridRoot.childCount; i++)
			{
				array[i] = this.gridRoot.GetChild(i);
			}
			foreach (Transform transform in array)
			{
				Object.DestroyImmediate(transform.gameObject);
			}
			int num = Mathf.CeilToInt((float)this.row / (float)AbstractSceneGrid.SegmentX);
			int num2 = Mathf.CeilToInt((float)this.column / (float)AbstractSceneGrid.SegmentY);
			this.cellMeshes = new Mesh[num * num2];
			this.cellVertices = new Vector3[num * num2][];
			this.cellColors = new Color32[num * num2][];
			this.outlineMeshes = new Mesh[num * num2];
			this.outlineVertices = new Vector3[num * num2][];
			for (int k = 0; k < num; k++)
			{
				for (int l = 0; l < num2; l++)
				{
					int num3 = k * num2 + l;
					this.UpdateSubmeshCell(k, l, out this.cellVertices[num3], out this.cellColors[num3], out this.cellMeshes[num3]);
					this.UpdateSubmeshOutline(k, l, out this.outlineVertices[num3], out this.outlineMeshes[num3]);
				}
			}
		}

		private void BuildData(int row, int column)
		{
			this.row = row;
			this.column = column;
			bool flag = (this.Cells == null || this.Cells.Length == 0) && this.row > 0 && this.column > 0;
			if (flag)
			{
				AbstractSceneCell[] array = new AbstractSceneCell[row * column];
				for (int i = 0; i < row; i++)
				{
					for (int j = 0; j < column; j++)
					{
						array[i * column + j] = this.CreateCell();
					}
				}
				this.Cells = array;
			}
		}

		private void ResizeData(int row, int column)
		{
			AbstractSceneCell[] array = new AbstractSceneCell[row * column];
			bool flag = this.Cells != null;
			if (flag)
			{
				for (int i = 0; i < row; i++)
				{
					for (int j = 0; j < column; j++)
					{
						bool flag2 = i < this.row && j < this.column;
						if (flag2)
						{
							int num = i * this.column + j;
							bool flag3 = num < this.Cells.Length;
							if (flag3)
							{
								array[i * column + j] = this.Cells[num];
							}
						}
						else
						{
							array[i * column + j] = this.CreateCell();
						}
					}
				}
			}
			else
			{
				for (int k = 0; k < row; k++)
				{
					for (int l = 0; l < column; l++)
					{
						array[k * column + l] = this.CreateCell();
					}
				}
			}
			this.Cells = array;
			this.row = row;
			this.column = column;
		}

		private void ResizeData(int offsetX, int offsetY, int offsetR, int offsetC)
		{
			bool flag = offsetX != 0 || offsetY != 0;
			if (flag)
			{
				List<Transform> list = new List<Transform>();
				foreach (object obj in base.transform)
				{
					Transform transform = (Transform)obj;
					bool flag2 = transform != this.gridRoot;
					if (flag2)
					{
						list.Add(transform);
					}
				}
				foreach (Transform transform2 in list)
				{
					transform2.SetParent(null, true);
				}
				Vector3 position = base.transform.position;
				position.x -= (float)offsetX * this.cellSize.x;
				position.z -= (float)offsetY * this.cellSize.y;
				base.transform.position = position;
				foreach (Transform transform3 in list)
				{
					transform3.SetParent(base.transform, true);
				}
			}
			int num = this.row + offsetX + offsetR;
			int num2 = this.column + offsetY + offsetC;
			AbstractSceneCell[] array = new AbstractSceneCell[num * num2];
			bool flag3 = this.Cells != null;
			if (flag3)
			{
				for (int i = 0; i < num; i++)
				{
					for (int j = 0; j < num2; j++)
					{
						bool flag4 = i >= offsetX && i < offsetX + this.row && j >= offsetY && j < offsetY + this.column;
						if (flag4)
						{
							int num3 = (i - offsetX) * this.column + j - offsetY;
							bool flag5 = num3 < this.Cells.Length;
							if (flag5)
							{
								array[i * num2 + j] = this.Cells[num3];
							}
						}
						else
						{
							array[i * num2 + j] = this.CreateCell();
						}
					}
				}
			}
			else
			{
				for (int k = 0; k < num; k++)
				{
					for (int l = 0; l < num2; l++)
					{
						array[k * num2 + l] = this.CreateCell();
					}
				}
			}
			this.Cells = array;
			this.row = num;
			this.column = num2;
		}

		private float PickHeight(float x, float y)
		{
			Vector3 vector;
			vector..ctor(x, 10000f, y);
			RaycastHit[] array = Physics.RaycastAll(vector, Vector3.down, 100000f, this.groundMask);
			bool flag = array.Length == 0;
			float num;
			if (flag)
			{
				num = this.baseHeight;
			}
			else
			{
				bool flag2 = false;
				float num2 = float.NegativeInfinity;
				foreach (RaycastHit raycastHit in array)
				{
					bool flag3 = base.transform == raycastHit.transform.parent;
					if (!flag3)
					{
						bool flag4 = num2 < raycastHit.point.y;
						if (flag4)
						{
							num2 = raycastHit.point.y;
							flag2 = true;
						}
					}
				}
				bool flag5 = flag2;
				if (flag5)
				{
					num = num2;
				}
				else
				{
					num = this.baseHeight;
				}
			}
			return num;
		}

		private void UpdateSubmeshCell(int segX, int segY, out Vector3[] verts, out Color32[] colors, out Mesh mesh)
		{
			GameObject gameObject = new GameObject();
			gameObject.name = "GridCell: " + segX.ToString() + "-" + segY.ToString();
			gameObject.tag = "EditorOnly";
			gameObject.transform.parent = this.gridRoot;
			gameObject.transform.localPosition = new Vector3(0f, 0f, 0f);
			MeshRenderer meshRenderer = gameObject.AddComponent<MeshRenderer>();
			MeshCollider meshCollider = gameObject.AddComponent<MeshCollider>();
			MeshFilter meshFilter = gameObject.AddComponent<MeshFilter>();
			mesh = new Mesh();
			mesh.name = "GridCell: " + segX.ToString() + "-" + segY.ToString();
			int num = AbstractSceneGrid.SegmentX * segX;
			int num2 = AbstractSceneGrid.SegmentY * segY;
			int num3 = Mathf.Min(AbstractSceneGrid.SegmentX * segX + AbstractSceneGrid.SegmentX, this.row);
			int num4 = Mathf.Min(AbstractSceneGrid.SegmentY * segY + AbstractSceneGrid.SegmentY, this.column);
			int num5 = num3 - num;
			int num6 = num4 - num2;
			mesh.Clear();
			verts = new Vector3[num5 * num6 * 4];
			int num7 = 0;
			for (int i = num; i < num3; i++)
			{
				for (int j = num2; j < num4; j++)
				{
					verts[num7] = this.GetCellPositionLT(i, j);
					verts[num7 + 1] = this.GetCellPositionRT(i, j);
					verts[num7 + 2] = this.GetCellPositionRB(i, j);
					verts[num7 + 3] = this.GetCellPositionLB(i, j);
					num7 += 4;
				}
			}
			mesh.vertices = verts;
			int[] array = new int[6 * num5 * num6];
			for (int k = 0; k < num5 * num6; k++)
			{
				array[6 * k] = 4 * k + 2;
				array[6 * k + 1] = 4 * k + 1;
				array[6 * k + 2] = 4 * k;
				array[6 * k + 3] = 4 * k;
				array[6 * k + 4] = 4 * k + 3;
				array[6 * k + 5] = 4 * k + 2;
			}
			mesh.triangles = array;
			colors = new Color32[num5 * num6 * 4];
			int num8 = 0;
			for (int l = num; l < num3; l++)
			{
				for (int m = num2; m < num4; m++)
				{
					Color grey = Color.grey;
					colors[num8] = grey;
					colors[num8 + 1] = grey;
					colors[num8 + 2] = grey;
					colors[num8 + 3] = grey;
					num8 += 4;
				}
			}
			mesh.colors32 = colors;
			mesh.RecalculateBounds();
			meshCollider.sharedMesh = mesh;
			meshFilter.sharedMesh = mesh;
			meshRenderer.material = new Material(Shader.Find("Nirvana/Editor/GridCell"));
		}

		private void UpdateSubmeshOutline(int segX, int segY, out Vector3[] verts, out Mesh mesh)
		{
			GameObject gameObject = new GameObject();
			gameObject.name = "GridOutline: " + segX.ToString() + "-" + segY.ToString();
			gameObject.tag = "EditorOnly";
			gameObject.transform.parent = this.gridRoot;
			gameObject.transform.localPosition = new Vector3(0f, 0f, 0f);
			MeshRenderer meshRenderer = gameObject.AddComponent<MeshRenderer>();
			MeshFilter meshFilter = gameObject.AddComponent<MeshFilter>();
			mesh = new Mesh();
			mesh.name = "GridOutline: " + segX.ToString() + "-" + segY.ToString();
			int num = AbstractSceneGrid.SegmentX * segX;
			int num2 = AbstractSceneGrid.SegmentY * segY;
			int num3 = Mathf.Min(AbstractSceneGrid.SegmentX * segX + AbstractSceneGrid.SegmentX, this.row);
			int num4 = Mathf.Min(AbstractSceneGrid.SegmentY * segY + AbstractSceneGrid.SegmentY, this.column);
			int num5 = num3 - num;
			int num6 = num4 - num2;
			mesh.Clear();
			verts = new Vector3[num5 * num6 * 4];
			int num7 = 0;
			for (int i = num; i < num3; i++)
			{
				for (int j = num2; j < num4; j++)
				{
					verts[num7] = this.GetCellPositionLT(i, j);
					verts[num7 + 1] = this.GetCellPositionRT(i, j);
					verts[num7 + 2] = this.GetCellPositionRB(i, j);
					verts[num7 + 3] = this.GetCellPositionLB(i, j);
					num7 += 4;
				}
			}
			mesh.vertices = verts;
			int[] array = new int[8 * num5 * num6];
			for (int k = 0; k < num5 * num6; k++)
			{
				array[8 * k] = 4 * k;
				array[8 * k + 1] = 4 * k + 1;
				array[8 * k + 2] = 4 * k + 1;
				array[8 * k + 3] = 4 * k + 2;
				array[8 * k + 4] = 4 * k + 2;
				array[8 * k + 5] = 4 * k + 3;
				array[8 * k + 6] = 4 * k + 3;
				array[8 * k + 7] = 4 * k;
			}
			mesh.SetIndices(array, 3, 0);
			mesh.RecalculateBounds();
			meshFilter.sharedMesh = mesh;
			meshRenderer.material = new Material(Shader.Find("Nirvana/Editor/GridOutline"));
		}

		private void FillSegmentColor(int segX, int segY, Color32[] colors, Mesh mesh, Func<AbstractSceneCell, Color> fill)
		{
			int num = AbstractSceneGrid.SegmentX * segX;
			int num2 = AbstractSceneGrid.SegmentY * segY;
			int num3 = Mathf.Min(AbstractSceneGrid.SegmentX * segX + AbstractSceneGrid.SegmentX, this.row);
			int num4 = Mathf.Min(AbstractSceneGrid.SegmentY * segY + AbstractSceneGrid.SegmentY, this.column);
			int num5 = 0;
			for (int i = num; i < num3; i++)
			{
				for (int j = num2; j < num4; j++)
				{
					AbstractSceneCell cell = this.GetCell(i, j);
					Color color = fill(cell);
					colors[num5] = color;
					colors[num5 + 1] = color;
					colors[num5 + 2] = color;
					colors[num5 + 3] = color;
					num5 += 4;
				}
			}
			mesh.colors32 = colors;
		}

		private static readonly int SegmentX = 64;

		private static readonly int SegmentY = 64;

		[SerializeField]
		[Tooltip("The row count in this grid.")]
		private int row;

		[SerializeField]
		[Tooltip("The column count in this grid.")]
		private int column;

		[SerializeField]
		[Tooltip("The size of each cell.")]
		private Vector2 cellSize = new Vector2(0.5f, 0.5f);

		[SerializeField]
		[Tooltip("The row count in this grid.")]
		private float baseHeight;

		[SerializeField]
		[Tooltip("The ground physics layer mask.")]
		[LayerMask]
		private int groundMask = 256;

		[SerializeField]
		[Tooltip("Whether this scene grid is for editing.")]
		private bool editing = true;

		private Transform gridRoot;

		private Mesh[] cellMeshes;

		private Vector3[][] cellVertices;

		private Color32[][] cellColors;

		private Mesh[] outlineMeshes;

		private Vector3[][] outlineVertices;
	}
}
