﻿using System;
using System.Diagnostics;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Effect/Effect Control")]
	public sealed class EffectControl : MonoBehaviour
	{
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Action FadeoutEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Action FinishEvent;

		public void WaitFinsh(Action callback)
		{
			this.FinishEvent = callback;
			bool isLooping = this.IsLooping;
			if (isLooping)
			{
				Debug.LogErrorFormat("该特效已设置循环，Finsh不会回调。可能造成内存泄露 {0}", new object[] { base.gameObject.name });
			}
		}

		public void WaitFadout(Action callback)
		{
			this.FadeoutEvent = callback;
		}

		public bool IsNoScalable
		{
			get
			{
				return this.noScalable;
			}
		}

		public bool IsLooping
		{
			get
			{
				return this.looping;
			}
			set
			{
				this.looping = value;
			}
		}

		public bool IsPaused
		{
			get
			{
				return EffectControl.PlayState.Pausing == this.state;
			}
		}

		public bool IsStopped
		{
			get
			{
				return this.state == EffectControl.PlayState.Stopping;
			}
		}

		public float Duration
		{
			get
			{
				return this.duration;
			}
			set
			{
				this.duration = value;
			}
		}

		public float Fadeout
		{
			get
			{
				return this.fadeout;
			}
			set
			{
				this.fadeout = value;
			}
		}

		public float PlaybackSpeed
		{
			get
			{
				return this.playbackSpeed;
			}
			set
			{
				this.playbackSpeed = value;
				foreach (ParticleSystem particleSystem in this.ParticleSystems)
				{
					particleSystem.main.simulationSpeed = this.playbackSpeed;
				}
				foreach (Animator animator in this.Animators)
				{
					animator.speed = this.playbackSpeed;
				}
				foreach (Animation animation in this.Animations)
				{
					AnimationClip clip = animation.clip;
					bool flag = clip != null;
					if (flag)
					{
						animation[clip.name].speed = this.playbackSpeed;
					}
				}
			}
		}

		private ParticleSystem[] ParticleSystems
		{
			get
			{
				bool flag = this.particleSystems == null;
				if (flag)
				{
					this.particleSystems = base.GetComponentsInChildren<ParticleSystem>(true);
					foreach (ParticleSystem particleSystem in this.ParticleSystems)
					{
						particleSystem.main.simulationSpeed = this.playbackSpeed;
					}
				}
				return this.particleSystems;
			}
		}

		private Animator[] Animators
		{
			get
			{
				bool flag = this.animators == null;
				if (flag)
				{
					this.animators = base.GetComponentsInChildren<Animator>(true);
					foreach (Animator animator in this.animators)
					{
						animator.speed = this.playbackSpeed;
					}
				}
				return this.animators;
			}
		}

		private Animation[] Animations
		{
			get
			{
				bool flag = this.animations == null;
				if (flag)
				{
					this.animations = base.GetComponentsInChildren<Animation>(true);
					foreach (Animation animation in this.animations)
					{
						AnimationClip clip = animation.clip;
						bool flag2 = clip != null;
						if (flag2)
						{
							animation[clip.name].speed = this.playbackSpeed;
						}
					}
				}
				return this.animations;
			}
		}

		public void Play()
		{
			bool flag = EffectControl.PlayState.Playing == this.state;
			if (flag)
			{
				this.Stop();
			}
			this.state = EffectControl.PlayState.Pending;
		}

		public void Pause()
		{
			bool flag = EffectControl.PlayState.Playing == this.state;
			if (flag)
			{
				foreach (ParticleSystem particleSystem in this.ParticleSystems)
				{
					particleSystem.Pause(false);
				}
				foreach (Animator animator in this.Animators)
				{
					animator.speed = 0f;
				}
				foreach (Animation animation in this.Animations)
				{
					AnimationClip clip = animation.clip;
					bool flag2 = clip != null;
					if (flag2)
					{
						animation[clip.name].speed = 0f;
					}
				}
				this.state = EffectControl.PlayState.Pausing;
			}
		}

		public void Resume()
		{
			bool flag = EffectControl.PlayState.Pausing == this.state;
			if (flag)
			{
				foreach (ParticleSystem particleSystem in this.ParticleSystems)
				{
					particleSystem.Play(false);
				}
				foreach (Animator animator in this.Animators)
				{
					animator.speed = this.playbackSpeed;
				}
				foreach (Animation animation in this.Animations)
				{
					AnimationClip clip = animation.clip;
					bool flag2 = clip != null;
					if (flag2)
					{
						animation[clip.name].speed = this.playbackSpeed;
					}
				}
				this.state = EffectControl.PlayState.Playing;
			}
		}

		public void Stop()
		{
			bool flag = this.state > EffectControl.PlayState.Stopping;
			if (flag)
			{
				this.state = EffectControl.PlayState.Fadeouting;
				for (int i = 0; i < this.ParticleSystems.Length; i++)
				{
					this.ParticleSystems[i].Stop(false);
				}
				for (int j = 0; j < this.Animators.Length; j++)
				{
					this.Animators[j].enabled = false;
				}
				for (int k = 0; k < this.Animations.Length; k++)
				{
					Animation animation = this.Animations[k];
					bool playAutomatically = animation.playAutomatically;
					if (playAutomatically)
					{
						animation.enabled = false;
					}
					else
					{
						animation.Stop();
					}
				}
				bool flag2 = this.FadeoutEvent != null;
				if (flag2)
				{
					this.FadeoutEvent();
					this.FadeoutEvent = null;
				}
			}
		}

		public void Reset()
		{
			this.timer = 0f;
			this.state = EffectControl.PlayState.Stopping;
			this.FinishEvent = null;
			this.FadeoutEvent = null;
			this.existedTime = 0f;
		}

		private void Awake()
		{
			this.Reset();
		}

		private void OnDestroy()
		{
			this.FinishEvent = null;
			this.FadeoutEvent = null;
		}

		private void LateUpdate()
		{
			bool flag = EffectControl.PlayState.Pausing != this.state;
			if (flag)
			{
				this.existedTime += Time.deltaTime;
				bool flag2 = this.existedTime >= this.duration + this.fadeout + 5f;
				if (flag2)
				{
					this.existedTime = 0f;
				}
			}
			bool flag3 = this.state == EffectControl.PlayState.Stopping || EffectControl.PlayState.Pausing == this.state;
			if (!flag3)
			{
				this.timer += Time.deltaTime * this.playbackSpeed;
				bool flag4 = EffectControl.PlayState.Pending == this.state && this.timer >= this.delay;
				if (flag4)
				{
					for (int i = 0; i < this.ParticleSystems.Length; i++)
					{
						this.ParticleSystems[i].Play(false);
					}
					for (int j = 0; j < this.Animators.Length; j++)
					{
						this.Animators[j].enabled = false;
						this.Animators[j].enabled = true;
					}
					for (int k = 0; k < this.Animations.Length; k++)
					{
						Animation animation = this.Animations[k];
						bool playAutomatically = animation.playAutomatically;
						if (playAutomatically)
						{
							animation.enabled = false;
							animation.enabled = true;
						}
						else
						{
							animation.Stop();
							animation.Play();
						}
					}
					this.state = EffectControl.PlayState.Playing;
				}
				bool flag5 = !this.looping;
				if (flag5)
				{
					bool flag6 = EffectControl.PlayState.Playing == this.state && this.timer >= this.duration;
					if (flag6)
					{
						this.Stop();
					}
				}
				bool flag7 = EffectControl.PlayState.Fadeouting == this.state && this.timer >= this.duration + this.fadeout;
				if (flag7)
				{
					this.state = EffectControl.PlayState.Stopping;
					bool flag8 = this.FinishEvent != null;
					if (flag8)
					{
						this.FinishEvent();
						this.FinishEvent = null;
					}
					else
					{
						Debug.LogErrorFormat("没有设置WaitFinsh回调将会很容易导致内存泄露，请添加回调并主动删除。不然就不要添加EffecControl, {0}", new object[] { base.gameObject.name });
					}
				}
			}
		}

		[SerializeField]
		[Tooltip("Whether this effect is looping.")]
		private bool looping = false;

		[SerializeField]
		[Tooltip("Whether this effect can be scaled.")]
		private bool noScalable = false;

		[SerializeField]
		[Tooltip("The delay time when play this effect.")]
		private float delay = 0f;

		[SerializeField]
		[Tooltip("The effect duration time.")]
		private float duration = 5f;

		[SerializeField]
		[Tooltip("The effect fade out time.")]
		private float fadeout = 1f;

		private EffectControl.PlayState state = EffectControl.PlayState.Stopping;

		private float timer;

		private ParticleSystem[] particleSystems;

		private Animator[] animators;

		private Animation[] animations;

		private float playbackSpeed = 1f;

		private float existedTime = 0f;

		private enum PlayState
		{
			Stopping,
			Pending,
			Playing,
			Pausing,
			Fadeouting
		}
	}
}
