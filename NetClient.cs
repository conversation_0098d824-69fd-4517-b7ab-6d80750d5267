﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.Sockets;
using UnityEngine;

namespace Nirvana
{
	public sealed class NetClient
	{
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event NetClient.DisconnectDelegate DisconnectEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event NetClient.ReceiveDelegate ReceiveEvent;

		public int ReceiveTimeout
		{
			get
			{
				return this.socket.ReceiveTimeout;
			}
			set
			{
				this.socket.ReceiveTimeout = value;
			}
		}

		public int SendTimeout
		{
			get
			{
				return this.socket.SendTimeout;
			}
			set
			{
				this.socket.SendTimeout = value;
			}
		}

		public NetClient()
		{
			this.readBuffer = new NetBuffer(2 * this.ReadLength);
		}

		public void SetReadLength(int length)
		{
			this.ReadLength = length;
			this.readBuffer = new NetBuffer(2 * this.ReadLength);
		}

		public void Clear()
		{
			Delegate[] invocationList = this.DisconnectEvent.GetInvocationList();
			for (int i = 0; i < invocationList.Length; i++)
			{
				this.DisconnectEvent -= invocationList[i] as NetClient.DisconnectDelegate;
			}
			Delegate[] invocationList2 = this.ReceiveEvent.GetInvocationList();
			for (int j = 0; j < invocationList2.Length; j++)
			{
				this.ReceiveEvent -= invocationList2[j] as NetClient.ReceiveDelegate;
			}
		}

		public void Connect(string host, int port, NetClient.ConnectDelegate completeDelegate = null)
		{
			bool flag = this.socket != null;
			if (flag)
			{
				completeDelegate(false);
			}
			else
			{
				Dns.BeginGetHostAddresses(host, delegate(IAsyncResult result)
				{
					IPAddress[] array = Dns.EndGetHostAddresses(result);
					bool flag2 = array.Length == 0;
					if (flag2)
					{
						Debug.LogError("[NetClient]Connect failed: can not resolve DNS.");
						completeDelegate(false);
					}
					else
					{
						try
						{
							IPAddress ipaddress = array[0];
							this.socket = new Socket(ipaddress.AddressFamily, SocketType.Stream, ProtocolType.Tcp);
							this.socket.BeginConnect(ipaddress, port, new AsyncCallback(this.ConnectCallback), completeDelegate);
						}
						catch (Exception ex)
						{
							Debug.LogErrorFormat("[NetClient]Connect failed: {0}", new object[] { ex.Message });
							completeDelegate(false);
						}
					}
				}, null);
			}
		}

		public void Disconnect()
		{
			try
			{
				bool connected = this.socket.Connected;
				if (connected)
				{
					bool flag = this.writeQueue.Count > 0;
					if (flag)
					{
						List<ArraySegment<byte>> list = new List<ArraySegment<byte>>();
						foreach (WriteMessage writeMessage in this.writeQueue)
						{
							ArraySegment<byte> arraySegment = new ArraySegment<byte>(writeMessage.Buffer, 0, writeMessage.BufferLength);
							list.Add(arraySegment);
						}
						this.writeQueue.Clear();
						this.socket.Send(list, SocketFlags.None);
					}
					this.socket.Shutdown(SocketShutdown.Both);
					this.socket.Close();
					bool flag2 = this.DisconnectEvent != null;
					if (flag2)
					{
						this.DisconnectEvent();
					}
				}
			}
			catch (Exception ex)
			{
				Debug.LogErrorFormat("[NetClient]Socket disconnect: {0}", new object[] { ex.Message });
			}
		}

		public void SetKeepAlive(uint onOff, uint keepAliveTime, uint keepAliveInterval)
		{
			byte[] array = new byte[12];
			BitConverter.GetBytes(onOff).CopyTo(array, 0);
			BitConverter.GetBytes(keepAliveTime).CopyTo(array, 4);
			BitConverter.GetBytes(keepAliveInterval).CopyTo(array, 8);
			this.socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.KeepAlive, array);
		}

		public void SendMsg(byte[] data, NetClient.SendDelegate sendDelegate = null)
		{
			bool flag = !this.socket.Connected;
			if (flag)
			{
				this.socket.Close();
				bool flag2 = this.DisconnectEvent != null;
				if (flag2)
				{
					this.DisconnectEvent();
				}
			}
			else
			{
				WriteMessage writeMessage = WriteMessage.Alloc();
				writeMessage.SetData(data);
				writeMessage.ByteSended = 0;
				writeMessage.Context = sendDelegate;
				this.writeQueue.Enqueue(writeMessage);
				bool flag3 = !this.writing;
				if (flag3)
				{
					this.Flush();
				}
			}
		}

		public void StartReceive()
		{
			try
			{
				int num = this.readBuffer.Prepare(this.ReadLength);
				this.socket.BeginReceive(this.readBuffer.Buffer, num, this.ReadLength, SocketFlags.None, new AsyncCallback(this.ReceiveCallback), this);
			}
			catch (Exception ex)
			{
				Debug.LogErrorFormat("[NetClient]Socket receive: {0}", new object[] { ex.Message });
				this.socket.Close();
				bool flag = this.DisconnectEvent != null;
				if (flag)
				{
					this.DisconnectEvent();
				}
			}
		}

		private void ConnectCallback(IAsyncResult ar)
		{
			NetClient.ConnectDelegate connectDelegate = (NetClient.ConnectDelegate)ar.AsyncState;
			try
			{
				this.socket.EndConnect(ar);
				bool flag = connectDelegate != null;
				if (flag)
				{
					bool isCompleted = ar.IsCompleted;
					Scheduler.PostTask(delegate
					{
						connectDelegate(isCompleted);
					});
				}
			}
			catch (ObjectDisposedException)
			{
			}
			catch (Exception ex)
			{
				Debug.LogErrorFormat("[NetClient]Socket receive: {0}", new object[] { ex.Message });
				bool flag2 = connectDelegate != null;
				if (flag2)
				{
					Scheduler.PostTask(delegate
					{
						connectDelegate(false);
					});
				}
			}
		}

		private void Flush()
		{
			bool flag = this.writeQueue.Count > 0;
			if (flag)
			{
				WriteMessage writeMessage = this.writeQueue.Dequeue();
				try
				{
					this.writing = true;
					this.socket.BeginSend(writeMessage.Buffer, writeMessage.ByteSended, writeMessage.BufferLength - writeMessage.ByteSended, SocketFlags.None, new AsyncCallback(this.SendCallback), writeMessage);
				}
				catch (Exception ex)
				{
					Debug.LogErrorFormat("[NetClient]Socket send error: {0}", new object[] { ex.Message });
					this.writing = false;
					this.socket.Close();
					bool flag2 = this.DisconnectEvent != null;
					if (flag2)
					{
						this.DisconnectEvent();
					}
				}
			}
			else
			{
				this.writing = false;
			}
		}

		private void SendCallback(IAsyncResult ar)
		{
			try
			{
				WriteMessage message = (WriteMessage)ar.AsyncState;
				int num = this.socket.EndSend(ar);
				message.ByteSended += num;
				bool flag = message.ByteSended < message.BufferLength;
				if (flag)
				{
					this.socket.BeginSend(message.Buffer, message.ByteSended, message.BufferLength - message.ByteSended, SocketFlags.None, new AsyncCallback(this.SendCallback), message);
				}
				else
				{
					NetClient.SendDelegate sendDelegate = (NetClient.SendDelegate)message.Context;
					Scheduler.PostTask(delegate
					{
						try
						{
							WriteMessage.Free(message);
							bool flag2 = sendDelegate != null;
							if (flag2)
							{
								sendDelegate();
							}
						}
						finally
						{
							this.Flush();
						}
					});
				}
			}
			catch (ObjectDisposedException)
			{
			}
			catch (Exception ex)
			{
				Debug.LogErrorFormat("[NetClient]SendCallback, Socket send: {0}", new object[] { ex.Message });
				Scheduler.PostTask(delegate
				{
					this.socket.Close();
					bool flag3 = this.DisconnectEvent != null;
					if (flag3)
					{
						this.DisconnectEvent();
					}
				});
			}
		}

		private void ReceiveCallback(IAsyncResult ar)
		{
			try
			{
				int length = this.socket.EndReceive(ar);
				bool flag = length > 0;
				if (flag)
				{
					Scheduler.PostTask(delegate
					{
						this.ProcessReadBuffer(length);
						bool connected = this.socket.Connected;
						if (connected)
						{
							this.StartReceive();
						}
					});
				}
				else
				{
					Scheduler.PostTask(delegate
					{
						this.socket.Close();
						bool flag2 = this.DisconnectEvent != null;
						if (flag2)
						{
							this.DisconnectEvent();
						}
					});
				}
			}
			catch (ObjectDisposedException)
			{
			}
			catch (Exception ex)
			{
				Debug.LogErrorFormat("Socket receive: {0}", new object[] { ex.Message });
				Scheduler.PostTask(delegate
				{
					this.socket.Close();
					bool flag3 = this.DisconnectEvent != null;
					if (flag3)
					{
						this.DisconnectEvent();
					}
				});
			}
		}

		private void ProcessReadBuffer(int length)
		{
			bool flag = !this.readBuffer.Submit(length);
			if (flag)
			{
				Debug.LogError("[NetClient]ProcessReadBuffer, NetClient receive buffer submit out of range.");
			}
			while (this.readBuffer.Payload >= 4)
			{
				uint num = BitConverter.ToUInt32(this.readBuffer.Buffer, this.readBuffer.DataStart);
				bool flag2 = (long)this.readBuffer.Payload < (long)((ulong)(4U + num));
				if (flag2)
				{
					break;
				}
				bool flag3 = (long)this.messageBuffer.Length < (long)((ulong)num);
				if (flag3)
				{
					this.messageBuffer = new byte[num];
				}
				Array.Copy(this.readBuffer.Buffer, (long)(this.readBuffer.DataStart + 4), this.messageBuffer, 0L, (long)((ulong)num));
				this.readBuffer.Consume((int)(num + 4U));
				bool flag4 = this.ReceiveEvent != null;
				if (flag4)
				{
					try
					{
						this.ReceiveEvent(this.messageBuffer, num);
					}
					catch (Exception ex)
					{
						Debug.LogErrorFormat("[NetClient]ProcessReadBuffer, Exception, {0}", new object[] { ex.ToString() });
					}
				}
			}
		}

		private Socket socket;

		private int ReadLength = 32768;

		private NetBuffer readBuffer = null;

		private byte[] messageBuffer = new byte[16384];

		private Queue<WriteMessage> writeQueue = new Queue<WriteMessage>();

		private bool writing = false;

		public delegate void ConnectDelegate(bool isCompleted);

		public delegate void DisconnectDelegate();

		public delegate void SendDelegate();

		public delegate void ReceiveDelegate(byte[] message, uint length);
	}
}
