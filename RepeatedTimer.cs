﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class RepeatedTimer : IDisposable
	{
		public float Speed
		{
			get
			{
				return this.speed;
			}
			set
			{
				this.speed = value;
			}
		}

		public float LeftTime
		{
			get
			{
				return this.leftTime;
			}
		}

		public float RepeatTime
		{
			get
			{
				return this.repeatTime;
			}
		}

		public static RepeatedTimer Repeat(float interval, Action task)
		{
			RepeatedTimer repeatedTimer = new RepeatedTimer();
			repeatedTimer.leftTime = interval;
			repeatedTimer.repeatTime = interval;
			repeatedTimer.unscaled = false;
			repeatedTimer.task = task;
			repeatedTimer.Start();
			return repeatedTimer;
		}

		public static RepeatedTimer Repeat(float delay, float interval, Action task)
		{
			RepeatedTimer repeatedTimer = new RepeatedTimer();
			repeatedTimer.leftTime = delay;
			repeatedTimer.repeatTime = interval;
			repeatedTimer.unscaled = false;
			repeatedTimer.task = task;
			repeatedTimer.Start();
			return repeatedTimer;
		}

		public void Dispose()
		{
			Scheduler.RemoveFrameListener(this.updateHandle);
			this.updateHandle = null;
		}

		private void Start()
		{
			this.updateHandle = Scheduler.AddFrameListener(new Action(this.Update));
		}

		private void Update()
		{
			bool flag = this.unscaled;
			if (flag)
			{
				this.leftTime -= Time.unscaledDeltaTime * this.speed;
			}
			else
			{
				this.leftTime -= Time.deltaTime * this.speed;
			}
			bool flag2 = this.leftTime <= 0f;
			if (flag2)
			{
				try
				{
					this.task();
				}
				catch (Exception ex)
				{
					RepeatedTimer.logger.LogError(ex);
				}
				finally
				{
					this.leftTime = this.repeatTime;
				}
			}
		}

		private static Logger logger = LogSystem.GetLogger("RepeatedTimer");

		private LinkedListNode<Action> updateHandle;

		private float leftTime;

		private float repeatTime;

		private bool unscaled;

		private float speed = 1f;

		private Action task;
	}
}
