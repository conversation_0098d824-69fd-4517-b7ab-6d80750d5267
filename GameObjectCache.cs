﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	public sealed class GameObjectCache
	{
		internal GameObjectCache(Dictionary<GameObject, GameObjectCache> lookup)
		{
			this.sweepChecker = delegate(GameObjectCache.CacheItem instance)
			{
				bool flag = instance.CacheObject != null;
				bool flag3;
				if (flag)
				{
					float num = instance.LastFreeTime + this.releaseAfterFree;
					bool flag2 = Time.realtimeSinceStartup > num;
					if (flag2)
					{
						Object.Destroy(instance.CacheObject);
						flag3 = true;
					}
					else
					{
						flag3 = false;
					}
				}
				else
				{
					Debug.LogErrorFormat("The instance of prefab '{0}'in the pool has been destroy.", new object[] { this.prefab.name });
					flag3 = true;
				}
				return flag3;
			};
			this.lookup = lookup;
		}

		public string Error { get; private set; }

		public bool Loading { get; set; }

		public int SpawnCount
		{
			get
			{
				return this.spawnCount;
			}
		}

		public int CacheCount
		{
			get
			{
				return this.instances.Count;
			}
		}

		internal bool HasPrefab
		{
			get
			{
				return this.prefab != null;
			}
		}

		internal float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				bool flag = this.poolStrategy != null;
				if (flag)
				{
					this.releaseAfterFree = this.poolStrategy.InstanceReleaseAfterFree;
				}
				else
				{
					this.releaseAfterFree = this.defaultReleaseAfterFree;
				}
			}
		}

		internal int DefaultInstancePoolCount
		{
			get
			{
				return this.defaultInstancePoolCount;
			}
			set
			{
				this.defaultInstancePoolCount = value;
				bool flag = this.poolStrategy != null;
				if (flag)
				{
					this.instancePoolCount = this.poolStrategy.InstancePoolCount;
				}
				else
				{
					this.instancePoolCount = this.defaultInstancePoolCount;
				}
			}
		}

		internal void SetPrefab(GameObject prefab)
		{
			this.prefab = prefab;
			this.poolStrategy = this.prefab.GetComponent<PoolStrategy>();
			bool flag = this.poolStrategy != null;
			if (flag)
			{
				this.releaseAfterFree = this.poolStrategy.InstanceReleaseAfterFree;
				this.instancePoolCount = this.poolStrategy.InstancePoolCount;
			}
			else
			{
				this.releaseAfterFree = this.DefaultReleaseAfterFree;
				this.instancePoolCount = this.DefaultInstancePoolCount;
			}
		}

		internal void LoadPrefab(AssetID assetID)
		{
			this.Loading = true;
			this.LoadPrefabImpl(assetID);
		}

		internal void ClearPrefab()
		{
			bool flag = this.prefab != null;
			if (flag)
			{
				bool flag2 = this.isFromAsset;
				if (flag2)
				{
					Singleton<PrefabPool>.Instance.Free(this.prefab, false);
				}
				this.prefab = null;
			}
		}

		internal GameObject Spawn(Transform parent)
		{
			this.spawnCount++;
			while (this.instances.Count > 0)
			{
				int num = this.instances.Count - 1;
				GameObjectCache.CacheItem cacheItem = this.instances[num];
				this.instances.RemoveAt(num);
				GameObject cacheObject = cacheItem.CacheObject;
				bool flag = cacheObject != null;
				if (flag)
				{
					cacheObject.SetActive(true);
					cacheObject.transform.SetParent(parent);
					cacheObject.transform.localPosition = this.prefab.transform.localPosition;
					cacheObject.transform.localRotation = this.prefab.transform.localRotation;
					cacheObject.transform.localScale = this.prefab.transform.localScale;
					this.lookup.Add(cacheObject, this);
					return cacheObject;
				}
				Debug.LogErrorFormat("The instance of prefab '{0}'in the pool has been destroy.", new object[] { this.prefab.name });
			}
			GameObject gameObject = Singleton<PrefabPool>.Instance.Instantiate(this.prefab);
			gameObject.transform.SetParent(parent, false);
			this.lookup.Add(gameObject, this);
			return gameObject;
		}

		internal void Spawn(InstantiateQueue queue, int priority, Action<GameObject> callback)
		{
			this.spawnCount++;
			bool flag = this.instances.Count > 0;
			if (flag)
			{
				int num = this.instances.Count - 1;
				GameObjectCache.CacheItem cacheItem = this.instances[num];
				this.instances.RemoveAt(num);
				GameObject cacheObject = cacheItem.CacheObject;
				Assert.IsNotNull<GameObject>(cacheObject);
				cacheObject.SetActive(true);
				cacheObject.transform.localPosition = this.prefab.transform.localPosition;
				cacheObject.transform.localRotation = this.prefab.transform.localRotation;
				cacheObject.transform.localScale = this.prefab.transform.localScale;
				this.lookup.Add(cacheObject, this);
				callback(cacheObject);
			}
			else
			{
				queue.Instantiate(this.prefab, priority, delegate(GameObject go)
				{
					this.lookup.Add(go, this);
					callback(go);
				});
			}
		}

		internal void PutBack(GameObject instance, bool destroy)
		{
			this.spawnCount--;
			bool flag = destroy || this.instances.Count >= this.instancePoolCount;
			if (flag)
			{
				Object.Destroy(instance);
			}
			else
			{
				GameObjectCache.CacheItem cacheItem = default(GameObjectCache.CacheItem);
				cacheItem.CacheObject = instance;
				cacheItem.LastFreeTime = Time.realtimeSinceStartup;
				this.instances.Add(cacheItem);
			}
		}

		internal void SweepCache()
		{
			this.instances.RemoveAll(this.sweepChecker);
		}

		internal void ClearCache()
		{
			foreach (GameObjectCache.CacheItem cacheItem in this.instances)
			{
				Object.Destroy(cacheItem.CacheObject);
			}
			this.instances.Clear();
		}

		private void LoadPrefabImpl(AssetID assetID)
		{
			Singleton<PrefabPool>.Instance.Load(assetID, delegate(GameObject go)
			{
				bool flag = null == go;
				if (flag)
				{
					this.Error = string.Format("This asset: {0} is not a GameObject", assetID);
				}
				else
				{
					this.SetPrefab(go);
					this.isFromAsset = true;
				}
			}, false);
		}

		private GameObject prefab;

		private Dictionary<GameObject, GameObjectCache> lookup;

		private PoolStrategy poolStrategy;

		private List<GameObjectCache.CacheItem> instances = new List<GameObjectCache.CacheItem>();

		private int spawnCount;

		private float defaultReleaseAfterFree = 30f;

		private int defaultInstancePoolCount = 16;

		private float releaseAfterFree;

		private int instancePoolCount;

		private bool isFromAsset = false;

		private Predicate<GameObjectCache.CacheItem> sweepChecker;

		public struct CacheItem
		{
			public GameObject CacheObject;

			public float LastFreeTime;
		}
	}
}
