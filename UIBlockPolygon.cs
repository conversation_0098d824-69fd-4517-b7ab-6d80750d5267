﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[RequireComponent(typeof(CanvasRenderer))]
	[RequireComponent(typeof(PolygonCollider2D))]
	public sealed class UIBlockPolygon : Graphic, ICanvasRaycastFilter
	{
		public override Texture mainTexture
		{
			get
			{
				return null;
			}
		}

		public override Material materialForRendering
		{
			get
			{
				return null;
			}
		}

		private PolygonCollider2D Polygon
		{
			get
			{
				if (this.polygon == null)
				{
					this.polygon = base.GetComponent<PolygonCollider2D>();
				}
				return this.polygon;
			}
		}

		public bool IsRaycastLocationValid(Vector2 screenPoint, Camera eventCamera)
		{
			bool flag;
			if (eventCamera != null)
			{
				Vector3 vector;
				flag = RectTransformUtility.ScreenPointToWorldPointInRectangle(base.rectTransform, screenPoint, eventCamera, ref vector) && this.Polygon.OverlapPoint(vector);
			}
			else
			{
				flag = this.Polygon.OverlapPoint(screenPoint);
			}
			return flag;
		}

		protected override void OnPopulateMesh(VertexHelper vh)
		{
			vh.Clear();
		}

		private PolygonCollider2D polygon;
	}
}
