﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
	public sealed class SimpleShadowMesh : MonoBehaviour
	{
		internal Material ShadowMaterial
		{
			get
			{
				return this.shadowMaterial;
			}
			set
			{
				bool flag = this.shadowMaterial != value;
				if (flag)
				{
					this.shadowMaterial = value;
					bool flag2 = this.meshRenderer != null;
					if (flag2)
					{
						this.meshRenderer.sharedMaterial = this.shadowMaterial;
					}
				}
			}
		}

		internal LinkedListNode<SimpleShadow> AddShadow(SimpleShadow shadow)
		{
			this.dirty = true;
			return this.shadows.AddLast(shadow);
		}

		internal void RemoveShadow(LinkedListNode<SimpleShadow> node)
		{
			this.dirty = true;
			this.shadows.Remove(node);
		}

		private void Awake()
		{
			MeshFilter component = base.GetComponent<MeshFilter>();
			bool flag = component.mesh == null;
			if (flag)
			{
				component.mesh = new Mesh();
			}
			this.mesh = component.mesh;
			this.mesh.name = "Shadow Mesh";
			this.MakeSureMeshCapacity(32);
			this.meshRenderer = base.GetComponent<MeshRenderer>();
			bool flag2 = this.meshRenderer != null;
			if (flag2)
			{
				this.meshRenderer.shadowCastingMode = 0;
				this.meshRenderer.receiveShadows = false;
				this.meshRenderer.lightProbeUsage = 0;
				this.meshRenderer.sharedMaterial = this.shadowMaterial;
			}
		}

		private void Update()
		{
			this.MakeSureMeshCapacity(this.shadows.Count);
			bool flag = !this.dirty;
			if (flag)
			{
				LinkedListNode<SimpleShadow> next;
				for (LinkedListNode<SimpleShadow> linkedListNode = this.shadows.First; linkedListNode != null; linkedListNode = next)
				{
					next = linkedListNode.Next;
					SimpleShadow value = linkedListNode.Value;
					bool flag2 = value.Dirty;
					if (flag2)
					{
						this.dirty = true;
						break;
					}
				}
			}
			bool flag3 = this.dirty;
			if (flag3)
			{
				int num = 0;
				LinkedListNode<SimpleShadow> next2;
				for (LinkedListNode<SimpleShadow> linkedListNode2 = this.shadows.First; linkedListNode2 != null; linkedListNode2 = next2)
				{
					next2 = linkedListNode2.Next;
					SimpleShadow value2 = linkedListNode2.Value;
					bool visible = value2.Visible;
					if (visible)
					{
						value2.UpdateShadowMesh(this.vertices, this.uvs, num);
						num++;
					}
					value2.Dirty = false;
				}
				this.mesh.vertices = this.vertices;
				this.mesh.uv = this.uvs;
				int num2 = 6 * num;
				bool flag4 = this.triangles == null || this.triangles.Length != num2;
				if (flag4)
				{
					this.triangles = new int[6 * num];
				}
				for (int i = 0; i < num; i++)
				{
					int num3 = 6 * i;
					int num4 = 4 * i;
					this.triangles[num3] = num4;
					this.triangles[num3 + 1] = num4 + 1;
					this.triangles[num3 + 2] = num4 + 2;
					this.triangles[num3 + 3] = num4 + 2;
					this.triangles[num3 + 4] = num4 + 1;
					this.triangles[num3 + 5] = num4 + 3;
				}
				this.mesh.triangles = this.triangles;
				this.mesh.RecalculateBounds();
				this.dirty = false;
			}
		}

		private void MakeSureMeshCapacity(int capacity)
		{
			bool flag = this.shadowCapacity < capacity;
			if (flag)
			{
				this.shadowCapacity = Mathf.Max(this.shadowCapacity * 2, capacity);
				this.vertices = new Vector3[4 * this.shadowCapacity];
				this.uvs = new Vector2[4 * this.shadowCapacity];
			}
		}

		private MeshRenderer meshRenderer;

		private Material shadowMaterial;

		private Mesh mesh;

		private Vector3[] vertices;

		private Vector2[] uvs;

		private int[] triangles;

		private LinkedList<SimpleShadow> shadows = new LinkedList<SimpleShadow>();

		private int shadowCapacity = 0;

		private bool dirty = false;
	}
}
