﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	internal sealed class DiagnosticsWorkbench
	{
		internal void AddTab(IDiagnosticsTab tab)
		{
			this.tabList.Add(tab);
			this.tabDirty = true;
		}

		internal bool Draw(DiagnosticsSystem system)
		{
			bool flag = Screen.height > Screen.width;
			Rect rect;
			if (flag)
			{
				rect..ctor(50f, 50f, 668f, 1180f);
			}
			else
			{
				rect..ctor(50f, 50f, 1180f, 668f);
			}
			GUILayout.BeginArea(rect, DiagnosticsSkin.BenchPanel);
			bool flag2 = true;
			GUILayout.BeginHorizontal(DiagnosticsSkin.BenchTitle, Array.Empty<GUILayoutOption>());
			GUILayout.Space(20f);
			GUILayout.Label("Diagnostics Bench", DiagnosticsSkin.BenchTitleLabel, Array.Empty<GUILayoutOption>());
			bool flag3 = DiagnosticsGUI.CloseButton();
			if (flag3)
			{
				flag2 = false;
			}
			GUILayout.EndHorizontal();
			bool flag4 = this.tabDirty;
			if (flag4)
			{
				this.tabButtons = new string[this.tabList.Count];
				for (int i = 0; i < this.tabList.Count; i++)
				{
					this.tabButtons[i] = this.tabList[i].TabName;
				}
				this.tabDirty = false;
			}
			this.selectedTab = GUILayout.Toolbar(this.selectedTab, this.tabButtons, DiagnosticsSkin.BenchTabbar, Array.Empty<GUILayoutOption>());
			GUILayout.BeginVertical(DiagnosticsSkin.BenchContent, Array.Empty<GUILayoutOption>());
			this.scrollPos = GUILayout.BeginScrollView(this.scrollPos, Array.Empty<GUILayoutOption>());
			this.tabList[this.selectedTab].DrawTab();
			GUILayout.EndScrollView();
			GUILayout.Space(10f);
			GUILayout.EndVertical();
			GUILayout.EndArea();
			return flag2;
		}

		private List<IDiagnosticsTab> tabList = new List<IDiagnosticsTab>();

		private string[] tabButtons;

		private bool tabDirty;

		private int selectedTab;

		private Vector2 scrollPos;
	}
}
