﻿using System;
using System.Collections;
using UnityEngine;

namespace Nirvana
{
	public sealed class AudioSequenceController : IAudioController, IAudioPlayer
	{
		public AudioSequenceController(AudioItem item, AudioSourcePool pool, AudioSource source, AudioSubItem[] subItems)
		{
			this.item = item;
			this.pool = pool;
			this.source = source;
			this.subItems = subItems;
			this.player = new AudioItemPlayer(source, 0f, 0f, 0f);
		}

		public bool IsPlaying
		{
			get
			{
				return !this.stopped || this.player.IsPlaying;
			}
		}

		public float LeftTime
		{
			get
			{
				bool flag = this.stopped;
				float num;
				if (flag)
				{
					num = 0f;
				}
				else
				{
					num = this.player.TotalTime - this.player.PlayTime;
				}
				return num;
			}
		}

		public override string ToString()
		{
			return this.item.name;
		}

		public IEnumerator WaitFinish()
		{
			return new WaitUntil(() => !this.IsPlaying);
		}

		public void Stop()
		{
			this.stopped = true;
			this.player.Stop();
		}

		public void SetPosition(Vector3 position)
		{
			this.source.transform.position = position;
		}

		public void SetTransform(Transform transform)
		{
			this.trans3D = transform;
		}

		public void Play()
		{
			this.stopped = false;
			this.playIndex = 0;
			this.PlayCurrentSubItem();
		}

		public void Update()
		{
			bool flag = this.stopped && !this.player.IsPlaying;
			if (!flag)
			{
				bool flag2 = this.trans3D != null;
				if (flag2)
				{
					this.source.transform.SetPositionAndRotation(this.trans3D.position, this.trans3D.rotation);
					this.source.transform.localScale = this.trans3D.localScale;
				}
				this.player.Update();
				float num = this.player.TotalTime - this.player.PlayTime;
				bool flag3 = num <= this.player.FadeoutTime && !this.player.IsFadeout;
				if (flag3)
				{
					this.player.Stop();
				}
				bool flag4 = !this.stopped && !this.player.IsPlaying;
				if (flag4)
				{
					this.playIndex = (this.playIndex + 1) % this.subItems.Length;
					this.PlayCurrentSubItem();
				}
			}
		}

		public void FinshAudio()
		{
			bool flag = this.item != null;
			if (flag)
			{
				this.item.ReducePlayingCount(this);
			}
			bool flag2 = this.pool != null;
			if (flag2)
			{
				this.pool.Free(this.source);
			}
		}

		private void PlayCurrentSubItem()
		{
			AudioSubItem audioSubItem = this.subItems[this.playIndex];
			this.item.SetupAudioSource(this.source, audioSubItem);
			this.player.Delay = this.item.Delay + audioSubItem.GetDelay();
			this.player.FadeinTime = audioSubItem.GetFadeInTime();
			this.player.FadeoutTime = audioSubItem.GetFadeOutTime();
			this.player.Play();
		}

		private AudioItem item;

		private AudioSourcePool pool;

		private AudioSource source;

		private AudioItemPlayer player;

		private AudioSubItem[] subItems;

		private bool stopped;

		private int playIndex;

		private Transform trans3D;
	}
}
