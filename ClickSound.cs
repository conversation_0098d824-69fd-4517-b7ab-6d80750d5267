﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/Click Sound")]
	public sealed class ClickSound : MonoBehaviour, IPointerClickHandler, IEventSystemHandler
	{
		public void OnPointerClick(PointerEventData eventData)
		{
			bool flag = !this.audioAsset.IsEmpty;
			if (flag)
			{
				bool flag2 = ClickSound.OnClick != null;
				if (flag2)
				{
					ClickSound.OnClick(this.audioAsset.BundleName, this.audioAsset.AssetName);
				}
			}
		}

		public static ClickSound.ClickDelegate OnClick;

		[SerializeField]
		[Tooltip("The audio asset")]
		public AssetID audioAsset;

		public delegate void ClickDelegate(string bundleName, string assetName);
	}
}
