﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Gray Scale")]
	[ExecuteInEditMode]
	[DisallowMultipleComponent]
	[RequireComponent(typeof(UIMaterialEffect))]
	public sealed class UIGrayscale : MonoBehaviour
	{
		public int GrayScale
		{
			get
			{
				return this.grayscale;
			}
			set
			{
				bool flag = this.grayscale != value;
				if (flag)
				{
					this.grayscale = value;
					this.Refresh();
				}
			}
		}

		private void Awake()
		{
			this.Refresh();
		}

		private void OnDestroy()
		{
			bool flag = this.materialEffect != null;
			if (flag)
			{
				UIEffectMaterialKey materialKey = this.materialEffect.MaterialKey;
				materialKey.GrayScale = 0;
				this.materialEffect.MaterialKey = materialKey;
				this.materialEffect.MarkDirty();
			}
		}

		private void Refresh()
		{
			bool flag = this.materialEffect == null;
			if (flag)
			{
				this.materialEffect = this.GetOrAddComponent<UIMaterialEffect>();
			}
			UIEffectMaterialKey materialKey = this.materialEffect.MaterialKey;
			materialKey.GrayScale = (byte)this.grayscale;
			this.materialEffect.MaterialKey = materialKey;
			this.materialEffect.MarkDirty();
		}

		[SerializeField]
		[Range(0f, 255f)]
		private int grayscale = 0;

		private UIMaterialEffect materialEffect;
	}
}
