﻿using System;
using UnityEngine;

namespace Nirvana
{
	[Serializable]
	public struct AssetID : IEquatable<AssetID>
	{
		public AssetID(string bundleName, string assetName)
		{
			this.bundleName = bundleName;
			this.assetName = assetName;
			this.assetGUID = string.Empty;
		}

		public string BundleName
		{
			get
			{
				return this.bundleName;
			}
			set
			{
				this.bundleName = value;
			}
		}

		public string AssetName
		{
			get
			{
				return this.assetName;
			}
			set
			{
				this.assetName = value;
			}
		}

		public bool IsEmpty
		{
			get
			{
				return string.IsNullOrEmpty(this.BundleName) || string.IsNullOrEmpty(this.AssetName);
			}
		}

		public static AssetID Parse(string text)
		{
			AssetID assetID;
			if (string.IsNullOrEmpty(text))
			{
				assetID = default(AssetID);
			}
			else
			{
				int num = text.IndexOf(':');
				if (num < 0)
				{
					throw new FormatException("Can not parse AssetID.");
				}
				string text2 = text.Substring(0, num);
				string text3 = text.Substring(num + 1);
				assetID = new AssetID(text2, text3);
			}
			return assetID;
		}

		public string GetAssetPath()
		{
			return "";
		}

		public override string ToString()
		{
			return this.BundleName + ": " + this.AssetName;
		}

		public bool Equals(AssetID other)
		{
			return this.BundleName == other.BundleName && this.AssetName == other.AssetName;
		}

		public override int GetHashCode()
		{
			int hashCode = this.bundleName.GetHashCode();
			return (397 * hashCode) ^ this.assetName.GetHashCode();
		}

		public string AssetGUID
		{
			get
			{
				return this.assetGUID;
			}
			set
			{
				this.assetGUID = value;
			}
		}

		public static readonly AssetID Empty = new AssetID(string.Empty, string.Empty);

		[SerializeField]
		private string bundleName;

		[SerializeField]
		private string assetName;

		[SerializeField]
		private string assetGUID;
	}
}
