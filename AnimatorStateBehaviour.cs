﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class AnimatorStateBehaviour : StateMachineBehaviour
	{
		public override void OnStateEnter(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
		{
			bool flag = this.enterActions != null;
			if (flag)
			{
				foreach (AnimatorAction animatorAction in this.enterActions)
				{
					animatorAction.TriggerAction(animator, stateInfo);
				}
			}
		}

		public override void OnStateExit(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
		{
			bool flag = this.exitActions != null;
			if (flag)
			{
				foreach (AnimatorAction animatorAction in this.exitActions)
				{
					animatorAction.TriggerAction(animator, stateInfo);
				}
			}
		}

		[SerializeField]
		[Tooltip("The state enter event to dispatch.")]
		private AnimatorAction[] enterActions;

		[SerializeField]
		[Tooltip("The state exit event to dispatch.")]
		private AnimatorAction[] exitActions;
	}
}
