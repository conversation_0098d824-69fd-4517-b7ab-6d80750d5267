﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	internal static class UIEffectMaterials
	{
		internal static Material Get(UIEffectMaterialKey key)
		{
			UIEffectMaterials.MaterialRef materialRef;
			bool flag = UIEffectMaterials.materials.TryGetValue(key, out materialRef);
			Material material;
			if (flag)
			{
				UIEffectMaterials.MaterialRef materialRef2 = materialRef;
				int num = materialRef2.RefCount + 1;
				materialRef2.RefCount = num;
				material = materialRef.Material;
			}
			else
			{
				Material material2 = key.CreateMaterial();
				bool flag2 = material2 == null;
				if (flag2)
				{
					material = null;
				}
				else
				{
					UIEffectMaterials.materials.Add(key, new UIEffectMaterials.MaterialRef(material2));
					UIEffectMaterials.lookup.Add(material2, key);
					material = material2;
				}
			}
			return material;
		}

		internal static void Free(Material material)
		{
			UIEffectMaterialKey uieffectMaterialKey;
			bool flag = !UIEffectMaterials.lookup.TryGetValue(material, out uieffectMaterialKey);
			if (flag)
			{
				Debug.LogError("Can not find the material key.");
			}
			else
			{
				UIEffectMaterials.MaterialRef materialRef;
				bool flag2 = !UIEffectMaterials.materials.TryGetValue(uieffectMaterialKey, out materialRef);
				if (flag2)
				{
					Debug.LogError("Can not find the material reference.");
				}
				else
				{
					UIEffectMaterials.MaterialRef materialRef2 = materialRef;
					int num = materialRef2.RefCount - 1;
					materialRef2.RefCount = num;
					bool flag3 = num <= 0;
					if (flag3)
					{
						bool isPlaying = Application.isPlaying;
						if (isPlaying)
						{
							Object.Destroy(materialRef.Material);
						}
						else
						{
							Object.DestroyImmediate(materialRef.Material);
						}
						UIEffectMaterials.materials.Remove(uieffectMaterialKey);
						UIEffectMaterials.lookup.Remove(material);
					}
				}
			}
		}

		private static Dictionary<UIEffectMaterialKey, UIEffectMaterials.MaterialRef> materials = new Dictionary<UIEffectMaterialKey, UIEffectMaterials.MaterialRef>();

		private static Dictionary<Material, UIEffectMaterialKey> lookup = new Dictionary<Material, UIEffectMaterialKey>();

		private class MaterialKeyComparer : IEqualityComparer<UIEffectMaterialKey>
		{
			public bool Equals(UIEffectMaterialKey x, UIEffectMaterialKey y)
			{
				return x.Equals(y);
			}

			public int GetHashCode(UIEffectMaterialKey obj)
			{
				return obj.GetHashCode();
			}
		}

		private class MaterialRef
		{
			public MaterialRef(Material material)
			{
				this.material = material;
				this.refcount = 1;
			}

			public Material Material
			{
				get
				{
					return this.material;
				}
			}

			public int RefCount
			{
				get
				{
					return this.refcount;
				}
				set
				{
					this.refcount = value;
				}
			}

			private Material material;

			private int refcount;
		}
	}
}
