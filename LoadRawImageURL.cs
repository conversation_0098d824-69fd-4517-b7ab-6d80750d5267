﻿using System;
using System.Collections;
using System.IO;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.UI;

namespace Nirvana
{
	[RequireComponent(typeof(RawImage))]
	[ExecuteInEditMode]
	public sealed class LoadRawImageURL : MonoBehaviour
	{
		public bool AutoFitNativeSize
		{
			get
			{
				return this.autoFitNativeSize;
			}
			set
			{
				this.autoFitNativeSize = value;
			}
		}

		public bool AutoUpdateAspectRatio
		{
			get
			{
				return this.autoUpdateAspectRatio;
			}
			set
			{
				this.autoUpdateAspectRatio = value;
			}
		}

		public bool AutoDisable
		{
			get
			{
				return this.autoDisable;
			}
			set
			{
				this.autoDisable = value;
			}
		}

		public string URL
		{
			get
			{
				return this.url;
			}
			set
			{
				bool flag = !object.Equals(this.url, value);
				if (flag)
				{
					this.url = value;
					bool flag2 = !string.IsNullOrEmpty(this.url);
					if (flag2)
					{
						this.ChangeURL(this.url);
					}
				}
			}
		}

		private void Awake()
		{
			this.rawImage = base.GetComponent<RawImage>();
		}

		private void OnEnable()
		{
			bool flag = this.rawImage.texture == null;
			if (flag)
			{
				bool flag2 = !string.IsNullOrEmpty(this.url);
				if (flag2)
				{
					this.ChangeURL(this.url);
				}
				else
				{
					bool flag3 = this.autoDisable;
					if (flag3)
					{
						this.rawImage.enabled = false;
					}
				}
			}
		}

		private void OnDestroy()
		{
			bool flag = this.rawImage != null;
			if (flag)
			{
				this.rawImage.texture = null;
			}
		}

		private void ChangeURL(string url)
		{
			bool flag = url.Contains("://");
			if (flag)
			{
				Scheduler.RunCoroutine(this.ChangeURLNetwork(this.url));
			}
			else
			{
				byte[] array = File.ReadAllBytes(url);
				TextureFormat textureFormat = 3;
				bool flag2 = array != null && array[1] == 80 && array[2] == 78 && array[3] == 71;
				if (flag2)
				{
					textureFormat = 4;
				}
				Texture2D texture2D = new Texture2D(2, 2, textureFormat, false);
				bool flag3 = !ImageConversion.LoadImage(texture2D, array, true);
				if (flag3)
				{
					this.rawImage.texture = null;
					bool flag4 = this.autoDisable;
					if (flag4)
					{
						this.rawImage.enabled = false;
					}
				}
				else
				{
					texture2D.wrapMode = 1;
					this.rawImage.texture = texture2D;
					bool flag5 = this.autoDisable;
					if (flag5)
					{
						this.rawImage.enabled = true;
					}
					bool flag6 = this.autoFitNativeSize;
					if (flag6)
					{
						this.rawImage.SetNativeSize();
					}
					bool flag7 = this.autoUpdateAspectRatio;
					if (flag7)
					{
						AspectRatioFitter component = this.rawImage.GetComponent<AspectRatioFitter>();
						bool flag8 = component != null;
						if (flag8)
						{
							component.aspectRatio = (float)texture2D.width / (float)texture2D.height;
						}
					}
				}
			}
		}

		private IEnumerator ChangeURLNetwork(string url)
		{
			UnityWebRequest request = UnityWebRequestTexture.GetTexture(url);
			yield return request.Send();
			bool isNetworkError = request.isNetworkError;
			if (isNetworkError)
			{
				Debug.LogError("Load image from url failed: " + request.error);
				yield break;
			}
			bool flag = this == null;
			if (flag)
			{
				yield break;
			}
			bool flag2 = url != this.url;
			if (flag2)
			{
				yield break;
			}
			Texture2D texture = DownloadHandlerTexture.GetContent(request);
			request.Dispose();
			bool flag3 = texture != null;
			if (flag3)
			{
				this.rawImage.texture = texture;
				bool flag4 = this.autoDisable;
				if (flag4)
				{
					this.rawImage.enabled = true;
				}
				bool flag5 = this.autoFitNativeSize;
				if (flag5)
				{
					this.rawImage.SetNativeSize();
				}
				bool flag6 = this.autoUpdateAspectRatio;
				if (flag6)
				{
					AspectRatioFitter ratioFitter = this.rawImage.GetComponent<AspectRatioFitter>();
					bool flag7 = ratioFitter != null;
					if (flag7)
					{
						ratioFitter.aspectRatio = (float)texture.width / (float)texture.height;
					}
					ratioFitter = null;
				}
			}
			else
			{
				this.rawImage.texture = null;
				bool flag8 = this.autoDisable;
				if (flag8)
				{
					this.rawImage.enabled = false;
				}
			}
			yield break;
		}

		[SerializeField]
		[Tooltip("The raw image url.")]
		public string url;

		[SerializeField]
		public bool autoFitNativeSize = false;

		[SerializeField]
		public bool autoUpdateAspectRatio = false;

		[SerializeField]
		public bool autoDisable = true;

		private RawImage rawImage;

		private UnityWebRequest request;
	}
}
