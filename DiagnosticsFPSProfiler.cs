﻿using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

namespace Nirvana
{
	[Serializable]
	internal sealed class DiagnosticsFPSProfiler : IDiagnosticsDocker, IDiagnosticsTab
	{
		public DiagnosticsDockAnchor DockAnchor
		{
			get
			{
				return this.dockAnchor;
			}
		}

		public bool DockDirty
		{
			get
			{
				return this.dockDitry;
			}
			set
			{
				this.dockDitry = value;
			}
		}

		public string TabName
		{
			get
			{
				return "FPS";
			}
		}

		public string GetDockText()
		{
			this.textBuilder.Remove(0, this.textBuilder.Length);
			this.textBuilder.Append("<size=");
			this.textBuilder.Append(this.dockFontSize);
			this.textBuilder.Append("><color=#");
			this.textBuilder.Append(this.GetFPSColor(this.sampleFPS));
			this.textBuilder.Append("><b>FPS: ");
			this.textBuilder.Concat(this.sampleFPS);
			bool flag = this.dockFPSMillisecond;
			if (flag)
			{
				this.textBuilder.Append(" [");
				this.textBuilder.Concat(1000f / (float)this.sampleFPS);
				this.textBuilder.Append("ms] ");
			}
			this.textBuilder.Append("</b></color>");
			bool flag2 = this.dockFPSAverage;
			if (flag2)
			{
				this.textBuilder.Append("<color=#");
				this.textBuilder.Append(this.GetFPSColor(this.averageFPS));
				this.textBuilder.Append("><b>AVG: ");
				this.textBuilder.Concat(this.averageFPS);
				this.textBuilder.Append("</b></color>\n");
			}
			bool flag3 = this.dockFPSMinMax;
			if (flag3)
			{
				this.textBuilder.Append("<color=#");
				this.textBuilder.Append(this.GetFPSColor(this.minFPS));
				this.textBuilder.Append("><b>MIN: ");
				this.textBuilder.Concat(this.minFPS);
				this.textBuilder.Append("</b></color> <color=#");
				this.textBuilder.Append(this.GetFPSColor(this.maxFPS));
				this.textBuilder.Append("><b>MAX: ");
				this.textBuilder.Concat(this.maxFPS);
				this.textBuilder.Append("</b></color>\n");
			}
			this.textBuilder.Append("</size>");
			return this.textBuilder.ToString();
		}

		public void DrawTab()
		{
			GUILayout.Label("Profile Chart: ", Array.Empty<GUILayoutOption>());
			GUILayout.BeginVertical(GUI.skin.textArea, Array.Empty<GUILayoutOption>());
			this.DrawChart();
			GUILayout.EndVertical();
			GUILayout.Label("Statistics: ", Array.Empty<GUILayoutOption>());
			GUILayout.BeginVertical(GUI.skin.textArea, Array.Empty<GUILayoutOption>());
			this.DrawStatistics();
			GUILayout.EndVertical();
			GUILayout.Label("Docker: ", Array.Empty<GUILayoutOption>());
			GUILayout.BeginVertical(GUI.skin.textArea, Array.Empty<GUILayoutOption>());
			this.DrawDock();
			GUILayout.EndVertical();
		}

		internal void Initialize()
		{
			this.sampleFPS = 0;
			this.averageFPS = 0;
			this.minFPS = int.MaxValue;
			this.maxFPS = int.MinValue;
			this.sampleTimeLeft = this.sampleInterval;
			this.sampleAccumTime = 0f;
			this.sampleFrameCount = 0;
			this.sampleWindow.Clear();
		}

		internal void Update()
		{
			this.sampleTimeLeft -= Time.unscaledDeltaTime;
			this.sampleAccumTime += Time.unscaledDeltaTime;
			this.sampleFrameCount++;
			bool flag = this.sampleTimeLeft <= 0f;
			if (flag)
			{
				this.sampleFPS = (int)((float)this.sampleFrameCount / this.sampleAccumTime);
				this.sampleTimeLeft = this.sampleInterval;
				this.sampleAccumTime = 0f;
				this.sampleFrameCount = 0;
				this.sampleWindow.Enqueue(this.sampleFPS);
				bool flag2 = this.sampleWindow.Count > this.sampleWindowLength;
				if (flag2)
				{
					this.sampleWindow.Dequeue();
				}
				int num = 0;
				this.minFPS = int.MaxValue;
				this.maxFPS = int.MinValue;
				foreach (int num2 in this.sampleWindow)
				{
					num += num2;
					bool flag3 = num2 < this.minFPS;
					if (flag3)
					{
						this.minFPS = num2;
					}
					bool flag4 = num2 > this.maxFPS;
					if (flag4)
					{
						this.maxFPS = num2;
					}
				}
				this.averageFPS = num / this.sampleWindow.Count;
				this.DockDirty = true;
			}
		}

		private void DrawChart()
		{
			Color color = GUI.color;
			GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
			foreach (int num in this.sampleWindow)
			{
				bool flag = num > this.warningFPSValue;
				if (flag)
				{
					GUI.color = Color.green;
				}
				else
				{
					bool flag2 = num > this.criticalFPSValue;
					if (flag2)
					{
						GUI.color = Color.yellow;
					}
					else
					{
						GUI.color = Color.red;
					}
				}
				DiagnosticsGUI.ChartPillar(100f * (float)num / (float)this.maxFPS, 100f);
			}
			for (int i = this.sampleWindow.Count; i < this.sampleWindowLength; i++)
			{
				DiagnosticsGUI.ChartPillar(0f, 100f);
			}
			GUILayout.EndHorizontal();
			GUI.color = color;
			string text = string.Format("<color=#{0}><b>FPS: {1} [{2}ms]</b></color> <color=#{3}><b>AVG: {4}</b></color> <color=#{5}><b>MIN: {6}</b></color> <color=#{7}><b>MAX: {8}</b></color>", new object[]
			{
				this.GetFPSColor(this.sampleFPS),
				this.sampleFPS,
				1000f / (float)this.sampleFPS,
				this.GetFPSColor(this.averageFPS),
				this.averageFPS,
				this.GetFPSColor(this.minFPS),
				this.minFPS,
				this.GetFPSColor(this.maxFPS),
				this.maxFPS
			});
			GUILayout.Label(text.ToString(), Array.Empty<GUILayoutOption>());
		}

		private void DrawStatistics()
		{
			this.sampleInterval = DiagnosticsGUI.SliderLabel(this.sampleInterval, "Sample Interval: ", 0.1f, 10f);
			this.sampleWindowLength = (int)DiagnosticsGUI.SliderLabel((float)this.sampleWindowLength, "Samples Window Length: ", 10f, 50f);
			this.warningFPSValue = (int)DiagnosticsGUI.SliderLabel((float)this.warningFPSValue, "FPS Warning Value: ", 0f, 100f);
			this.criticalFPSValue = (int)DiagnosticsGUI.SliderLabel((float)this.criticalFPSValue, "FPS Warning Value: ", 0f, 100f);
		}

		private void DrawDock()
		{
			DiagnosticsDockAnchor diagnosticsDockAnchor = DiagnosticsGUI.Anchor(this.dockAnchor, "Dock Anchor: ");
			bool flag = this.dockAnchor != diagnosticsDockAnchor;
			if (flag)
			{
				this.dockDitry = true;
				this.dockAnchor = diagnosticsDockAnchor;
			}
			int num = (int)DiagnosticsGUI.SliderLabel((float)this.dockFontSize, "Dock Font Size", 5f, 25f);
			bool flag2 = this.dockFontSize != num;
			if (flag2)
			{
				this.dockDitry = true;
				this.dockFontSize = num;
			}
			bool flag3 = DiagnosticsGUI.Toggle(this.dockFPSAverage, "Dock FPS Average");
			bool flag4 = this.dockFPSAverage != flag3;
			if (flag4)
			{
				this.dockDitry = true;
				this.dockFPSAverage = flag3;
			}
			bool flag5 = DiagnosticsGUI.Toggle(this.dockFPSMinMax, "Dock FPS Min Max");
			bool flag6 = this.dockFPSMinMax != flag5;
			if (flag6)
			{
				this.dockDitry = true;
				this.dockFPSMinMax = flag5;
			}
		}

		private string GetFPSColor(int fps)
		{
			bool flag = fps > this.warningFPSValue;
			string text;
			if (flag)
			{
				text = "47FF6EFF";
			}
			else
			{
				bool flag2 = fps > this.criticalFPSValue;
				if (flag2)
				{
					text = "FFAD1CFF";
				}
				else
				{
					text = "FF4747FF";
				}
			}
			return text;
		}

		private const string FPSFineColor = "47FF6EFF";

		private const string FPSWarningColor = "FFAD1CFF";

		private const string FPSCriticalColor = "FF4747FF";

		[Header("FPS Statistics Configuration")]
		[SerializeField]
		[Range(0.1f, 10f)]
		[Tooltip("Sample FPS interval in seconds.")]
		private float sampleInterval = 1f;

		[SerializeField]
		[Range(10f, 200f)]
		[Tooltip("The sample window length.")]
		private int sampleWindowLength = 60;

		[SerializeField]
		[Tooltip("If FPS will drop below this value, the fps will mark as warning.")]
		private int warningFPSValue = 50;

		[SerializeField]
		[Tooltip("If FPS will be equal or less this value, the fps will mark as critical.")]
		private int criticalFPSValue = 20;

		[Header("Dock Display Configuration")]
		[SerializeField]
		[Tooltip("The dock anchor.")]
		[EnumLabel]
		private DiagnosticsDockAnchor dockAnchor;

		[SerializeField]
		[Tooltip("The font size of simple panel.")]
		private int dockFontSize = 12;

		[SerializeField]
		[Tooltip("Whether dock the fps millisecond value to the anchor.")]
		private bool dockFPSMillisecond = true;

		[SerializeField]
		[Tooltip("Whether dock the fps average value to the anchor.")]
		private bool dockFPSAverage = true;

		[SerializeField]
		[Tooltip("Whether dock the fps min max value to the anchor.")]
		private bool dockFPSMinMax = true;

		private int sampleFPS;

		private int averageFPS;

		private int minFPS;

		private int maxFPS;

		private float sampleTimeLeft;

		private float sampleAccumTime;

		private int sampleFrameCount;

		private Queue<int> sampleWindow = new Queue<int>();

		private bool dockDitry = true;

		private StringBuilder textBuilder = new StringBuilder();
	}
}
