﻿using System;
using System.Reflection;
using UnityEngine;

namespace Nirvana
{
	public static class GeometryUtilityHelper
	{
		static GeometryUtilityHelper()
		{
			MethodInfo method = typeof(GeometryUtility).GetMethod("Internal_ExtractPlanes", BindingFlags.Static | BindingFlags.NonPublic);
			bool flag = method != null;
			if (flag)
			{
				GeometryUtilityHelper.ExtractPlanesDelegate = (Action<Plane[], Matrix4x4>)Delegate.CreateDelegate(typeof(Action<Plane[], Matrix4x4>), method, false);
			}
		}

		public static void ExtractPlanes(Plane[] planes, Camera camera)
		{
			Matrix4x4 matrix4x = camera.projectionMatrix * camera.worldToCameraMatrix;
			GeometryUtilityHelper.ExtractPlanes(planes, matrix4x);
		}

		public static void ExtractPlanes(Plane[] planes, Matrix4x4 worldToProjectionMatrix)
		{
			bool flag = GeometryUtilityHelper.ExtractPlanesDelegate != null;
			if (flag)
			{
				GeometryUtilityHelper.ExtractPlanesDelegate(planes, worldToProjectionMatrix);
			}
			else
			{
				Plane[] array = GeometryUtility.CalculateFrustumPlanes(worldToProjectionMatrix);
				Array.Copy(array, planes, 6);
			}
		}

		private static readonly Action<Plane[], Matrix4x4> ExtractPlanesDelegate;
	}
}
