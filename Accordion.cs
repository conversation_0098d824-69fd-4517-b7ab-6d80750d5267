﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/Accordion")]
	[RequireComponent(typeof(VerticalLayoutGroup))]
	[RequireComponent(typeof(ContentSizeFitter))]
	[RequireComponent(typeof(ToggleGroup))]
	public sealed class Accordion : MonoBehaviour
	{
		public Accordion.Transition TransitionType
		{
			get
			{
				return this.transitionType;
			}
			set
			{
				this.transitionType = value;
			}
		}

		public float TransitionDuration
		{
			get
			{
				return this.transitionDuration;
			}
			set
			{
				this.transitionDuration = value;
			}
		}

		[SerializeField]
		[Tooltip("The transition type.")]
		private Accordion.Transition transitionType = Accordion.Transition.Instant;

		[SerializeField]
		[Tooltip("The transition duration.")]
		private float transitionDuration = 0.3f;

		public enum Transition
		{
			Instant,
			Tween
		}
	}
}
