﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.Networking;

namespace Nirvana
{
	public sealed class AssetBundleFileInfo
	{
		internal bool Loaded
		{
			get
			{
				return this.loaded;
			}
		}

		internal bool Loading
		{
			get
			{
				return this.wait != null;
			}
		}

		public int GetSize(string name)
		{
			int num;
			bool flag = this.sizeTable.TryGetValue(name, out num);
			int num2;
			if (flag)
			{
				num2 = num;
			}
			else
			{
				num2 = 0;
			}
			return num2;
		}

		internal WaitLoadFileInfo Load(string url)
		{
			bool flag = this.wait != null;
			WaitLoadFileInfo waitLoadFileInfo;
			if (flag)
			{
				waitLoadFileInfo = this.wait;
			}
			else
			{
				bool flag2 = this.Loaded;
				if (flag2)
				{
					waitLoadFileInfo = new WaitLoadFileInfo(this);
				}
				else
				{
					Debug.Log(string.Format("Start Load File Info: {0}", url));
					UnityWebRequest unityWebRequest = UnityWebRequest.Get(url);
					AsyncOperation asyncOperation = unityWebRequest.Send();
					this.wait = new WaitLoadFileInfo(this, unityWebRequest, asyncOperation);
					waitLoadFileInfo = this.wait;
				}
			}
			return waitLoadFileInfo;
		}

		internal void LoadComplete()
		{
			Assert.IsNotNull<WaitLoadFileInfo>(this.wait);
			this.wait = null;
		}

		internal bool Parse(string data)
		{
			this.sizeTable.Clear();
			string[] array = data.Split(new char[] { '\n', ' ' }, StringSplitOptions.RemoveEmptyEntries);
			bool flag = array.Length % 2 != 0;
			bool flag2;
			if (flag)
			{
				Debug.LogErrorFormat("[AssetBundleInfo] Parse Length Fail1: {0}", new object[] { data });
				flag2 = false;
			}
			else
			{
				for (int i = 0; i < array.Length; i += 2)
				{
					string text = array[i];
					int num;
					bool flag3 = !int.TryParse(array[i + 1], out num);
					if (flag3)
					{
						Debug.LogErrorFormat("[AssetBundleInfo] Parse Token Fail: {0}", new object[] { array[i + 1] });
						return false;
					}
					this.sizeTable.Add(text, num);
				}
				this.loaded = true;
				flag2 = true;
			}
			return flag2;
		}

		private bool loaded;

		private WaitLoadFileInfo wait;

		private Dictionary<string, int> sizeTable = new Dictionary<string, int>(StringComparer.Ordinal);
	}
}
