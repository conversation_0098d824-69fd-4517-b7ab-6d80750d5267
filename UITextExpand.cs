﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Text Expand")]
	[RequireComponent(typeof(Text))]
	public sealed class UITextExpand : BaseMeshEffect
	{
		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive() || vh.currentVertCount <= 0;
			if (!flag)
			{
				Text component = base.GetComponent<Text>();
				bool flag2 = component == null;
				if (flag2)
				{
					Debug.LogWarning("LetterSpacing: Missing Text component");
				}
				else
				{
					RectTransform rectTransform = base.transform as RectTransform;
					bool flag3 = rectTransform == null;
					if (flag3)
					{
						Debug.LogWarning("LetterSpacing: Missing RectTransform component");
					}
					else
					{
						bool flag4 = this.fitWidth > 0f;
						float num;
						if (flag4)
						{
							num = this.fitWidth - component.preferredWidth;
						}
						else
						{
							num = rectTransform.rect.width - component.preferredWidth;
						}
						bool flag5 = num < 0f;
						if (!flag5)
						{
							int num2 = 0;
							bool flag6 = this.skips != null;
							if (flag6)
							{
								num2 = this.skips.Length;
							}
							int num3 = component.text.Length - 1 - num2;
							bool flag7 = num3 <= 0;
							if (!flag7)
							{
								float num4 = num / (float)num3 / (float)component.fontSize;
								List<UIVertex> list = UIVertexListPool.Get();
								vh.GetUIVertexStream(list);
								List<UIVertex> list2 = this.Modify(list, component, num4);
								bool flag8 = list2 != null;
								if (flag8)
								{
									vh.Clear();
									vh.AddUIVertexTriangleStream(list2);
								}
								UIVertexListPool.Release(list);
							}
						}
					}
				}
			}
		}

		private List<UIVertex> Modify(List<UIVertex> verts, Text text, float spacing)
		{
			float num = 0f;
			switch (text.alignment)
			{
			case 0:
			case 3:
			case 6:
				num = 0f;
				break;
			case 1:
			case 4:
			case 7:
				num = 0.5f;
				break;
			case 2:
			case 5:
			case 8:
				num = 1f;
				break;
			}
			UIVertex[] array = new UIVertex[6];
			int[] array2 = new int[6];
			int num2 = 0;
			string[] array3 = text.text.Split(new char[] { '\n' });
			float num3 = spacing * (float)text.fontSize;
			foreach (string text2 in array3)
			{
				float num4 = (float)(text2.Length - 1) * num3 * num;
				int num5 = 0;
				for (int j = 0; j < text2.Length; j++)
				{
					for (int k = 0; k < 6; k++)
					{
						array2[k] = num2 * 6 + k;
					}
					bool flag = array2[array2.Length - 1] > verts.Count - 1;
					if (flag)
					{
						return null;
					}
					for (int l = 0; l < 6; l++)
					{
						array[l] = verts[array2[l]];
					}
					Vector3 vector = Vector3.right * (num3 * (float)num5 - num4);
					bool flag2 = !this.IsSkip(j, text2.Length);
					if (flag2)
					{
						num5++;
					}
					for (int m = 0; m < 6; m++)
					{
						UIVertex[] array4 = array;
						int num6 = m;
						array4[num6].position = array4[num6].position + vector;
					}
					for (int n = 0; n < 6; n++)
					{
						verts[array2[n]] = array[n];
					}
					num2++;
				}
				num2++;
			}
			return verts;
		}

		private bool IsSkip(int index, int length)
		{
			bool flag = this.skips == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				foreach (int num in this.skips)
				{
					bool flag3 = num >= 0;
					if (flag3)
					{
						bool flag4 = index == num;
						if (flag4)
						{
							return true;
						}
					}
					else
					{
						bool flag5 = index == length - 1 + num;
						if (flag5)
						{
							return true;
						}
					}
				}
				flag2 = false;
			}
			return flag2;
		}

		private const int N = 6;

		[SerializeField]
		public float fitWidth = -1f;

		[SerializeField]
		private int[] skips;
	}
}
