﻿using System;
using UnityEngine;
using UnityEngine.Audio;

namespace Nirvana
{
	[Serializable]
	public struct AudioSubItem
	{
		public AudioClip Clip
		{
			get
			{
				return this.audioClip;
			}
		}

		public AudioMixerGroup MixerGroup
		{
			get
			{
				return this.outputAudioMixerGroup;
			}
		}

		public float Weight
		{
			get
			{
				return this.weight;
			}
		}

		public void Reset()
		{
			this.audioClip = null;
			this.outputAudioMixerGroup = null;
			this.weight = 0f;
			this.volume = 1f;
			this.randomVolume = 0f;
			this.pitch = 1f;
			this.randomPitch = 0f;
			this.delay = 0f;
			this.randomDelay = 0f;
			this.startAt = 0f;
			this.randomStartAt = 0f;
			this.fadeInTime = 0f;
			this.fadeOutTime = 0f;
		}

		public float GetVolume()
		{
			float num = Random.Range(-1f, 1f) * this.randomVolume;
			return this.volume + num;
		}

		public float GetPitch()
		{
			float num = Random.Range(-1f, 1f) * this.randomPitch;
			return this.pitch + num;
		}

		public float GetDelay()
		{
			float num = Random.Range(-1f, 1f) * this.randomDelay;
			return this.delay + num;
		}

		public float GetStartAt()
		{
			float num = Random.Range(-1f, 1f) * this.randomStartAt;
			return this.startAt + num;
		}

		public float GetFadeInTime()
		{
			return this.fadeInTime;
		}

		public float GetFadeOutTime()
		{
			return this.fadeOutTime;
		}

		[SerializeField]
		[Tooltip("The audio to play.")]
		private AudioClip audioClip;

		[SerializeField]
		[Tooltip("The output audio mixer group, override the parent if valid.")]
		private AudioMixerGroup outputAudioMixerGroup;

		[SerializeField]
		[Tooltip("The weight of this sub-item to play in a AudioItem.")]
		private float weight;

		[SerializeField]
		[Tooltip("The volume of this audio.")]
		[Range(0f, 1f)]
		private float volume;

		[SerializeField]
		[Tooltip("The random volume add or sub on the base volume.")]
		[Range(0f, 1f)]
		private float randomVolume;

		[SerializeField]
		[Tooltip("The pitch shift for this audio.")]
		private float pitch;

		[SerializeField]
		[Tooltip("The random pitch add or sub on the base pitch.")]
		private float randomPitch;

		[SerializeField]
		[Tooltip("The delay of this audio.")]
		private float delay;

		[SerializeField]
		[Tooltip("The random delay add or sub on the base delay.")]
		private float randomDelay;

		[SerializeField]
		[Tooltip("The start at point of this audio.")]
		private float startAt;

		[SerializeField]
		[Tooltip("The random start at point add or sub on the base delay.")]
		private float randomStartAt;

		[SerializeField]
		[Tooltip("The fade in time.")]
		private float fadeInTime;

		[SerializeField]
		[Tooltip("The fade out time.")]
		private float fadeOutTime;
	}
}
