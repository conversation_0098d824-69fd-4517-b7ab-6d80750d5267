﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class RectTransformExtensions
	{
		public static Vector3 GetWorldCenter(this RectTransform transform)
		{
			transform.GetWorldCorners(RectTransformExtensions.corners);
			return (RectTransformExtensions.corners[0] + RectTransformExtensions.corners[2]) / 2f;
		}

		public static float GetWorldCenterX(this RectTransform transform)
		{
			transform.GetWorldCorners(RectTransformExtensions.corners);
			return (RectTransformExtensions.corners[0].x + RectTransformExtensions.corners[2].x) / 2f;
		}

		public static float GetWorldCenterY(this RectTransform transform)
		{
			transform.GetWorldCorners(RectTransformExtensions.corners);
			return (RectTransformExtensions.corners[0].y + RectTransformExtensions.corners[2].y) / 2f;
		}

		public static float GetWorldCenterZ(this RectTransform transform)
		{
			transform.GetWorldCorners(RectTransformExtensions.corners);
			return (RectTransformExtensions.corners[0].z + RectTransformExtensions.corners[2].z) / 2f;
		}

		private static Vector3[] corners = new Vector3[4];
	}
}
