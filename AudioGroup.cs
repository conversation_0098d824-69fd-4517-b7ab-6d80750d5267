﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	[CreateAssetMenu(fileName = "AudioGroup", menuName = "Nirvana/Audio/AudioGroup")]
	public sealed class AudioGroup : ScriptableObject
	{
		internal bool CheckPlayableAndTryToEliminate()
		{
			bool flag = this.IsPlayable();
			bool flag2;
			if (flag)
			{
				flag2 = true;
			}
			else
			{
				bool flag3 = this.eliminationMode == AudioGroup.EliminationMode.SkipCurrent;
				if (flag3)
				{
					flag2 = false;
				}
				else
				{
					bool flag4 = this.playgings.Count == 0;
					if (flag4)
					{
						flag2 = false;
					}
					else
					{
						IAudioController audioController = null;
						float num = float.MaxValue;
						foreach (IAudioController audioController2 in this.playgings)
						{
							bool flag5 = audioController2.IsPlaying && audioController2.LeftTime >= 0f;
							if (flag5)
							{
								bool flag6 = audioController2.LeftTime < num;
								if (flag6)
								{
									num = audioController2.LeftTime;
									audioController = audioController2;
								}
							}
						}
						bool flag7 = audioController != null;
						if (flag7)
						{
							audioController.Stop();
							flag2 = true;
						}
						else
						{
							flag2 = false;
						}
					}
				}
			}
			return flag2;
		}

		internal void AddPlaying(IAudioController ctrl)
		{
			this.playingCount++;
			this.lastTime = Time.realtimeSinceStartup;
			bool flag = this.eliminationMode > AudioGroup.EliminationMode.SkipCurrent;
			if (flag)
			{
				this.playgings.Add(ctrl);
			}
		}

		internal void StopPlaying(IAudioController ctrl)
		{
			this.playingCount--;
			bool flag = this.eliminationMode > AudioGroup.EliminationMode.SkipCurrent;
			if (flag)
			{
				this.playgings.Remove(ctrl);
			}
		}

		private bool IsPlayable()
		{
			bool flag = Time.realtimeSinceStartup < this.lastTime + this.interval;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = this.maxCount > 0 && this.playingCount >= this.maxCount;
				flag2 = !flag3;
			}
			return flag2;
		}

		[SerializeField]
		[Tooltip("The elimination mode of this group.")]
		[EnumLabel]
		private AudioGroup.EliminationMode eliminationMode;

		[SerializeField]
		[Tooltip("The play interval between two play.")]
		private float interval = 0f;

		[SerializeField]
		[Tooltip("The max count of this item can play, 0 mean no limit.")]
		private int maxCount = 0;

		private HashSet<IAudioController> playgings = new HashSet<IAudioController>();

		private float lastTime;

		private int playingCount;

		private enum EliminationMode
		{
			[EnumLabel("Skip Current Audio")]
			SkipCurrent,
			[EnumLabel("Stop By Left Time")]
			StopByLeftTime
		}
	}
}
