﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class NetWorkState
	{
		public static void SetNetWorkStateListener(Action<int, string> callback = null)
		{
			bool flag = callback != null;
			if (flag)
			{
				callback(NetWorkState.curState, NetWorkState.curMsg);
			}
			NetWorkState.netWorkStateReceiverListener.CompleteDelegate = callback;
			NetWorkState.InitNetWorkState();
		}

		private static void InitNetWorkState()
		{
			bool flag = NetWorkState.netWorkStateClass == null;
			if (flag)
			{
				NetWorkState.netWorkStateClass = new AndroidJavaClass("com.winunet.and.InternetState");
			}
			NetWorkState.netWorkStateClass.CallStatic("setListener", new object[] { NetWorkState.netWorkStateReceiverListener });
		}

		private static int curState = 0;

		private static string curMsg = string.Empty;

		private static AndroidJavaClass netWorkStateClass;

		private static NetWorkState.NetWorkStateReceiverListener netWorkStateReceiverListener = new NetWorkState.NetWorkStateReceiverListener();

		private class NetWorkStateReceiverListener : AndroidJavaProxy
		{
			public NetWorkStateReceiverListener()
				: base("com.winunet.and.InternetStateReceiverListener")
			{
			}

			public Action<int, string> CompleteDelegate { get; set; }

			private void onComplete(int state, string msg)
			{
				NetWorkState.curState = state;
				NetWorkState.curMsg = msg;
				SdkScheduler.PostTask(delegate
				{
					bool flag = this.CompleteDelegate != null;
					if (flag)
					{
						this.CompleteDelegate(state, msg);
					}
				});
			}
		}
	}
}
