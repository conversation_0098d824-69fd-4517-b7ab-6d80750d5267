﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class Vector3Extensions
	{
		public static Vector3 Parse(string text)
		{
			Vector3 vector;
			bool flag = !Vector3Extensions.TryParse(text, out vector);
			if (flag)
			{
				string text2 = string.Format("The string {0} can not convert to Rect.", text);
				throw new FormatException(text2);
			}
			return vector;
		}

		public static bool TryParse(string text, out Vector3 v)
		{
			bool flag = text.Length < 2 || text[0] != '(' || text[text.Length - 1] != ')';
			bool flag2;
			if (flag)
			{
				v = Vector3.zero;
				flag2 = false;
			}
			else
			{
				string[] array = text.Substring(1, text.Length - 2).Split(new char[] { ',' });
				bool flag3 = array.Length != 3;
				if (flag3)
				{
					v = Vector3.zero;
					flag2 = false;
				}
				else
				{
					float num = float.Parse(array[0]);
					float num2 = float.Parse(array[1]);
					float num3 = float.Parse(array[2]);
					v = new Vector3(num, num2, num3);
					flag2 = true;
				}
			}
			return flag2;
		}
	}
}
