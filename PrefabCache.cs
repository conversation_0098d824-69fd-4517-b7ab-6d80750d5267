﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class PrefabCache
	{
		internal PrefabCache(AssetID assetID, IDictionary<GameObject, PrefabCache> lookup)
		{
			this.assetID = assetID;
			this.lookup = lookup;
		}

		public AssetID AssetID
		{
			get
			{
				return this.assetID;
			}
		}

		public int ReferenceCount
		{
			get
			{
				return this.referenceCount;
			}
		}

		public float LastFreeTime
		{
			get
			{
				return this.lastFreeTime;
			}
		}

		public float ReleaseAfterFree
		{
			get
			{
				return this.releaseAfterFree;
			}
		}

		public bool IsSync
		{
			get
			{
				return this.isSync;
			}
			set
			{
				this.isSync = value;
			}
		}

		public string Error { get; private set; }

		internal float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				bool flag = this.poolStrategy != null;
				if (flag)
				{
					this.releaseAfterFree = this.poolStrategy.PrefabReleaseAfterFree;
				}
				else
				{
					this.releaseAfterFree = this.defaultReleaseAfterFree;
				}
			}
		}

		internal void Retain()
		{
			this.referenceCount++;
		}

		internal void Release()
		{
			this.referenceCount--;
			this.lastFreeTime = Time.time;
			bool flag = this.referenceCount < 0;
			if (flag)
			{
				Debug.LogErrorFormat("[PrefabCache] referenceCount is error {0} {1}", new object[]
				{
					this.assetID.ToString(),
					this.referenceCount
				});
			}
		}

		internal void LoadObject(AssetID assetID)
		{
			Scheduler.RunCoroutine(this.LoadObjectImpl(assetID));
		}

		internal bool HasLoaded()
		{
			return this.cachedObject != null;
		}

		internal GameObject GetObject()
		{
			return this.cachedObject;
		}

		private IEnumerator LoadObjectImpl(AssetID assetID)
		{
			WaitLoadObject waitobj = null;
			bool flag = this.isSync;
			if (flag)
			{
				waitobj = AssetManager.LoadObjectSync(assetID, typeof(GameObject));
			}
			else
			{
				waitobj = AssetManager.LoadObject(assetID, typeof(GameObject));
			}
			yield return waitobj;
			bool flag2 = !string.IsNullOrEmpty(waitobj.Error);
			if (flag2)
			{
				this.Error = waitobj.Error;
				yield break;
			}
			this.cachedObject = waitobj.GetObject() as GameObject;
			bool flag3 = this.cachedObject == null;
			if (flag3)
			{
				this.Error = string.Format("This asset: {0} is not a GameObject", assetID);
				yield break;
			}
			this.poolStrategy = this.cachedObject.GetComponent<PoolStrategy>();
			bool flag4 = this.poolStrategy != null;
			if (flag4)
			{
				this.releaseAfterFree = this.poolStrategy.InstanceReleaseAfterFree;
			}
			else
			{
				this.releaseAfterFree = this.DefaultReleaseAfterFree;
			}
			bool flag5 = this.lookup.ContainsKey(this.cachedObject);
			if (flag5)
			{
				PrefabCache.logger.LogWarning("The prefab {0} has been loaded.", new object[] { assetID });
				this.lookup[this.cachedObject] = this;
			}
			else
			{
				this.lookup.Add(this.cachedObject, this);
			}
			yield break;
		}

		private static Logger logger = LogSystem.GetLogger("PrefabCache");

		private GameObject cachedObject;

		private int referenceCount;

		private AssetID assetID;

		private IDictionary<GameObject, PrefabCache> lookup;

		private PoolStrategy poolStrategy;

		private float lastFreeTime = -1f;

		private float defaultReleaseAfterFree = 30f;

		private float releaseAfterFree;

		private bool isSync = false;
	}
}
