﻿using System;

namespace Nirvana
{
	internal sealed class NetBuffer
	{
		public NetBuffer(int capacity)
		{
			this.buffer = new byte[capacity];
			this.readPos = 0;
			this.writePos = 0;
		}

		public byte[] Buffer
		{
			get
			{
				return this.buffer;
			}
		}

		public int DataStart
		{
			get
			{
				return this.readPos;
			}
		}

		public int Payload
		{
			get
			{
				return this.writePos - this.readPos;
			}
		}

		public int Prepare(int length)
		{
			bool flag = this.buffer.Length - this.writePos < length;
			if (flag)
			{
				this.Reserve(this.Payload + length);
			}
			return this.writePos;
		}

		public bool Submit(int length)
		{
			this.writePos += length;
			bool flag = this.writePos > this.buffer.Length;
			bool flag2;
			if (flag)
			{
				this.writePos = this.buffer.Length;
				flag2 = false;
			}
			else
			{
				flag2 = true;
			}
			return flag2;
		}

		public void Consume(int length)
		{
			this.readPos += length;
			bool flag = this.readPos >= this.writePos;
			if (flag)
			{
				this.readPos = 0;
				this.writePos = 0;
			}
		}

		public void Reserve(int capacity)
		{
			bool flag = this.buffer.Length - this.readPos >= capacity;
			if (flag)
			{
				int payload = this.Payload;
				Array.Copy(this.buffer, this.readPos, this.buffer, 0, payload);
				this.readPos = 0;
				this.writePos = payload;
			}
			else
			{
				int num = Math.Min(this.buffer.Length * 2, capacity);
				byte[] array = new byte[num];
				int payload2 = this.Payload;
				Array.Copy(this.buffer, this.readPos, array, 0, payload2);
				this.buffer = array;
				this.readPos = 0;
				this.writePos = payload2;
			}
		}

		private byte[] buffer;

		private int readPos;

		private int writePos;
	}
}
