﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Nirvana
{
	public abstract class PassEvent : MonoBehaviour
	{
		protected void PassEventDown<T>(PointerEventData data, ExecuteEvents.EventFunction<T> function) where T : IEventSystemHandler
		{
			bool flag = this.passing;
			if (!flag)
			{
				PassEvent.results.Clear();
				EventSystem.current.RaycastAll(data, PassEvent.results);
				this.passing = true;
				try
				{
					GameObject gameObject = data.pointerCurrentRaycast.gameObject;
					for (int i = 0; i < PassEvent.results.Count; i++)
					{
						bool flag2 = PassEvent.results[i].gameObject != gameObject;
						if (flag2)
						{
							ExecuteEvents.Execute<T>(PassEvent.results[i].gameObject, data, function);
							break;
						}
					}
				}
				finally
				{
					this.passing = false;
				}
			}
		}

		private static List<RaycastResult> results = new List<RaycastResult>();

		private bool passing = false;
	}
}
