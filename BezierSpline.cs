﻿using System;
using System.Diagnostics;
using UnityEngine;

namespace Nirvana
{
	public sealed class BezierSpline : Spline
	{
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public override event Action ChangedEvent;

		public bool Loop
		{
			get
			{
				return this.loop;
			}
			set
			{
				this.loop = value;
				if (value)
				{
					this.modes[this.modes.Length - 1] = this.modes[0];
					this.SetControlPoint(0, this.points[0]);
				}
			}
		}

		public int ControlPointCount
		{
			get
			{
				return this.points.Length;
			}
		}

		public int CurveCount
		{
			get
			{
				return (this.points.Length - 1) / 3;
			}
		}

		public Vector3 GetControlPoint(int index)
		{
			return this.points[index];
		}

		public void SetControlPoint(int index, Vector3 point)
		{
			bool flag = index % 3 == 0;
			if (flag)
			{
				Vector3 vector = point - this.points[index];
				bool flag2 = this.loop;
				if (flag2)
				{
					bool flag3 = index == 0;
					if (flag3)
					{
						this.points[1] += vector;
						this.points[this.points.Length - 2] += vector;
						this.points[this.points.Length - 1] = point;
					}
					else
					{
						bool flag4 = index == this.points.Length - 1;
						if (flag4)
						{
							this.points[0] = point;
							this.points[1] += vector;
							this.points[index - 1] += vector;
						}
						else
						{
							this.points[index - 1] += vector;
							this.points[index + 1] += vector;
						}
					}
				}
				else
				{
					bool flag5 = index > 0;
					if (flag5)
					{
						this.points[index - 1] += vector;
					}
					bool flag6 = index + 1 < this.points.Length;
					if (flag6)
					{
						this.points[index + 1] += vector;
					}
				}
			}
			this.points[index] = point;
			this.EnforceMode(index);
			bool flag7 = this.ChangedEvent != null;
			if (flag7)
			{
				this.ChangedEvent();
			}
		}

		public BezierSpline.PointMode GetControlPointMode(int index)
		{
			return this.modes[(index + 1) / 3];
		}

		public void SetControlPointMode(int index, BezierSpline.PointMode mode)
		{
			int num = (index + 1) / 3;
			this.modes[num] = mode;
			bool flag = this.loop;
			if (flag)
			{
				bool flag2 = num == 0;
				if (flag2)
				{
					this.modes[this.modes.Length - 1] = mode;
				}
				else
				{
					bool flag3 = num == this.modes.Length - 1;
					if (flag3)
					{
						this.modes[0] = mode;
					}
				}
			}
			this.EnforceMode(index);
			bool flag4 = this.ChangedEvent != null;
			if (flag4)
			{
				this.ChangedEvent();
			}
		}

		public void AddCurve()
		{
			Vector3 vector = this.points[this.points.Length - 1];
			Vector3 normalized = (this.points[this.points.Length - 1] - this.points[this.points.Length - 2]).normalized;
			Array.Resize<Vector3>(ref this.points, this.points.Length + 3);
			vector += normalized;
			this.points[this.points.Length - 3] = vector;
			vector += normalized * 2f;
			this.points[this.points.Length - 2] = vector;
			vector += normalized * 3f;
			this.points[this.points.Length - 1] = vector;
			Array.Resize<BezierSpline.PointMode>(ref this.modes, this.modes.Length + 1);
			this.modes[this.modes.Length - 1] = this.modes[this.modes.Length - 2];
			this.EnforceMode(this.points.Length - 4);
			bool flag = this.loop;
			if (flag)
			{
				this.points[this.points.Length - 1] = this.points[0];
				this.modes[this.modes.Length - 1] = this.modes[0];
				this.EnforceMode(0);
			}
			bool flag2 = this.ChangedEvent != null;
			if (flag2)
			{
				this.ChangedEvent();
			}
		}

		public override Vector3 GetPoint(float t)
		{
			bool flag = t >= 1f;
			int num;
			if (flag)
			{
				t = 1f;
				num = this.points.Length - 4;
			}
			else
			{
				t = Mathf.Clamp01(t) * (float)this.CurveCount;
				num = (int)t;
				t -= (float)num;
				num *= 3;
			}
			Vector3 point = BezierTool.GetPoint(this.points[num], this.points[num + 1], this.points[num + 2], this.points[num + 3], t);
			return base.transform.TransformPoint(point);
		}

		public override Vector3 GetVelocity(float t)
		{
			bool flag = t >= 1f;
			int num;
			if (flag)
			{
				t = 1f;
				num = this.points.Length - 4;
			}
			else
			{
				t = Mathf.Clamp01(t) * (float)this.CurveCount;
				num = (int)t;
				t -= (float)num;
				num *= 3;
			}
			Vector3 firstDerivative = BezierTool.GetFirstDerivative(this.points[num], this.points[num + 1], this.points[num + 2], this.points[num + 3], t);
			return base.transform.TransformPoint(firstDerivative) - base.transform.position;
		}

		public override Vector3 GetDirection(float t)
		{
			return this.GetVelocity(t).normalized;
		}

		private void EnforceMode(int index)
		{
			int num = (index + 1) / 3;
			BezierSpline.PointMode pointMode = this.modes[num];
			bool flag = pointMode == BezierSpline.PointMode.Free;
			if (!flag)
			{
				bool flag2 = !this.loop;
				if (!flag2)
				{
					bool flag3 = num == 0 || num == this.modes.Length - 1;
					if (!flag3)
					{
						int num2 = num * 3;
						bool flag4 = index <= num2;
						int num3;
						int num4;
						if (flag4)
						{
							num3 = num2 - 1;
							bool flag5 = num3 < 0;
							if (flag5)
							{
								num3 = this.points.Length - 2;
							}
							num4 = num2 + 1;
							bool flag6 = num4 >= this.points.Length;
							if (flag6)
							{
								num4 = 1;
							}
						}
						else
						{
							num3 = num2 + 1;
							bool flag7 = num3 >= this.points.Length;
							if (flag7)
							{
								num3 = 1;
							}
							num4 = num2 - 1;
							bool flag8 = num4 < 0;
							if (flag8)
							{
								num4 = this.points.Length - 2;
							}
						}
						Vector3 vector = this.points[num2];
						Vector3 vector2 = vector - this.points[num3];
						bool flag9 = pointMode == BezierSpline.PointMode.Aligned;
						if (flag9)
						{
							vector2 = vector2.normalized * Vector3.Distance(vector, this.points[num4]);
						}
						this.points[num4] = vector + vector2;
					}
				}
			}
		}

		private void Reset()
		{
			this.points = new Vector3[]
			{
				new Vector3(1f, 0f, 0f),
				new Vector3(2f, 0f, 0f),
				new Vector3(3f, 0f, 0f),
				new Vector3(4f, 0f, 0f)
			};
			this.modes = new BezierSpline.PointMode[2];
			bool flag = this.ChangedEvent != null;
			if (flag)
			{
				this.ChangedEvent();
			}
		}

		[SerializeField]
		private Vector3[] points;

		[SerializeField]
		private BezierSpline.PointMode[] modes;

		[SerializeField]
		private bool loop;

		public enum PointMode
		{
			Free,
			Aligned,
			Mirrored
		}
	}
}
