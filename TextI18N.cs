﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/Text I18N")]
	[RequireComponent(typeof(Text))]
	[DisallowMultipleComponent]
	[ExecuteInEditMode]
	public sealed class TextI18N : Translatable
	{
		protected override void TranslateText()
		{
			Text component = base.GetComponent<Text>();
			bool flag = component == null;
			if (!flag)
			{
				bool flag2 = this.font == null;
				if (flag2)
				{
					this.UpdateText(component);
				}
				else
				{
					SystemLanguage language = I18N.Language;
					AssetID assetID = this.font.FindFont(language);
					bool isEmpty = assetID.IsEmpty;
					if (isEmpty)
					{
						TextI18N.logger.LogError("The text {0} can not find font for language: {1}", new object[]
						{
							base.name,
							I18N.Language
						});
						component.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
					}
					else
					{
						this.LoadFontRuntime(component, assetID, language);
					}
				}
			}
		}

		private void LoadFontRuntime(Text text, AssetID fontAsset, SystemLanguage language)
		{
			Singleton<FontPool>.Instance.Load(fontAsset, delegate(Font font)
			{
				bool flag = language != I18N.Language;
				if (flag)
				{
					Singleton<FontPool>.Instance.Free(font, false);
				}
				else
				{
					text.font = font;
					this.UpdateText(text);
				}
			});
		}

		private void UpdateText(Text text)
		{
			UIVariableBindText component = base.GetComponent<UIVariableBindText>();
			bool flag = component != null;
			if (flag)
			{
				component.Format = base.GetText();
			}
			else
			{
				text.text = base.GetText();
			}
		}

		private static Logger logger = LogSystem.GetLogger("TextI18N");

		[SerializeField]
		[Tooltip("The font for i18n support.")]
		private I18NFont font;
	}
}
