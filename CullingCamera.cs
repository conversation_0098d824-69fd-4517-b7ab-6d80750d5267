﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Render/Culling Camera")]
	[RequireComponent(typeof(Camera))]
	public sealed class CullingCamera : MonoBehaviour
	{
		private void Awake()
		{
			this.cullCamera = base.GetComponent<Camera>();
		}

		private void OnPreCull()
		{
			GeometryUtilityHelper.ExtractPlanes(this.planes, this.cullCamera);
			Singleton<CullingManager>.Instance.CullingRenderers(this.planes);
		}

		private void OnPostRender()
		{
			Singleton<CullingManager>.Instance.RestoreRenderers();
		}

		private Camera cullCamera;

		private Plane[] planes = new Plane[6];
	}
}
