﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class WaitUpdateAssetBundle : CustomYieldInstruction
	{
		internal WaitUpdateAssetBundle()
		{
		}

		internal WaitUpdateAssetBundle(AssetBundleLoadTask task)
		{
			this.task = task;
		}

		internal WaitUpdateAssetBundle(string error)
		{
			this.error = error;
		}

		public int BytesDownloaded
		{
			get
			{
				bool flag = this.task == null;
				int num;
				if (flag)
				{
					num = 0;
				}
				else
				{
					num = this.task.BytesDownloaded;
				}
				return num;
			}
		}

		public int ContentLength
		{
			get
			{
				bool flag = this.task == null;
				int num;
				if (flag)
				{
					num = 0;
				}
				else
				{
					num = this.task.ContentLength;
				}
				return num;
			}
		}

		public float Progress
		{
			get
			{
				bool flag = this.task == null;
				float num;
				if (flag)
				{
					num = 0f;
				}
				else
				{
					num = this.task.Progress;
				}
				return num;
			}
		}

		public int DownloadSpeed
		{
			get
			{
				bool flag = this.task == null;
				int num;
				if (flag)
				{
					num = 0;
				}
				else
				{
					num = this.task.DownloadSpeed;
				}
				return num;
			}
		}

		public string Error
		{
			get
			{
				bool flag = !string.IsNullOrEmpty(this.error);
				string text;
				if (flag)
				{
					text = this.error;
				}
				else
				{
					bool flag2 = this.task != null;
					if (flag2)
					{
						text = this.task.Error;
					}
					else
					{
						text = null;
					}
				}
				return text;
			}
		}

		public override bool keepWaiting
		{
			get
			{
				return this.Error == null && this.task != null && this.task.Updating;
			}
		}

		private AssetBundleLoadTask task;

		private string error;
	}
}
