﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	internal sealed class AssetBundleLoader
	{
		internal AssetBundleLoader(AssetBundleCache cache, AssetBundleDownloader downloader)
		{
			this.cache = cache;
			this.downloader = downloader;
		}

		internal string DownloadingURL { get; set; }

		internal string AssetVersion { get; set; }

		internal bool IgnoreHashCheck { get; set; }

		internal WaitUpdateAssetBundle UpdateBundle(string bundleName, Hash128 hash)
		{
			bool flag = !hash.isValid;
			WaitUpdateAssetBundle waitUpdateAssetBundle;
			if (flag)
			{
				waitUpdateAssetBundle = new WaitUpdateAssetBundle("Bundle hash is invalid.");
			}
			else
			{
				bool flag2 = this.cache.IsVersionCached(bundleName, hash);
				if (flag2)
				{
					waitUpdateAssetBundle = new WaitUpdateAssetBundle();
				}
				else
				{
					foreach (AssetBundleLoadTask assetBundleLoadTask in this.tasks)
					{
						bool flag3 = assetBundleLoadTask.AssetBundleName == bundleName && assetBundleLoadTask.Hash == hash;
						if (flag3)
						{
							return new WaitUpdateAssetBundle(assetBundleLoadTask);
						}
					}
					string remotePath = this.GetRemotePath(bundleName, hash.ToString());
					AssetBundleLoadTask assetBundleLoadTask2 = new AssetBundleLoadTask(true, this.downloader, remotePath, this.cache, bundleName, hash);
					this.tasks.AddLast(assetBundleLoadTask2);
					this.downloader.Start(assetBundleLoadTask2);
					waitUpdateAssetBundle = new WaitUpdateAssetBundle(assetBundleLoadTask2);
				}
			}
			return waitUpdateAssetBundle;
		}

		internal AssetBundle LoadLocalSync(string bundleName)
		{
			return this.cache.LoadFromCacheSync(bundleName);
		}

		internal AssetBundle LoadLocalSync(string bundleName, Hash128 hash)
		{
			return this.cache.LoadFromCacheSync(bundleName, hash, this.IgnoreHashCheck);
		}

		internal WaitLoadAssetBundle LoadLocal(string bundleName, bool sync)
		{
			WaitLoadAssetBundle waitLoadAssetBundle;
			if (sync)
			{
				AssetBundle assetBundle = this.cache.LoadFromCacheSync(bundleName);
				waitLoadAssetBundle = new WaitLoadAssetBundle(assetBundle);
			}
			else
			{
				AssetBundleCreateRequest assetBundleCreateRequest = this.cache.LoadFromCache(bundleName);
				bool flag = assetBundleCreateRequest == null;
				if (flag)
				{
					waitLoadAssetBundle = null;
				}
				else
				{
					AssetBundleLoadTask assetBundleLoadTask = new AssetBundleLoadTask(this.cache, assetBundleCreateRequest, bundleName);
					this.tasks.AddLast(assetBundleLoadTask);
					waitLoadAssetBundle = new WaitLoadAssetBundle(assetBundleLoadTask);
				}
			}
			return waitLoadAssetBundle;
		}

		internal WaitLoadAssetBundle LoadLocal(string bundleName, Hash128 hash, bool sync)
		{
			WaitLoadAssetBundle waitLoadAssetBundle;
			if (sync)
			{
				AssetBundle assetBundle = this.cache.LoadFromCacheSync(bundleName, hash, this.IgnoreHashCheck);
				waitLoadAssetBundle = new WaitLoadAssetBundle(assetBundle);
			}
			else
			{
				AssetBundleCreateRequest assetBundleCreateRequest = this.cache.LoadFromCache(bundleName, hash, this.IgnoreHashCheck);
				bool flag = assetBundleCreateRequest == null;
				if (flag)
				{
					waitLoadAssetBundle = null;
				}
				else
				{
					AssetBundleLoadTask assetBundleLoadTask = new AssetBundleLoadTask(this.cache, assetBundleCreateRequest, bundleName, hash);
					this.tasks.AddLast(assetBundleLoadTask);
					waitLoadAssetBundle = new WaitLoadAssetBundle(assetBundleLoadTask);
				}
			}
			return waitLoadAssetBundle;
		}

		internal WaitLoadAssetBundle Load(string bundleName, Hash128 hash, bool sync)
		{
			bool ignoreHashCheck = this.IgnoreHashCheck;
			WaitLoadAssetBundle waitLoadAssetBundle;
			if (ignoreHashCheck)
			{
				waitLoadAssetBundle = this.LoadLocal(bundleName, hash, sync);
			}
			else
			{
				bool flag = this.cache.IsVersionCached(bundleName, hash);
				if (flag)
				{
					waitLoadAssetBundle = this.LoadLocal(bundleName, hash, sync);
				}
				else
				{
					foreach (AssetBundleLoadTask assetBundleLoadTask in this.tasks)
					{
						bool flag2 = assetBundleLoadTask.AssetBundleName == bundleName && assetBundleLoadTask.Hash == hash;
						if (flag2)
						{
							assetBundleLoadTask.UpdateOnly = false;
							return new WaitLoadAssetBundle(assetBundleLoadTask);
						}
					}
					string remotePath = this.GetRemotePath(bundleName, hash.ToString());
					AssetBundleLoadTask assetBundleLoadTask2 = new AssetBundleLoadTask(false, this.downloader, remotePath, this.cache, bundleName, hash);
					this.tasks.AddLast(assetBundleLoadTask2);
					this.downloader.Start(assetBundleLoadTask2);
					waitLoadAssetBundle = new WaitLoadAssetBundle(assetBundleLoadTask2);
				}
			}
			return waitLoadAssetBundle;
		}

		internal WaitLoadAssetBundle LoadRemote(string bundleName)
		{
			foreach (AssetBundleLoadTask assetBundleLoadTask in this.tasks)
			{
				bool flag = string.Equals(assetBundleLoadTask.AssetBundleName, bundleName);
				if (flag)
				{
					assetBundleLoadTask.UpdateOnly = false;
					return new WaitLoadAssetBundle(assetBundleLoadTask);
				}
			}
			string remotePath = this.GetRemotePath(bundleName, this.AssetVersion);
			AssetBundleLoadTask assetBundleLoadTask2 = new AssetBundleLoadTask(false, this.downloader, remotePath, this.cache, bundleName);
			this.tasks.AddLast(assetBundleLoadTask2);
			this.downloader.Start(assetBundleLoadTask2);
			return new WaitLoadAssetBundle(assetBundleLoadTask2);
		}

		internal bool IsLocalCached(string bundleName, Hash128 hash)
		{
			return this.cache.IsVersionCached(bundleName, hash);
		}

		internal void Dispose()
		{
			foreach (AssetBundleLoadTask assetBundleLoadTask in this.tasks)
			{
				bool updating = assetBundleLoadTask.Updating;
				if (updating)
				{
					assetBundleLoadTask.UpdateOnly = true;
				}
			}
		}

		internal void Update()
		{
			LinkedListNode<AssetBundleLoadTask> next;
			for (LinkedListNode<AssetBundleLoadTask> linkedListNode = this.tasks.First; linkedListNode != null; linkedListNode = next)
			{
				next = linkedListNode.Next;
				AssetBundleLoadTask value = linkedListNode.Value;
				bool flag = !value.Update();
				if (flag)
				{
					this.tasks.Remove(linkedListNode);
				}
			}
			AssetBundleLoadTask.ClearWriteCountLimit();
		}

		private string GetRemotePath(string bundleName, string variant)
		{
			bool flag = string.IsNullOrEmpty(this.DownloadingURL);
			string text;
			if (flag)
			{
				text = string.Empty;
			}
			else
			{
				text = string.Format("{0}/{1}?v={2}", this.DownloadingURL, bundleName, variant);
			}
			return text;
		}

		private AssetBundleCache cache;

		private AssetBundleDownloader downloader;

		private LinkedList<AssetBundleLoadTask> tasks = new LinkedList<AssetBundleLoadTask>();
	}
}
