﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	internal sealed class LogDestination
	{
		internal void Log(LogItem item)
		{
			foreach (ILogAppender logAppender in this.appenders)
			{
				logAppender.AppendLog(item);
			}
			bool flag = this.appenders.Count == 0;
			if (flag)
			{
				switch (item.Severity)
				{
				case LogSeverity.Warning:
					Debug.LogWarning(item.Message, item.Context);
					goto IL_00AE;
				case LogSeverity.Error:
					Debug.LogError(item.Message, item.Context);
					goto IL_00AE;
				}
				Debug.Log(item.Message, item.Context);
				IL_00AE:;
			}
		}

		internal void AddAppender(ILogAppender appender)
		{
			this.appenders.Add(appender);
		}

		internal bool RemoveAppender(ILogAppender appender)
		{
			return this.appenders.Remove(appender);
		}

		internal void ClearAppenders()
		{
			this.appenders.Clear();
		}

		private List<ILogAppender> appenders = new List<ILogAppender>();
	}
}
