﻿using System;
using UnityEngine;

namespace Nirvana
{
	internal sealed class WaitLoadAssetBundle : CustomYieldInstruction
	{
		internal WaitLoadAssetBundle(AssetBundleLoadTask task)
		{
			this.task = task;
		}

		internal WaitLoadAssetBundle(AssetBundle assetBundle)
		{
			this.assetBundle = assetBundle;
		}

		public override bool keepWaiting
		{
			get
			{
				return this.Error == null && this.AssetBundle == null;
			}
		}

		internal string Error
		{
			get
			{
				bool flag = this.task != null;
				string text;
				if (flag)
				{
					text = this.task.Error;
				}
				else
				{
					text = null;
				}
				return text;
			}
		}

		internal AssetBundle AssetBundle
		{
			get
			{
				bool flag = this.assetBundle != null;
				AssetBundle assetBundle;
				if (flag)
				{
					assetBundle = this.assetBundle;
				}
				else
				{
					assetBundle = this.task.AssetBundle;
				}
				return assetBundle;
			}
		}

		private AssetBundleLoadTask task;

		private AssetBundle assetBundle;
	}
}
