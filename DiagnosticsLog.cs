﻿using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Nirvana
{
	[Serializable]
	internal sealed class DiagnosticsLog : IDiagnosticsTab
	{
		public string TabName
		{
			get
			{
				return "Log";
			}
		}

		internal bool HasError
		{
			get
			{
				return this.hasError;
			}
			set
			{
				this.hasError = value;
			}
		}

		public void DrawTab()
		{
			this.DrawToolbar();
			GUILayout.Space(5f);
			this.scrollPosition = GUILayout.BeginScrollView(this.scrollPosition, GUI.skin.textArea);
			int num = this.filteredList.Count - this.showLimit;
			bool flag = num < 0;
			if (flag)
			{
				num = 0;
			}
			for (int i = num; i < this.filteredList.Count; i++)
			{
				DiagnosticsLog.Log log = this.filteredList[i];
				this.DrawLog(log, i);
			}
			GUILayout.FlexibleSpace();
			GUILayout.EndScrollView();
			GUILayout.Space(10f);
		}

		internal void Initialize()
		{
			this.errorIcon = TextureMaker.Dot(64, Color.red, Color.clear);
			this.warningIcon = TextureMaker.Dot(64, Color.yellow, Color.clear);
			this.infoIcon = TextureMaker.Dot(64, Color.cyan, Color.clear);
			this.filteredList = this.logsList;
			Application.logMessageReceived += delegate(string condition, string stackTrace, LogType type)
			{
				this.AddLog(new DiagnosticsLog.Log
				{
					Type = type,
					Time = DateTime.Now,
					Message = condition,
					StackTrace = stackTrace
				});
				bool flag = type == null || 4 == type;
				if (flag)
				{
					this.hasError = true;
				}
			};
		}

		private void DrawToolbar()
		{
			GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
			bool flag = GUILayout.Button("Clear", DiagnosticsSkin.BenchButton, new GUILayoutOption[] { GUILayout.Width(60f) });
			if (flag)
			{
				this.logsList.Clear();
				this.filteredList.Clear();
			}
			Color color = GUI.color;
			string text = GUILayout.TextField(this.filter, new GUILayoutOption[] { GUILayout.Height(20f) });
			bool flag2 = text != this.filter;
			if (flag2)
			{
				this.filter = text;
				this.UpdateFilter();
			}
			GUI.color = new Color(0.75f, 0.5f, 0.5f, 1f);
			bool flag3 = GUILayout.Toggle(this.showError, "Error", DiagnosticsSkin.BenchToggle, new GUILayoutOption[] { GUILayout.Width(80f) });
			bool flag4 = flag3 != this.showError;
			if (flag4)
			{
				this.showError = flag3;
				this.UpdateFilter();
			}
			GUI.color = new Color(0.95f, 0.95f, 0.3f, 1f);
			bool flag5 = GUILayout.Toggle(this.showWarning, "Warning", DiagnosticsSkin.BenchToggle, new GUILayoutOption[] { GUILayout.Width(80f) });
			bool flag6 = flag5 != this.showWarning;
			if (flag6)
			{
				this.showWarning = flag5;
				this.UpdateFilter();
			}
			GUI.color = new Color(0.5f, 0.75f, 0.5f, 1f);
			bool flag7 = GUILayout.Toggle(this.showInfo, "Debug", DiagnosticsSkin.BenchToggle, new GUILayoutOption[] { GUILayout.Width(80f) });
			bool flag8 = flag7 != this.showInfo;
			if (flag8)
			{
				this.showInfo = flag7;
				this.UpdateFilter();
			}
			GUI.color = color;
			GUILayout.EndHorizontal();
			bool flag9 = this.filtered;
			if (flag9)
			{
				GUILayout.Label("Find " + this.filteredList.Count.ToString() + " matching logs", new GUILayoutOption[] { GUILayout.Height(20f) });
			}
			else
			{
				GUILayout.Label("Total " + this.logsList.Count.ToString() + " logs", new GUILayoutOption[] { GUILayout.Height(20f) });
			}
		}

		private void UpdateFilter()
		{
			bool flag = string.IsNullOrEmpty(this.filter) && this.showError && this.showWarning && this.showInfo;
			if (flag)
			{
				this.filteredList = this.logsList;
				this.filtered = false;
			}
			else
			{
				this.filteredList = this.logsList.Where((DiagnosticsLog.Log log) => log.Message.ToLower().Contains(this.filter.ToLower()) && ((this.showError && (log.Type == null || log.Type == 4)) || (this.showWarning && log.Type == 2) || (this.showInfo && (log.Type == 3 || log.Type == 1)))).ToList<DiagnosticsLog.Log>();
				this.filtered = true;
			}
		}

		private void DrawLog(DiagnosticsLog.Log log, int index)
		{
			bool flag = index % 2 == 0;
			GUIStyle guistyle;
			if (flag)
			{
				guistyle = DiagnosticsSkin.LogItemEvenLine;
			}
			else
			{
				guistyle = DiagnosticsSkin.LogItemOddLine;
			}
			GUILayout.BeginHorizontal(guistyle, Array.Empty<GUILayoutOption>());
			Color color = GUI.color;
			switch (log.Type)
			{
			case 0:
			case 4:
				GUILayout.Label(this.errorIcon, DiagnosticsSkin.LogIcon, Array.Empty<GUILayoutOption>());
				GUI.color = Color.red;
				break;
			case 1:
			case 3:
				GUILayout.Label(this.infoIcon, DiagnosticsSkin.LogIcon, Array.Empty<GUILayoutOption>());
				GUI.color = Color.white;
				break;
			case 2:
				GUILayout.Label(this.warningIcon, DiagnosticsSkin.LogIcon, Array.Empty<GUILayoutOption>());
				GUI.color = Color.yellow;
				break;
			}
			GUILayout.Label(log.Time.ToString() + " | " + log.Message, Array.Empty<GUILayoutOption>());
			GUILayout.Label(log.StackTrace, Array.Empty<GUILayoutOption>());
			GUI.color = color;
			GUILayout.EndHorizontal();
		}

		private void AddLog(DiagnosticsLog.Log log)
		{
			bool flag = this.logsList.Count > this.memoryLimit + 1;
			if (flag)
			{
				this.logsList.RemoveRange(0, this.logsList.Count - this.memoryLimit + 1);
			}
			this.logsList.Add(log);
		}

		[SerializeField]
		[Tooltip("Whether show info log.")]
		private bool showInfo = true;

		[SerializeField]
		[Tooltip("Whether show warning log.")]
		private bool showWarning = true;

		[SerializeField]
		[Tooltip("Whether show error log.")]
		private bool showError = true;

		[SerializeField]
		[Tooltip("The filter for log.")]
		private string filter = string.Empty;

		[SerializeField]
		[Tooltip("How many log entries to store in memory.")]
		private int memoryLimit = 2000;

		[SerializeField]
		[Tooltip("How many log entries to show in panel.")]
		private int showLimit = 200;

		private Vector2 scrollPosition = new Vector2(0f, 0f);

		private List<DiagnosticsLog.Log> logsList = new List<DiagnosticsLog.Log>();

		private List<DiagnosticsLog.Log> filteredList;

		private bool filtered = false;

		private bool hasError = false;

		private Texture2D errorIcon;

		private Texture2D warningIcon;

		private Texture2D infoIcon;

		private class Log
		{
			public LogType Type { get; set; }

			public string Message { get; set; }

			public string StackTrace { get; set; }

			public DateTime Time { get; set; }
		}
	}
}
