﻿using System;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	public static class GameObjectExtensions
	{
		public static Component GetOrAddComponent(this GameObject obj, Type type)
		{
			Component component = obj.GetComponent(type);
			bool flag = component == null;
			if (flag)
			{
				component = obj.gameObject.AddComponent(type);
			}
			return component;
		}

		public static T GetOrAddComponent<T>(this GameObject obj) where T : Component
		{
			T t = obj.GetComponent<T>();
			bool flag = t == null;
			if (flag)
			{
				t = obj.gameObject.AddComponent<T>();
			}
			return t;
		}

		public static bool HasComponent(this GameObject obj, Type type)
		{
			return obj.GetComponent(type) != null;
		}

		public static bool HasComponent<T>(this GameObject obj) where T : Component
		{
			return obj.GetComponent<T>() != null;
		}

		public static void SetLayerRecursively(this GameObject obj, int layer)
		{
			obj.layer = layer;
			foreach (object obj2 in obj.transform)
			{
				Transform transform = (Transform)obj2;
				transform.gameObject.SetLayerRecursively(layer);
			}
		}

		public static T GetComponentInParentHard<T>(this GameObject obj) where T : Component
		{
			Assert.IsNotNull<GameObject>(obj);
			Transform transform = obj.transform;
			while (transform != null)
			{
				T component = transform.GetComponent<T>();
				bool flag = component != null;
				if (flag)
				{
					return component;
				}
				transform = transform.parent;
			}
			return default(T);
		}
	}
}
