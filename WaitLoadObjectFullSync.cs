﻿using System;
using UnityEngine;

namespace Nirvana
{
	internal sealed class WaitLoadObjectFullSync : WaitLoadObject
	{
		internal WaitLoadObjectFullSync(AssetBundleManager assetBundleManager, string assetBundleName, string assetName, Type assetType)
		{
			this.AssetBundleManager = assetBundleManager;
			this.assetBundleName = assetBundleName;
			this.assetName = assetName;
			this.assetType = assetType;
		}

		public override bool keepWaiting
		{
			get
			{
				return base.Error == null && this.assetObj == null;
			}
		}

		internal AssetBundleManager AssetBundleManager { get; private set; }

		private string AssetBundleName
		{
			get
			{
				return this.assetBundleName;
			}
		}

		public override Object GetObject()
		{
			return this.assetObj;
		}

		internal override bool Update()
		{
			AssetBundleItem assetBundle = this.AssetBundleManager.GetAssetBundle(this.assetBundleName);
			bool flag = assetBundle == null;
			bool flag2;
			if (flag)
			{
				flag2 = true;
			}
			else
			{
				bool flag3 = assetBundle.Error != null;
				if (flag3)
				{
					base.Error = assetBundle.Error;
					flag2 = false;
				}
				else
				{
					this.assetObj = assetBundle.AssetBundle.LoadAsset(this.assetName, this.assetType);
					flag2 = false;
				}
			}
			return flag2;
		}

		private string assetBundleName;

		private string assetName;

		private Type assetType;

		private Object assetObj;
	}
}
