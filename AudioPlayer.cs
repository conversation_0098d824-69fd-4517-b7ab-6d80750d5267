﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class AudioPlayer
	{
		public static void Play(string uri, Action complete = null)
		{
			AudioPlayer.audioPlayerListener.CompleteDelegate = complete;
			AudioPlayer.GetAudioPlayer().CallStatic("play", new object[] { uri });
		}

		public static void Stop()
		{
			AudioPlayer.GetAudioPlayer().CallStatic("stop", new object[0]);
		}

		private static AndroidJavaClass GetAudioPlayer()
		{
			bool flag = AudioPlayer.audioPlayerClass == null;
			if (flag)
			{
				AudioPlayer.audioPlayerClass = new AndroidJavaClass("com.winunet.and.SoundPlayer");
				AudioPlayer.audioPlayerClass.CallStatic("setListener", new object[] { AudioPlayer.audioPlayerListener });
			}
			return AudioPlayer.audioPlayerClass;
		}

		private static AndroidJavaClass audioPlayerClass;

		private static AudioPlayer.AudioPlayerListener audioPlayerListener = new AudioPlayer.AudioPlayerListener();

		private class AudioPlayerListener : AndroidJavaProxy
		{
			public AudioPlayerListener()
				: base("com.winunet.and.SoundPlayerListener")
			{
			}

			public Action CompleteDelegate { get; set; }

			private void onComplete()
			{
				SdkScheduler.PostTask(delegate
				{
					bool flag = this.CompleteDelegate != null;
					if (flag)
					{
						this.CompleteDelegate();
					}
				});
			}
		}
	}
}
