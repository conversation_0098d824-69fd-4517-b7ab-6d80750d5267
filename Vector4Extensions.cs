﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class Vector4Extensions
	{
		public static Vector4 Parse(string text)
		{
			Vector4 vector;
			bool flag = !Vector4Extensions.TryParse(text, out vector);
			if (flag)
			{
				string text2 = string.Format("The string {0} can not convert to Rect.", text);
				throw new FormatException(text2);
			}
			return vector;
		}

		public static bool TryParse(string text, out Vector4 v)
		{
			bool flag = text.Length < 2 || text[0] != '(' || text[text.Length - 1] != ')';
			bool flag2;
			if (flag)
			{
				v = Vector4.zero;
				flag2 = false;
			}
			else
			{
				string[] array = text.Substring(1, text.Length - 2).Split(new char[] { ',' });
				bool flag3 = array.Length != 4;
				if (flag3)
				{
					v = Vector4.zero;
					flag2 = false;
				}
				else
				{
					float num = float.Parse(array[0]);
					float num2 = float.Parse(array[1]);
					float num3 = float.Parse(array[2]);
					float num4 = float.Parse(array[3]);
					v = new Vector4(num, num2, num3, num4);
					flag2 = true;
				}
			}
			return flag2;
		}
	}
}
