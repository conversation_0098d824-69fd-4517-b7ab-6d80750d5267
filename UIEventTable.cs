﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/UI Event Table")]
	public sealed class UIEventTable : MonoBehaviour
	{
		private Dictionary<string, Signal> EventTable
		{
			get
			{
				bool flag = this.eventTable == null;
				if (flag)
				{
					this.eventTable = new Dictionary<string, Signal>(StringComparer.Ordinal);
					foreach (string text in this.events)
					{
						bool flag2 = !this.eventTable.ContainsKey(text);
						if (flag2)
						{
							this.eventTable.Add(text, new Signal());
						}
					}
					this.events = null;
				}
				return this.eventTable;
			}
		}

		public SignalHandle ListenEvent(string eventName, SignalDelegate callback)
		{
			Signal signal;
			bool flag = !this.EventTable.TryGetValue(eventName, out signal);
			SignalHandle signalHandle;
			if (flag)
			{
				UIEventTable.logger.LogWarning("{0} is trying to listen event {1}, but it does not existed.", new object[] { base.name, eventName });
				signalHandle = new SignalHandle(null, null);
			}
			else
			{
				signalHandle = signal.Add(callback);
			}
			return signalHandle;
		}

		public void ClearEvent(string eventName)
		{
			Signal signal;
			bool flag = !this.EventTable.TryGetValue(eventName, out signal);
			if (flag)
			{
				UIEventTable.logger.LogWarning("{0} is trying to clear event {1}, but it does not existed.", new object[] { base.name, eventName });
			}
			else
			{
				bool flag2 = signal != null;
				if (flag2)
				{
					signal.Clear();
				}
			}
		}

		public void ClearAllEvents()
		{
			foreach (KeyValuePair<string, Signal> keyValuePair in this.EventTable)
			{
				keyValuePair.Value.Clear();
			}
		}

		internal Signal GetEventSignal(string eventName)
		{
			bool flag = string.IsNullOrEmpty(eventName);
			Signal signal;
			if (flag)
			{
				signal = null;
			}
			else
			{
				Signal signal2;
				bool flag2 = !this.EventTable.TryGetValue(eventName, out signal2);
				if (flag2)
				{
					signal = null;
				}
				else
				{
					signal = signal2;
				}
			}
			return signal;
		}

		private static Logger logger = LogSystem.GetLogger("UIEventTable");

		[SerializeField]
		[Tooltip("The event list.")]
		private string[] events;

		private Dictionary<string, Signal> eventTable;

		public delegate void EventDelegate(params object[] args);
	}
}
