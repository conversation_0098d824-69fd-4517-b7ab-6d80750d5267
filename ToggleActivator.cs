﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/Toggle Activator")]
	[RequireComponent(typeof(Toggle))]
	public sealed class ToggleActivator : MonoBehaviour
	{
		private void Awake()
		{
			this.toggle = base.GetComponent<Toggle>();
			Assert.IsNotNull<Toggle>(this.toggle);
		}

		private void Start()
		{
			this.OnToggleChanged(this.toggle.isOn);
			this.toggle.onValueChanged.AddListener(new UnityAction<bool>(this.OnToggleChanged));
		}

		private void OnEnable()
		{
			this.OnToggleChanged(this.toggle.isOn);
		}

		private void OnToggleChanged(bool isOn)
		{
			foreach (GameObject gameObject in this.actives)
			{
				this.Transite(gameObject, isOn);
			}
			foreach (GameObject gameObject2 in this.deactives)
			{
				this.Transite(gameObject2, !isOn);
			}
			if (isOn)
			{
				foreach (GameObject gameObject3 in this.activesOnly)
				{
					this.Transite(gameObject3, true);
				}
			}
			else
			{
				foreach (GameObject gameObject4 in this.deactivesOnly)
				{
					this.Transite(gameObject4, false);
				}
			}
		}

		private void Transite(GameObject go, bool isOn)
		{
			bool flag = this.transitionMode == ToggleActivator.TransitionModeEnum.Instant;
			if (flag)
			{
				go.SetActive(isOn);
			}
			else
			{
				CanvasGroup component = go.GetComponent<CanvasGroup>();
				bool flag2 = component != null;
				if (flag2)
				{
					go.SetActive(true);
					bool activeInHierarchy = go.activeInHierarchy;
					if (activeInHierarchy)
					{
						if (isOn)
						{
							base.StartCoroutine(this.TransiteFade(component, 1f, true));
						}
						else
						{
							base.StartCoroutine(this.TransiteFade(component, 0f, false));
						}
					}
					else
					{
						go.SetActive(isOn);
					}
				}
				else
				{
					go.SetActive(isOn);
				}
			}
		}

		private IEnumerator TransiteFade(CanvasGroup group, float alphaTarget, bool activeTarget)
		{
			float leftTime = this.transitionTime;
			float alphaStart = group.alpha;
			while (leftTime > 0f)
			{
				yield return null;
				leftTime -= Time.deltaTime;
				float alpha = Mathf.Lerp(alphaStart, alphaTarget, 1f - leftTime / this.transitionTime);
				group.alpha = alpha;
			}
			group.gameObject.SetActive(activeTarget);
			yield break;
		}

		[SerializeField]
		private GameObject[] actives;

		[SerializeField]
		private GameObject[] deactives;

		[SerializeField]
		private GameObject[] activesOnly;

		[SerializeField]
		private GameObject[] deactivesOnly;

		[SerializeField]
		private ToggleActivator.TransitionModeEnum transitionMode = ToggleActivator.TransitionModeEnum.Instant;

		[SerializeField]
		private float transitionTime = 0.1f;

		private Toggle toggle;

		public enum TransitionModeEnum
		{
			Instant,
			Fade
		}
	}
}
