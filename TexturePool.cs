﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class TexturePool : Singleton<TexturePool>
	{
		public TexturePool()
		{
			this.sweepChecker = delegate(AssetID assetID, TextureCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						bool flag4 = this.unloadedCountInTime < 1 && Time.time > cache.LastFreeTime + cache.ReleaseAfterFree;
						if (flag4)
						{
							this.unloadedCountInTime++;
							Texture @object = cache.GetObject();
							bool flag5 = @object != null;
							if (flag5)
							{
								this.lookup.Remove(@object);
								Resources.UnloadAsset(@object);
								AssetManager.UnloadAsseBundle(assetID.BundleName);
							}
							flag2 = true;
						}
						else
						{
							flag2 = false;
						}
					}
				}
				return flag2;
			};
			Scheduler.AddFrameListener(new Action(this.Update));
		}

		public float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				foreach (KeyValuePair<AssetID, TextureCache> keyValuePair in this.caches)
				{
					keyValuePair.Value.DefaultReleaseAfterFree = value;
				}
			}
		}

		public void SetMaxLoadingCount(int value)
		{
			this.maxLoadingCount = value;
		}

		private void Update()
		{
			this.QueueLoadTexture();
			bool flag = Time.time - this.lastCheckSweepTime >= 1f;
			if (flag)
			{
				this.lastCheckSweepTime = Time.time;
				this.unloadedCountInTime = 0;
				this.caches.RemoveAll(this.sweepChecker);
			}
		}

		private void QueueLoadTexture()
		{
			int num = this.maxLoadingCount + this.loadQueue.Count / 5;
			while (this.loadQueue.Count > 0 && num - this.loadingCount > 0)
			{
				this.loadingCount++;
				TextureLoadItem textureLoadItem = this.loadQueue.Dequeue();
				Scheduler.RunCoroutine(this.LoadAsyncImplInQueueLoad(textureLoadItem.assetId, textureLoadItem.complete));
			}
		}

		public void Free(Texture texture, bool destroy = false)
		{
			bool flag = texture == null;
			if (flag)
			{
				TexturePool.logger.LogError("Try to free a null Texture.");
			}
			else
			{
				TextureCache textureCache;
				bool flag2 = !this.lookup.TryGetValue(texture, out textureCache);
				if (flag2)
				{
					TexturePool.logger.LogWarning("Try to free an instance {0} not allocated by this pool.", new object[] { texture.name });
				}
				else
				{
					textureCache.Release();
					bool flag3 = destroy && textureCache.ReferenceCount == 0;
					if (flag3)
					{
						AssetID assetID = textureCache.AssetID;
						Texture @object = textureCache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							Resources.UnloadAsset(@object);
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						this.caches.Remove(assetID);
					}
				}
			}
		}

		public void ClearAllUnused()
		{
			this.caches.RemoveAll(delegate(AssetID assetID, TextureCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						Texture @object = cache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							Resources.UnloadAsset(@object);
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						flag2 = true;
					}
				}
				return flag2;
			});
		}

		public void Clear()
		{
			this.caches.Clear();
			this.lookup.Clear();
		}

		public void Load(AssetID assetID, Action<Texture> complete, bool isSync = false)
		{
			bool flag = !assetID.AssetName.EndsWith(".png") && !assetID.AssetName.EndsWith(".jpg");
			if (flag)
			{
				assetID.AssetName += ".png";
			}
			assetID.AssetName = assetID.AssetName.ToLower();
			if (isSync)
			{
				Scheduler.RunCoroutine(this.LoadSyncImpl(assetID, complete));
			}
			else
			{
				bool flag2 = !AssetManager.IsVersionCached(assetID.BundleName);
				if (flag2)
				{
					Scheduler.RunCoroutine(this.LoadAsyncImpl(assetID, complete));
				}
				else
				{
					TextureLoadItem textureLoadItem = new TextureLoadItem(assetID, complete);
					this.loadQueue.Enqueue(textureLoadItem);
				}
			}
		}

		private IEnumerator LoadAsyncImplInQueueLoad(AssetID assetID, Action<Texture> complete)
		{
			WaitLoadTexture waitLoad = this.InternalLoad(assetID, false);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			this.loadingCount--;
			bool flag = this.loadingCount < 0;
			if (flag)
			{
				Debug.LogError("[TexturePool] loadingCount is occur error " + this.loadingCount.ToString());
			}
			bool flag2 = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag2)
			{
				TexturePool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private IEnumerator LoadAsyncImpl(AssetID assetID, Action<Texture> complete)
		{
			WaitLoadTexture waitLoad = this.InternalLoad(assetID, false);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			bool flag = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag)
			{
				TexturePool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private IEnumerator LoadSyncImpl(AssetID assetID, Action<Texture> complete)
		{
			WaitLoadTexture waitLoad = this.InternalLoad(assetID, true);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			bool flag = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag)
			{
				TexturePool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private WaitLoadTexture InternalLoad(AssetID assetID, bool isSync)
		{
			TextureCache textureCache;
			bool flag = this.caches.TryGetValue(assetID, out textureCache);
			WaitLoadTexture waitLoadTexture;
			if (flag)
			{
				textureCache.Retain();
				waitLoadTexture = new WaitLoadTexture(textureCache);
			}
			else
			{
				textureCache = new TextureCache(assetID, this.lookup);
				textureCache.IsSync = isSync;
				textureCache.LoadObject(assetID);
				textureCache.Retain();
				this.caches.Add(assetID, textureCache);
				waitLoadTexture = new WaitLoadTexture(textureCache);
			}
			return waitLoadTexture;
		}

		private static Logger logger = LogSystem.GetLogger("TexturePool");

		private Dictionary<AssetID, TextureCache> caches = new Dictionary<AssetID, TextureCache>();

		private Dictionary<Texture, TextureCache> lookup = new Dictionary<Texture, TextureCache>();

		private float defaultReleaseAfterFree = 15f;

		private Func<AssetID, TextureCache, bool> sweepChecker;

		private int unloadedCountInTime = 0;

		private float lastCheckSweepTime = 0f;

		private Queue<TextureLoadItem> loadQueue = new Queue<TextureLoadItem>();

		private int maxLoadingCount = 3;

		private int loadingCount = 0;
	}
}
