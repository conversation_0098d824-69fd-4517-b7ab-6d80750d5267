﻿using System;
using UnityEngine;

namespace Nirvana
{
	internal struct UIEffectMaterialKey : IEquatable<UIEffectMaterialKey>
	{
		public bool Equals(UIEffectMaterialKey o)
		{
			bool flag = this.BlurDistance != o.BlurDistance;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = this.OverlayTexture != o.OverlayTexture;
				if (flag3)
				{
					flag2 = false;
				}
				else
				{
					bool flag4 = this.OverlayColorMode != o.OverlayColorMode;
					if (flag4)
					{
						flag2 = false;
					}
					else
					{
						bool flag5 = this.OverlaySpeed != o.OverlaySpeed;
						if (flag5)
						{
							flag2 = false;
						}
						else
						{
							bool flag6 = this.EnableInnerBevel != o.EnableInnerBevel;
							if (flag6)
							{
								flag2 = false;
							}
							else
							{
								bool flag7 = this.HighlightColor != o.HighlightColor;
								if (flag7)
								{
									flag2 = false;
								}
								else
								{
									bool flag8 = this.HighlightColorMode != o.HighlightColorMode;
									if (flag8)
									{
										flag2 = false;
									}
									else
									{
										bool flag9 = this.ShadowColor != o.ShadowColor;
										if (flag9)
										{
											flag2 = false;
										}
										else
										{
											bool flag10 = this.ShadowColorMode != o.ShadowColorMode;
											if (flag10)
											{
												flag2 = false;
											}
											else
											{
												bool flag11 = this.HighlightOffset != o.HighlightOffset;
												if (flag11)
												{
													flag2 = false;
												}
												else
												{
													bool flag12 = this.GrayScale != o.GrayScale;
													flag2 = !flag12;
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
			return flag2;
		}

		public override int GetHashCode()
		{
			int num = this.BlurDistance.GetHashCode();
			bool flag = this.OverlayTexture != null;
			if (flag)
			{
				num = (397 * num) ^ this.OverlayTexture.GetHashCode();
			}
			num = (397 * num) ^ this.OverlayColorMode.GetHashCode();
			num = (397 * num) ^ this.OverlaySpeed.GetHashCode();
			num = (397 * num) ^ this.EnableInnerBevel.GetHashCode();
			num = (397 * num) ^ this.HighlightColor.GetHashCode();
			num = (397 * num) ^ this.HighlightColorMode.GetHashCode();
			num = (397 * num) ^ this.ShadowColor.GetHashCode();
			num = (397 * num) ^ this.ShadowColorMode.GetHashCode();
			num = (397 * num) ^ this.HighlightOffset.GetHashCode();
			return (397 * num) ^ this.GrayScale.GetHashCode();
		}

		internal Material CreateMaterial()
		{
			bool flag = this.BlurDistance == 0f && this.OverlayTexture == null && !this.EnableInnerBevel && this.GrayScale == 0;
			Material material;
			if (flag)
			{
				material = null;
			}
			else
			{
				Shader shader = Shader.Find("UI/Effect");
				bool flag2 = shader == null;
				if (flag2)
				{
					Debug.LogError("Can not found shader: 'UI/Effect'");
					material = null;
				}
				else
				{
					Material material2 = new Material(shader);
					bool flag3 = this.BlurDistance > 0f;
					if (flag3)
					{
						material2.EnableKeyword("UIEFFECT_BLUR");
						material2.SetFloat("_BlurDistance", this.BlurDistance);
					}
					bool flag4 = this.OverlayTexture != null;
					if (flag4)
					{
						bool flag5 = this.OverlaySpeed > 0f;
						if (flag5)
						{
							material2.EnableKeyword("UIEFFECT_OVERLAY_ANIMATION");
							material2.SetFloat("_OverlaySpeed", this.OverlaySpeed);
						}
						else
						{
							material2.EnableKeyword("UIEFFECT_OVERLAY");
						}
						material2.SetTexture("_OverlayTex", this.OverlayTexture);
						material2.SetInt("_OverlayColorMode", (int)this.OverlayColorMode);
					}
					bool enableInnerBevel = this.EnableInnerBevel;
					if (enableInnerBevel)
					{
						material2.EnableKeyword("UIEFFECT_INNER_BEVEL");
						material2.SetColor("_HighlightColor", this.HighlightColor);
						material2.SetInt("_HighlightColorMode", (int)this.HighlightColorMode);
						material2.SetColor("_ShadowColor", this.ShadowColor);
						material2.SetInt("_ShadowColorMode", (int)this.ShadowColorMode);
						material2.SetVector("_HighlightOffset", this.HighlightOffset);
					}
					bool flag6 = this.GrayScale > 0;
					if (flag6)
					{
						bool flag7 = this.GrayScale == byte.MaxValue;
						if (flag7)
						{
							material2.EnableKeyword("UIEFFECT_GRAYSCALE");
						}
						else
						{
							material2.EnableKeyword("UIEFFECT_GRAYSCALE_LERP");
							material2.SetFloat("_GrayLerp", 1f - (float)this.GrayScale / 255f);
						}
					}
					material = material2;
				}
			}
			return material;
		}

		internal float BlurDistance;

		internal Texture2D OverlayTexture;

		internal ColorModeEnum OverlayColorMode;

		internal float OverlaySpeed;

		internal bool EnableInnerBevel;

		internal Color HighlightColor;

		internal ColorModeEnum HighlightColorMode;

		internal Color ShadowColor;

		internal ColorModeEnum ShadowColorMode;

		internal Vector2 HighlightOffset;

		internal byte GrayScale;
	}
}
