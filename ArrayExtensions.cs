﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public static class ArrayExtensions
	{
		public static void Shuffle<T>(this T[] array)
		{
			for (int i = 0; i < array.Length; i++)
			{
				int num = Random.Range(0, array.Length);
				T t = array[num];
				array[num] = array[i];
				array[i] = t;
			}
		}

		public static T[] RemoveDuplicate<T>(this T[] array)
		{
			HashSet<T> hashSet = new HashSet<T>();
			foreach (T t in array)
			{
				bool flag = !hashSet.Contains(t);
				if (flag)
				{
					hashSet.Add(t);
				}
			}
			T[] array2 = new T[hashSet.Count];
			int num = 0;
			foreach (T t2 in hashSet)
			{
				array2[num++] = t2;
			}
			return array2;
		}

		public static U[] Cast<T, U>(this T[] array) where T : class where U : class, T
		{
			return Array.ConvertAll<T, U>(array, (T input) => input as U);
		}
	}
}
