﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Render/Nirvana Renderer")]
	[RequireComponent(typeof(Renderer))]
	[ExecuteInEditMode]
	[DisallowMultipleComponent]
	public sealed class NirvanaRenderer : MonoBehaviour
	{
		public Material[] Materials
		{
			get
			{
				return this.materials;
			}
			set
			{
				this.materials = value;
			}
		}

		public Bounds[] CustomBounds
		{
			get
			{
				return this.customBounds;
			}
		}

		public int RenderQueue
		{
			get
			{
				return this.renderQueue;
			}
			set
			{
				this.renderQueue = value;
			}
		}

		public Renderer UnityRenderer
		{
			get
			{
				bool flag = this.unityRenderer == null;
				if (flag)
				{
					this.unityRenderer = base.GetComponent<Renderer>();
				}
				return this.unityRenderer;
			}
		}

		public bool IsStatic
		{
			get
			{
				return this.isStatic;
			}
			set
			{
				this.isStatic = value;
			}
		}

		public MaterialPropertyBlock PropertyBlock
		{
			get
			{
				bool flag = this.propertyBlock == null;
				if (flag)
				{
					this.propertyBlock = new MaterialPropertyBlock();
				}
				return this.propertyBlock;
			}
		}

		private Material[] AppliedMaterials
		{
			get
			{
				bool flag = this.appliedMaterials == null || this.appliedMaterials.Length != this.materials.Length;
				if (flag)
				{
					this.appliedMaterials = new Material[this.materials.Length];
				}
				return this.appliedMaterials;
			}
		}

		public RenderEffect[] FindAllEffect()
		{
			return new RenderEffect[0];
		}

		public void SetDefaultMaterial()
		{
		}

		public void AddDefaultMaterial()
		{
		}

		public void ClearPropertyBlock()
		{
			this.propertyBlock = null;
		}

		public void SetKeyword(int keyword)
		{
			this.keywords.SetKeyword(keyword);
		}

		public void UnsetKeyword(int keyword)
		{
			this.keywords.UnsetKeyword(keyword);
		}

		public void SetCustomBounds(Bounds[] bounds)
		{
			this.customBounds = bounds;
			bool flag = this.customCullNode == null;
			if (flag)
			{
				this.customCullNode = Singleton<CullingManager>.Instance.RegisterRenderer(this);
			}
		}

		public void ClearCustomBounds()
		{
			this.customBounds = null;
			bool flag = this.customCullNode != null;
			if (flag)
			{
				Singleton<CullingManager>.Instance.UnregisterRenderer(this.customCullNode);
				this.customCullNode = null;
			}
		}

		internal static LinkedListNode<RenderEffectManager> RegisterEffectManager(RenderEffectManager manager)
		{
			return NirvanaRenderer.effectManagers.AddLast(manager);
		}

		internal static void UnregisterEffectManager(LinkedListNode<RenderEffectManager> node)
		{
			NirvanaRenderer.effectManagers.Remove(node);
		}

		private void Awake()
		{
			this.OnWillRenderObject();
		}

		private void OnEnable()
		{
			bool flag = this.customBounds != null && this.customCullNode == null;
			if (flag)
			{
				this.customCullNode = Singleton<CullingManager>.Instance.RegisterRenderer(this);
			}
		}

		private void OnDisable()
		{
			bool flag = this.customCullNode != null;
			if (flag)
			{
				Singleton<CullingManager>.Instance.UnregisterRenderer(this.customCullNode);
				this.customCullNode = null;
			}
		}

		private void OnDestroy()
		{
			bool flag = this.customCullNode != null;
			if (flag)
			{
				Singleton<CullingManager>.Instance.UnregisterRenderer(this.customCullNode);
				this.customCullNode = null;
			}
		}

		private void OnWillRenderObject()
		{
			bool flag = this.materials == null;
			if (!flag)
			{
				ShaderKeywords shaderKeywords = this.keywords;
				LinkedListNode<RenderEffectManager> next;
				for (LinkedListNode<RenderEffectManager> linkedListNode = NirvanaRenderer.effectManagers.First; linkedListNode != null; linkedListNode = next)
				{
					next = linkedListNode.Next;
					RenderEffectManager value = linkedListNode.Value;
					value.SetupRenderer(this, ref shaderKeywords, ref this.propertyBlock);
				}
				bool flag2 = this.renderQueue != -1 || !shaderKeywords.IsEmpty;
				if (flag2)
				{
					Material[] array = this.AppliedMaterials;
					for (int i = 0; i < this.materials.Length; i++)
					{
						Material material = this.materials[i];
						bool flag3 = material != null;
						if (flag3)
						{
							array[i] = Singleton<RendererMaterialCache>.Instance.GetMaterial(material, this.renderQueue, shaderKeywords);
						}
						else
						{
							array[i] = null;
						}
					}
					Renderer renderer = this.UnityRenderer;
					renderer.sharedMaterials = array;
				}
				else
				{
					Renderer renderer2 = this.UnityRenderer;
					renderer2.sharedMaterials = this.materials;
				}
				bool flag4 = this.propertyBlock != null;
				if (flag4)
				{
					Renderer renderer3 = this.UnityRenderer;
					renderer3.SetPropertyBlock(this.propertyBlock);
				}
			}
		}

		private static LinkedList<RenderEffectManager> effectManagers = new LinkedList<RenderEffectManager>();

		[SerializeField]
		[Tooltip("The origin material used for the renderer.")]
		private Material[] materials;

		[SerializeField]
		[Tooltip("The render queue.")]
		private int renderQueue = -1;

		private ShaderKeywords keywords;

		private Bounds[] customBounds;

		private LinkedListNode<NirvanaRenderer> customCullNode;

		private MaterialPropertyBlock propertyBlock;

		private Material[] appliedMaterials;

		private bool isStatic;

		private Renderer unityRenderer;
	}
}
