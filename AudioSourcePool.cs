﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class AudioSourcePool
	{
		public AudioSourcePool(Transform usingRoot, Transform cacheRoot)
		{
			this.usingRoot = usingRoot;
			this.cacheRoot = cacheRoot;
		}

		internal AudioSource Allocate(string name)
		{
			bool flag = this.cache.Count > 0;
			AudioSource audioSource;
			if (flag)
			{
				audioSource = this.cache.Pop();
				audioSource.name = name;
			}
			else
			{
				GameObject gameObject = new GameObject(name);
				audioSource = gameObject.AddComponent<AudioSource>();
			}
			audioSource.transform.parent = this.usingRoot;
			audioSource.gameObject.SetActive(true);
			return audioSource;
		}

		internal void Free(AudioSource source)
		{
			source.name = "Free Audio Source";
			source.transform.parent = this.cacheRoot;
			source.clip = null;
			source.outputAudioMixerGroup = null;
			source.gameObject.SetActive(false);
			this.cache.Push(source);
		}

		private Transform usingRoot;

		private Transform cacheRoot;

		private Stack<AudioSource> cache = new Stack<AudioSource>();
	}
}
