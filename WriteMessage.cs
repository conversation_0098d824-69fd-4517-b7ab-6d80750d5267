﻿using System;
using System.Collections.Generic;
using System.IO;

namespace Nirvana
{
	internal sealed class WriteMessage
	{
		public uint Length
		{
			get
			{
				return this.length;
			}
		}

		public int ByteSended { get; set; }

		public object Context { get; set; }

		public byte[] Buffer
		{
			get
			{
				bool flag = this.stream == null;
				byte[] array;
				if (flag)
				{
					array = null;
				}
				else
				{
					array = this.stream.GetBuffer();
				}
				return array;
			}
		}

		public int BufferLength
		{
			get
			{
				bool flag = this.stream == null;
				int num;
				if (flag)
				{
					num = 0;
				}
				else
				{
					num = (int)this.stream.Length;
				}
				return num;
			}
		}

		public static WriteMessage Alloc()
		{
			bool flag = WriteMessage.pool.Count > 0;
			WriteMessage writeMessage;
			if (flag)
			{
				writeMessage = WriteMessage.pool.Pop();
			}
			else
			{
				writeMessage = new WriteMessage();
			}
			return writeMessage;
		}

		public static void Free(WriteMessage message)
		{
			bool flag = WriteMessage.pool.Count < 8;
			if (flag)
			{
				message.length = 0U;
				message.ByteSended = 0;
				message.Context = null;
				WriteMessage.pool.Push(message);
			}
		}

		public void SetData(byte[] data)
		{
			bool flag = this.stream == null;
			if (flag)
			{
				this.stream = new MemoryStream(4 + data.Length);
			}
			else
			{
				this.stream.Seek(0L, SeekOrigin.Begin);
				this.stream.SetLength(0L);
			}
			uint num = (uint)data.Length;
			byte[] array = new byte[]
			{
				(byte)(num & 255U),
				(byte)((num >> 8) & 255U),
				(byte)((num >> 16) & 255U),
				(byte)((num >> 24) & 255U)
			};
			this.stream.Write(array, 0, 4);
			this.stream.Write(data, 0, data.Length);
			this.length = num;
		}

		private const int PoolCount = 8;

		private static Stack<WriteMessage> pool = new Stack<WriteMessage>();

		private uint length;

		private MemoryStream stream;
	}
}
