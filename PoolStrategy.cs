﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Pool/Pool Strategy")]
	public sealed class PoolStrategy : MonoBehaviour
	{
		public float PrefabReleaseAfterFree
		{
			get
			{
				return this.prefabReleaseAfterFree;
			}
		}

		public float InstanceReleaseAfterFree
		{
			get
			{
				return this.instanceReleaseAfterFree;
			}
		}

		public int InstancePoolCount
		{
			get
			{
				return this.instancePoolCount;
			}
		}

		[Header("Prefab cache strategy.")]
		[SerializeField]
		[Tooltip("The time in second the prefab will auto release after it has free.")]
		private float prefabReleaseAfterFree = 30f;

		[<PERSON><PERSON>("Instance pool strategy.")]
		[SerializeField]
		[Tooltip("The time in second the instance will auto release after it has free.")]
		private float instanceReleaseAfterFree = 60f;

		[SerializeField]
		[Tooltip("The maximum count of the instance will pool.")]
		private int instancePoolCount = 5;
	}
}
