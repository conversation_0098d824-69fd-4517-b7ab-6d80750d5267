﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	[Serializable]
	public struct ShaderKeywords : IEnumerable<int>, IEnumerable, IEquatable<ShaderKeywords>
	{
		public bool IsEmpty
		{
			get
			{
				return this.keywords == 0;
			}
		}

		public static void SetKeywordName(int keyword, string name)
		{
			Assert.IsTrue(keyword >= 0 && keyword < 32);
			ShaderKeywords.KeywordNames[keyword] = name;
		}

		public static string GetKeywordName(int keyword)
		{
			Assert.IsTrue(keyword >= 0 && keyword < 32);
			return ShaderKeywords.KeywordNames[keyword];
		}

		public void SetKeyword(int keyword)
		{
			Assert.IsTrue(keyword >= 0 && keyword < 32);
			this.keywords |= 1 << keyword;
		}

		public void UnsetKeyword(int keyword)
		{
			Assert.IsTrue(keyword >= 0 && keyword < 32);
			this.keywords &= ~(1 << keyword);
		}

		public void ToggleKeyword(int keyword)
		{
			Assert.IsTrue(keyword >= 0 && keyword < 32);
			this.keywords ^= 1 << keyword;
		}

		public void Merge(ShaderKeywords keywords)
		{
			this.keywords |= keywords.keywords;
		}

		public bool HasKeyword(int keyword)
		{
			Assert.IsTrue(keyword > 0 && keyword < 32);
			return (this.keywords & (1 << keyword)) != 0;
		}

		public bool Equals(ShaderKeywords other)
		{
			return this.keywords == other.keywords;
		}

		public override int GetHashCode()
		{
			return this.keywords.GetHashCode();
		}

		IEnumerator IEnumerable.GetEnumerator()
		{
			return new ShaderKeywords.KeyworkdEnumerator(this.keywords);
		}

		public IEnumerator<int> GetEnumerator()
		{
			return new ShaderKeywords.KeyworkdEnumerator(this.keywords);
		}

		private const int MaxKeywordCount = 32;

		private static readonly string[] KeywordNames = new string[32];

		[SerializeField]
		private int keywords;

		private struct KeyworkdEnumerator : IEnumerator<int>, IEnumerator, IDisposable
		{
			public KeyworkdEnumerator(int keywords)
			{
				this.keywords = keywords;
				this.index = -1;
			}

			public int Current
			{
				get
				{
					return this.index;
				}
			}

			object IEnumerator.Current
			{
				get
				{
					return this.index;
				}
			}

			public bool MoveNext()
			{
				this.index++;
				int num = 32;
				for (int i = this.index; i < num; i++)
				{
					bool flag = (this.keywords & (1 << i)) != 0;
					if (flag)
					{
						this.index = i;
						return true;
					}
				}
				return false;
			}

			public void Reset()
			{
				this.index = -1;
			}

			public void Dispose()
			{
			}

			private int keywords;

			private int index;
		}
	}
}
