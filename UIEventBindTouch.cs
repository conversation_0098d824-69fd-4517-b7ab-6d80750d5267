﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Event Bind Touch")]
	public sealed class UIEventBindTouch : UIEventBind, IPointerDownHandler, IEventSystemHandler, IPointerUpHandler
	{
		private Signal UIDownEvent
		{
			get
			{
				bool flag = this.pointerDownSignal == null;
				if (flag)
				{
					this.pointerDownSignal = base.FindEvent(this.downEventName);
				}
				return this.pointerDownSignal;
			}
		}

		private Signal UIUpEvent
		{
			get
			{
				bool flag = this.pointerUpSignal == null;
				if (flag)
				{
					this.pointerUpSignal = base.FindEvent(this.upEventName);
				}
				return this.pointerUpSignal;
			}
		}

		public void OnPointerDown(PointerEventData eventData)
		{
			Signal uidownEvent = this.UIDownEvent;
			bool flag = uidownEvent != null;
			if (flag)
			{
				uidownEvent.Invoke(Array.Empty<object>());
			}
		}

		public void OnPointerUp(PointerEventData eventData)
		{
			Signal uiupEvent = this.UIUpEvent;
			bool flag = uiupEvent != null;
			if (flag)
			{
				uiupEvent.Invoke(Array.Empty<object>());
			}
		}

		[SerializeField]
		[EventName]
		public string downEventName;

		[SerializeField]
		[EventName]
		public string upEventName;

		private Signal pointerDownSignal;

		private Signal pointerUpSignal;
	}
}
