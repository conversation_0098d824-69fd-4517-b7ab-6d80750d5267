﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public abstract class RenderEffect : MonoBehaviour
	{
		public abstract bool IsStatic { get; }

		public abstract RenderEffectManager EffectManager { get; }

		public abstract bool IsTakeEffect(NirvanaRenderer renderer);

		protected void Awake()
		{
			bool flag = this.IsStatic && Application.isPlaying;
			if (flag)
			{
				this.node = this.EffectManager.AddStaticEffect(this);
			}
			else
			{
				this.node = this.EffectManager.AddDynamicEffect(this);
			}
		}

		protected void OnEnable()
		{
			bool flag = this.node == null;
			if (flag)
			{
				bool flag2 = this.IsStatic && Application.isPlaying;
				if (flag2)
				{
					this.node = this.EffectManager.AddStaticEffect(this);
				}
				else
				{
					this.node = this.EffectManager.AddDynamicEffect(this);
				}
			}
		}

		protected void OnDisable()
		{
			bool flag = this.node != null;
			if (flag)
			{
				bool flag2 = this.IsStatic && Application.isPlaying;
				if (flag2)
				{
					this.EffectManager.RemoveStaticEffect(this.node);
				}
				else
				{
					this.EffectManager.RemoveDynamicEffect(this.node);
				}
				this.node = null;
			}
		}

		protected void OnDestroy()
		{
			bool flag = this.node != null;
			if (flag)
			{
				bool flag2 = this.IsStatic && Application.isPlaying;
				if (flag2)
				{
					this.EffectManager.RemoveStaticEffect(this.node);
				}
				else
				{
					this.EffectManager.RemoveDynamicEffect(this.node);
				}
				this.node = null;
			}
		}

		private LinkedListNode<RenderEffect> node;
	}
}
