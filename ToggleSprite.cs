﻿using System;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/Toggle Sprite")]
	public sealed class ToggleSprite : MonoBehaviour
	{
		private void Awake()
		{
			this.toggle = base.GetComponent<Toggle>();
			Assert.IsNotNull<Toggle>(this.toggle);
			bool flag = this.image != null;
			if (flag)
			{
				this.origin = this.image.sprite;
			}
			this.OnValueChanged(this.toggle.isOn);
			this.toggle.onValueChanged.AddListener(new UnityAction<bool>(this.OnValueChanged));
		}

		private void OnDestroy()
		{
			bool flag = this.toggle != null;
			if (flag)
			{
				this.toggle.onValueChanged.RemoveListener(new UnityAction<bool>(this.OnValueChanged));
			}
		}

		private void OnValueChanged(bool isOn)
		{
			bool flag = this.image != null;
			if (flag)
			{
				this.image.sprite = (isOn ? this.toggledSprite : this.origin);
			}
		}

		[SerializeField]
		[Tooltip("The image target to change color.")]
		private Image image;

		[SerializeField]
		[Tooltip("The sprite changed when toggled.")]
		private Sprite toggledSprite;

		private Toggle toggle;

		private Sprite origin;
	}
}
