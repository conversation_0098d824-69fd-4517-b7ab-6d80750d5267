﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class AudioItemPlayer
	{
		internal AudioItemPlayer(AudioSource source, float delay = 0f, float fadeinTime = 0f, float fadeoutTime = 0f)
		{
			this.source = source;
			this.delay = delay;
			this.fadeinTime = fadeinTime;
			this.fadeoutTime = fadeoutTime;
		}

		internal float Delay
		{
			get
			{
				return this.delay;
			}
			set
			{
				this.delay = value;
			}
		}

		internal float FadeinTime
		{
			get
			{
				return this.fadeinTime;
			}
			set
			{
				this.fadeinTime = value;
			}
		}

		internal float FadeoutTime
		{
			get
			{
				return this.fadeoutTime;
			}
			set
			{
				this.fadeoutTime = value;
			}
		}

		internal bool IsPlaying
		{
			get
			{
				return this.source != null && this.source.isPlaying;
			}
		}

		internal float PlayTime
		{
			get
			{
				bool flag = null == this.source;
				float num;
				if (flag)
				{
					num = 0f;
				}
				else
				{
					num = this.source.time;
				}
				return num;
			}
		}

		internal float TotalTime
		{
			get
			{
				bool flag = null == this.source;
				float num;
				if (flag)
				{
					num = 0f;
				}
				else
				{
					num = this.source.clip.length;
				}
				return num;
			}
		}

		internal bool IsFadeout
		{
			get
			{
				return this.stopTime > 0f;
			}
		}

		internal void Play()
		{
			bool flag = null == this.source;
			if (!flag)
			{
				this.startTime = Time.realtimeSinceStartup;
				this.stopTime = -1f;
				this.volume = this.source.volume;
				bool flag2 = this.delay > 0f;
				if (flag2)
				{
					this.source.PlayDelayed(this.delay);
				}
				else
				{
					this.source.Play();
				}
			}
		}

		internal void Stop()
		{
			bool flag = this.fadeoutTime > 0f;
			if (flag)
			{
				this.stopTime = Time.realtimeSinceStartup;
			}
			else
			{
				bool flag2 = this.source != null;
				if (flag2)
				{
					this.source.Stop();
					this.source = null;
				}
			}
		}

		internal void Update()
		{
			bool flag = null == this.source;
			if (!flag)
			{
				bool flag2 = this.stopTime > 0f;
				if (flag2)
				{
					float num = Time.realtimeSinceStartup - this.stopTime;
					float num2 = this.fadeoutTime - num;
					bool flag3 = num2 > 0f;
					if (!flag3)
					{
						this.source.Stop();
						this.source = null;
						return;
					}
					float num3 = num2 / this.fadeoutTime;
					this.source.volume = this.volume * num3;
				}
				bool flag4 = this.startTime > 0f;
				if (flag4)
				{
					float num4 = Time.realtimeSinceStartup - this.startTime;
					float num5 = this.fadeinTime - num4;
					bool flag5 = num5 > 0f;
					if (flag5)
					{
						float num6 = 1f - num5 / this.fadeinTime;
						this.source.volume = this.volume * num6;
					}
					else
					{
						this.source.volume = this.volume;
						this.startTime = -1f;
					}
				}
			}
		}

		private AudioSource source;

		private float delay;

		private float fadeinTime;

		private float fadeoutTime;

		private float startTime;

		private float stopTime;

		private float volume;
	}
}
