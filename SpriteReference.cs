﻿using System;
using UnityEngine;

namespace Nirvana
{
	public class SpriteReference
	{
		~SpriteReference()
		{
			this.TryRelease(true);
		}

		public void SetSprite(Sprite sprite)
		{
			bool flag = this.sprite == sprite;
			if (!flag)
			{
				bool flag2 = null != this.sprite;
				if (flag2)
				{
					this.TryRelease(false);
				}
				this.sprite = sprite;
				this.TryRetain();
			}
		}

		private void TryRetain()
		{
			bool flag = null != this.sprite && !this.isRetained;
			if (flag)
			{
				this.isRetained = Singleton<SpritePool>.Instance.Retain(this.sprite);
			}
		}

		private void TryRelease(bool finilized = false)
		{
			bool flag = this.isRetained && null != this.sprite;
			if (flag)
			{
				this.isRetained = false;
				if (finilized)
				{
					ReferenceDict.ReleaseSprite(this.sprite);
				}
				else
				{
					Singleton<SpritePool>.Instance.Free(this.sprite, false);
				}
				this.sprite = null;
			}
		}

		private Sprite sprite;

		private bool isRetained = false;
	}
}
