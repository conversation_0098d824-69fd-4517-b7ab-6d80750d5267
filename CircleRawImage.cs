﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	public sealed class CircleRawImage : RawImage
	{
		public int SegmentCount
		{
			get
			{
				return this.segmentCount;
			}
			set
			{
				bool flag = this.segmentCount != value;
				if (flag)
				{
					this.segmentCount = value;
					this.SetVerticesDirty();
				}
			}
		}

		protected override void OnPopulateMesh(VertexHelper vh)
		{
			vh.Clear();
			Vector2 pivot = base.rectTransform.pivot;
			Rect rect = base.rectTransform.rect;
			float num = -pivot.x * rect.width;
			float width = base.rectTransform.rect.width;
			float height = base.rectTransform.rect.height;
			float num2 = (float)this.fillPercent / 100f * 6.2831855f / (float)this.segmentCount;
			float num3 = 0f;
			Vector2 vector = Vector2.zero;
			for (int i = 0; i < this.segmentCount + 1; i++)
			{
				float num4 = Mathf.Cos(num3);
				float num5 = Mathf.Sin(num3);
				Vector2 vector2 = vector;
				Vector2 vector3;
				vector3..ctor(num * num4, num * num5);
				Vector2 zero = Vector2.zero;
				Vector2 zero2 = Vector2.zero;
				vector = vector3;
				Vector2 vector4;
				vector4..ctor(vector2.x / width + 0.5f, vector2.y / height + 0.5f);
				Vector2 vector5;
				vector5..ctor(vector3.x / width + 0.5f, vector3.y / height + 0.5f);
				Vector2 vector6;
				vector6..ctor(zero.x / width + 0.5f, zero.y / height + 0.5f);
				Vector2 vector7;
				vector7..ctor(zero2.x / width + 0.5f, zero2.y / height + 0.5f);
				UIVertex[] array = new UIVertex[4];
				int num6 = 0;
				UIVertex uivertex = default(UIVertex);
				uivertex.color = this.color;
				uivertex.position = vector2;
				uivertex.uv0 = vector4;
				array[num6] = uivertex;
				int num7 = 1;
				uivertex = default(UIVertex);
				uivertex.color = this.color;
				uivertex.position = vector3;
				uivertex.uv0 = vector5;
				array[num7] = uivertex;
				int num8 = 2;
				uivertex = default(UIVertex);
				uivertex.color = this.color;
				uivertex.position = zero;
				uivertex.uv0 = vector6;
				array[num8] = uivertex;
				int num9 = 3;
				uivertex = default(UIVertex);
				uivertex.color = this.color;
				uivertex.position = zero2;
				uivertex.uv0 = vector7;
				array[num9] = uivertex;
				UIVertex[] array2 = array;
				vh.AddUIVertexQuad(array2);
				num3 += num2;
			}
		}

		[SerializeField]
		[Range(4f, 360f)]
		private int segmentCount = 36;

		[SerializeField]
		[Range(-100f, 100f)]
		private int fillPercent = 100;
	}
}
