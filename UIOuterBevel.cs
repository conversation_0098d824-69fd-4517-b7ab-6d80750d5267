﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Outer Bevel")]
	[RequireComponent(typeof(RectTransform))]
	public sealed class UIOuterBevel : BaseMeshEffect
	{
		public Color HighlightColor
		{
			get
			{
				return this.highlightColor;
			}
			set
			{
				bool flag = this.highlightColor != value;
				if (flag)
				{
					this.highlightColor = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public Color ShadowColor
		{
			get
			{
				return this.shadowColor;
			}
			set
			{
				bool flag = this.shadowColor != value;
				if (flag)
				{
					this.shadowColor = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public Vector2 BevelDirectionAndDepth
		{
			get
			{
				return this.bevelDirectionAndDepth;
			}
			set
			{
				bool flag = this.bevelDirectionAndDepth != value;
				if (flag)
				{
					this.bevelDirectionAndDepth = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public bool UseGraphicAlpha
		{
			get
			{
				return this.useGraphicAlpha;
			}
			set
			{
				bool flag = this.useGraphicAlpha != value;
				if (flag)
				{
					this.useGraphicAlpha = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive() || vh.currentVertCount <= 0;
			if (!flag)
			{
				List<UIVertex> list = UIVertexListPool.Get();
				vh.GetUIVertexStream(list);
				List<UIVertex> list2 = this.Modify(list);
				bool flag2 = list2 != null;
				if (flag2)
				{
					vh.Clear();
					vh.AddUIVertexTriangleStream(list2);
				}
				UIVertexListPool.Release(list);
			}
		}

		private List<UIVertex> Modify(List<UIVertex> verts)
		{
			int count = verts.Count;
			int num = 0;
			int num2 = num;
			num = verts.Count;
			this.ApplyShadowZeroAlloc(verts, this.shadowColor, num2, verts.Count, this.bevelDirectionAndDepth.x * 0.75f, -this.bevelDirectionAndDepth.y * 0.75f);
			num2 = num;
			num = verts.Count;
			this.ApplyShadowZeroAlloc(verts, this.shadowColor, num2, verts.Count, this.bevelDirectionAndDepth.x, this.bevelDirectionAndDepth.y * 0.5f);
			num2 = num;
			num = verts.Count;
			this.ApplyShadowZeroAlloc(verts, this.shadowColor, num2, verts.Count, -this.bevelDirectionAndDepth.x * 0.5f, -this.bevelDirectionAndDepth.y);
			num2 = num;
			num = verts.Count;
			this.ApplyShadowZeroAlloc(verts, this.highlightColor, num2, verts.Count, -this.bevelDirectionAndDepth.x, this.bevelDirectionAndDepth.y * 0.5f);
			num2 = num;
			num = verts.Count;
			this.ApplyShadowZeroAlloc(verts, this.highlightColor, num2, verts.Count, -this.bevelDirectionAndDepth.x * 0.5f, this.bevelDirectionAndDepth.y);
			bool flag = this.HasComponent<UIMaterialEffect>();
			if (flag)
			{
				for (int i = 0; i < verts.Count - count; i++)
				{
					UIVertex uivertex = verts[i];
					uivertex.uv1 = new Vector2(0f, 0f);
					verts[i] = uivertex;
				}
			}
			return verts;
		}

		private void ApplyShadowZeroAlloc(List<UIVertex> verts, Color32 color, int start, int end, float x, float y)
		{
			for (int i = start; i < end; i++)
			{
				UIVertex uivertex = verts[i];
				verts.Add(uivertex);
				Vector3 position = uivertex.position;
				position.x += x;
				position.y += y;
				uivertex.position = position;
				Color32 color2 = color;
				bool flag = this.useGraphicAlpha;
				if (flag)
				{
					color2.a = color2.a * verts[i].color.a / byte.MaxValue;
				}
				uivertex.color = color2;
				verts[i] = uivertex;
			}
		}

		[SerializeField]
		private Color highlightColor = Color.white;

		[SerializeField]
		private Color shadowColor = Color.black;

		[SerializeField]
		private Vector2 bevelDirectionAndDepth = Vector2.one;

		[SerializeField]
		private bool useGraphicAlpha = true;
	}
}
