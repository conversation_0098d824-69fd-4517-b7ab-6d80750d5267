﻿using System;
using UnityEngine.EventSystems;

namespace Nirvana
{
	public sealed class PassPointerClickEvent : PassEvent, IPointerClickHandler, IEventSystemHandler
	{
		public void OnPointerClick(PointerEventData eventData)
		{
			base.PassEventDown<ISubmitHandler>(eventData, ExecuteEvents.submitHandler);
			base.PassEventDown<IPointerClickHandler>(eventData, ExecuteEvents.pointerClickHandler);
		}
	}
}
