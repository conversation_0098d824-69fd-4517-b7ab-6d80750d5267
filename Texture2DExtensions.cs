﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class Texture2DExtensions
	{
		public static bool HasAlpha(this Texture2D texture)
		{
			TextureFormat format = texture.format;
			TextureFormat textureFormat = format;
			if (textureFormat <= 20)
			{
				if (textureFormat - 1 > 1 && textureFormat - 4 > 1)
				{
					switch (textureFormat)
					{
					case 12:
					case 13:
					case 14:
					case 17:
					case 20:
						break;
					case 15:
					case 16:
					case 18:
					case 19:
						goto IL_0083;
					default:
						goto IL_0083;
					}
				}
			}
			else if (textureFormat - 24 > 1)
			{
				switch (textureFormat)
				{
				case 29:
				case 31:
				case 33:
					break;
				case 30:
				case 32:
					goto IL_0083;
				default:
					if (textureFormat - 46 > 7)
					{
						goto IL_0083;
					}
					break;
				}
			}
			return true;
			IL_0083:
			return false;
		}
	}
}
