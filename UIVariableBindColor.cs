﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Color")]
	[RequireComponent(typeof(Graphic))]
	public sealed class UIVariableBindColor : UIVariableBindBool
	{
		protected override void OnValueChanged()
		{
			bool flag = this.graphic == null;
			if (flag)
			{
				this.graphic = base.GetComponent<Graphic>();
			}
			bool flag2 = this.shadow == null;
			if (flag2)
			{
				this.shadow = base.GetComponent<Shadow>();
				bool flag3 = this.shadow != null;
				if (flag3)
				{
					this.shadowColor = this.shadow.effectColor;
				}
			}
			bool result = base.GetResult();
			bool flag4 = result;
			if (flag4)
			{
				this.graphic.color = this.enabledColor;
				bool flag5 = this.shadow != null;
				if (flag5)
				{
					this.shadow.effectColor = this.shadowColor;
				}
			}
			else
			{
				this.graphic.color = this.disabledColor;
				bool flag6 = this.shadow != null;
				if (flag6)
				{
					this.shadow.effectColor = this.disabledColor;
				}
			}
		}

		[SerializeField]
		public Color enabledColor = Color.white;

		[SerializeField]
		public Color disabledColor = Color.gray;

		private Graphic graphic;

		private Shadow shadow;

		private Color shadowColor;
	}
}
