﻿using System;
using System.Text;
using UnityEngine;

namespace Nirvana
{
	[Serializable]
	internal sealed class DiagnosticsGeneric : IDiagnosticsDocker, IDiagnosticsTab
	{
		public DiagnosticsDockAnchor DockAnchor
		{
			get
			{
				return this.dockAnchor;
			}
		}

		public bool DockDirty
		{
			get
			{
				return this.dockDitry;
			}
			set
			{
				this.dockDitry = value;
			}
		}

		public string TabName
		{
			get
			{
				return "Generic";
			}
		}

		internal bool HotspotTips
		{
			get
			{
				return this.hotspotTips;
			}
		}

		internal bool ErrorTips
		{
			get
			{
				return this.errorTips;
			}
		}

		public string GetDockText()
		{
			this.textBuilder.Remove(0, this.textBuilder.Length);
			this.textBuilder.Append("<size=");
			this.textBuilder.Append(this.dockFontSize);
			this.textBuilder.Append('>');
			bool flag = this.dockDeviceCPU;
			if (flag)
			{
				this.textBuilder.Append("CPU: ");
				this.textBuilder.Append(SystemInfo.processorType);
				this.textBuilder.Append(" [");
				this.textBuilder.Append(SystemInfo.processorCount);
				this.textBuilder.Append("] cores\n");
			}
			bool flag2 = this.dockDeviceGPU;
			if (flag2)
			{
				this.textBuilder.Append("GPU: ");
				this.textBuilder.Append(SystemInfo.graphicsDeviceName);
				this.textBuilder.Append(", API: ");
				this.textBuilder.Append(SystemInfo.graphicsDeviceVersion);
				this.textBuilder.Append("\nGPU SM: ");
				this.textBuilder.Append(this.GetShaderModeText(SystemInfo.graphicsShaderLevel));
				this.textBuilder.Append('\n');
			}
			bool flag3 = this.dockDeviceMemory;
			if (flag3)
			{
				this.textBuilder.Append("RAM: ");
				this.textBuilder.Append(SystemInfo.systemMemorySize);
				this.textBuilder.Append("MB, VRAM: ");
				this.textBuilder.Append(SystemInfo.graphicsMemorySize);
				this.textBuilder.Append(" MB\n");
			}
			bool flag4 = this.dockDeviceScreen;
			if (flag4)
			{
				Resolution currentResolution = Screen.currentResolution;
				this.textBuilder.Append("Screen: ");
				this.textBuilder.Append(currentResolution.width);
				this.textBuilder.Append('x');
				this.textBuilder.Append(currentResolution.height);
				this.textBuilder.Append('@');
				this.textBuilder.Append(currentResolution.refreshRate);
				this.textBuilder.Append("Hz, Window: ");
				this.textBuilder.Append(Screen.width);
				this.textBuilder.Append('x');
				this.textBuilder.Append(Screen.height);
				this.textBuilder.Append(", DPI: ");
				this.textBuilder.Append(Screen.dpi);
				this.textBuilder.Append('\n');
			}
			this.textBuilder.Append("</size>");
			return this.textBuilder.ToString();
		}

		public void DrawTab()
		{
			GUILayout.Label("System: ", Array.Empty<GUILayoutOption>());
			GUILayout.BeginVertical(GUI.skin.textArea, Array.Empty<GUILayoutOption>());
			this.DrawSystem();
			GUILayout.EndVertical();
			GUILayout.Label("Application: ", Array.Empty<GUILayoutOption>());
			GUILayout.BeginVertical(GUI.skin.textArea, Array.Empty<GUILayoutOption>());
			this.DrawApplication();
			GUILayout.EndVertical();
			GUILayout.Label("Workbench: ", Array.Empty<GUILayoutOption>());
			GUILayout.BeginVertical(GUI.skin.textArea, Array.Empty<GUILayoutOption>());
			this.DrawWorkbench();
			GUILayout.EndVertical();
			GUILayout.Label("Docker: ", Array.Empty<GUILayoutOption>());
			GUILayout.BeginVertical(GUI.skin.textArea, Array.Empty<GUILayoutOption>());
			this.DrawDock();
			GUILayout.EndVertical();
		}

		private void DrawSystem()
		{
			string text = string.Format("CPU: {0} [{1}] cores", SystemInfo.processorType, SystemInfo.processorCount);
			GUILayout.Label(text, Array.Empty<GUILayoutOption>());
			string text2 = string.Format("GPU: {0}, API: {1}", SystemInfo.graphicsDeviceName, SystemInfo.graphicsDeviceVersion);
			GUILayout.Label(text2, Array.Empty<GUILayoutOption>());
			string text3 = string.Format("RAM: {0}MB, VRAM: {0} MB", SystemInfo.systemMemorySize, SystemInfo.graphicsMemorySize);
			GUILayout.Label(text3, Array.Empty<GUILayoutOption>());
			string text4 = string.Format("Screen: {0}x{1}@{2}Hz, Window: {3}x{4}, DPI: {5}", new object[]
			{
				Screen.currentResolution.width,
				Screen.currentResolution.height,
				Screen.currentResolution.refreshRate,
				Screen.width,
				Screen.height,
				Screen.dpi
			});
			GUILayout.Label(text4, Array.Empty<GUILayoutOption>());
		}

		private void DrawApplication()
		{
			string text = string.Format("Application version: {0}", Application.version);
			GUILayout.Label(text, Array.Empty<GUILayoutOption>());
			string text2 = string.Format("Unity version: {0}", Application.unityVersion);
			GUILayout.Label(text2, Array.Empty<GUILayoutOption>());
			string text3 = string.Format("Company: {0}", Application.companyName);
			GUILayout.Label(text3, Array.Empty<GUILayoutOption>());
			string text4 = string.Format("Product: {0}", Application.productName);
			GUILayout.Label(text4, Array.Empty<GUILayoutOption>());
			string text5 = string.Format("Language: {0}", Application.systemLanguage);
			GUILayout.Label(text5, Array.Empty<GUILayoutOption>());
		}

		private void DrawWorkbench()
		{
			this.hotspotTips = DiagnosticsGUI.Toggle(this.hotspotTips, "HotSpot Tips");
			this.errorTips = DiagnosticsGUI.Toggle(this.errorTips, "Error Tips");
		}

		private void DrawDock()
		{
			DiagnosticsDockAnchor diagnosticsDockAnchor = DiagnosticsGUI.Anchor(this.dockAnchor, "Dock Anchor");
			bool flag = this.dockAnchor != diagnosticsDockAnchor;
			if (flag)
			{
				this.dockDitry = true;
				this.dockAnchor = diagnosticsDockAnchor;
			}
			int num = (int)DiagnosticsGUI.SliderLabel((float)this.dockFontSize, "Dock Font Size", 5f, 25f);
			bool flag2 = this.dockFontSize != num;
			if (flag2)
			{
				this.dockDitry = true;
				this.dockFontSize = num;
			}
			bool flag3 = DiagnosticsGUI.Toggle(this.dockDeviceCPU, "Dock Device CPU");
			bool flag4 = this.dockDeviceCPU != flag3;
			if (flag4)
			{
				this.dockDitry = true;
				this.dockDeviceCPU = flag3;
			}
			bool flag5 = DiagnosticsGUI.Toggle(this.dockDeviceGPU, "Dock Device GPU");
			bool flag6 = this.dockDeviceGPU != flag5;
			if (flag6)
			{
				this.dockDitry = true;
				this.dockDeviceGPU = flag5;
			}
			bool flag7 = DiagnosticsGUI.Toggle(this.dockDeviceMemory, "Dock Device Memory");
			bool flag8 = this.dockDeviceMemory != flag7;
			if (flag8)
			{
				this.dockDitry = true;
				this.dockDeviceMemory = flag7;
			}
			bool flag9 = DiagnosticsGUI.Toggle(this.dockDeviceScreen, "Dock Device Screen");
			bool flag10 = this.dockDeviceScreen != flag9;
			if (flag10)
			{
				this.dockDitry = true;
				this.dockDeviceScreen = flag9;
			}
		}

		private string GetShaderModeText(int shaderModel)
		{
			bool flag = shaderModel == 20;
			string text;
			if (flag)
			{
				text = "2.0";
			}
			else
			{
				bool flag2 = shaderModel == 30;
				if (flag2)
				{
					text = "3.0";
				}
				else
				{
					bool flag3 = shaderModel == 40;
					if (flag3)
					{
						text = "4.0";
					}
					else
					{
						bool flag4 = shaderModel == 41;
						if (flag4)
						{
							text = "4.1";
						}
						else
						{
							bool flag5 = shaderModel == 50;
							if (flag5)
							{
								text = "5.0";
							}
							else
							{
								text = "unknown";
							}
						}
					}
				}
			}
			return text;
		}

		[Header("Generic Configuration")]
		[SerializeField]
		[Tooltip("Whether to tip the hot spot region.")]
		private bool hotspotTips = true;

		[SerializeField]
		[Tooltip("Whether to tip when error happened.")]
		private bool errorTips = true;

		[Header("Dock Display Configuration")]
		[SerializeField]
		[Tooltip("The dock anchor.")]
		[EnumLabel]
		private DiagnosticsDockAnchor dockAnchor;

		[SerializeField]
		[Tooltip("The dock font size.")]
		private int dockFontSize = 12;

		[SerializeField]
		[Tooltip("Whether dock the device CPU information to the anchor.")]
		private bool dockDeviceCPU = true;

		[SerializeField]
		[Tooltip("Whether dock the device GPU information to the anchor.")]
		private bool dockDeviceGPU = true;

		[SerializeField]
		[Tooltip("Whether dock the device memory information to the anchor.")]
		private bool dockDeviceMemory = true;

		[SerializeField]
		[Tooltip("Whether dock the device screen information to the anchor.")]
		private bool dockDeviceScreen = true;

		private bool dockDitry = true;

		private StringBuilder textBuilder = new StringBuilder();
	}
}
