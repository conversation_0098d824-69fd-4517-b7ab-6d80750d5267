﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Event Bind Dropdown")]
	[RequireComponent(typeof(Dropdown))]
	public sealed class UIEventBindDropdown : UIEventBind
	{
		private Signal ValueChangedSignal
		{
			get
			{
				bool flag = this.valueChangedSignal == null;
				if (flag)
				{
					this.valueChangedSignal = base.FindEvent(this.valueChangedEventName);
				}
				return this.valueChangedSignal;
			}
		}

		private new void Awake()
		{
			base.Awake();
			this.dropdown = base.GetComponent<Dropdown>();
		}

		private void OnEnable()
		{
			this.dropdown.onValueChanged.AddListener(new UnityAction<int>(this.OnValueChanged));
		}

		private void OnDisable()
		{
			this.dropdown.onValueChanged.RemoveListener(new UnityAction<int>(this.OnValueChanged));
		}

		private void OnValueChanged(int value)
		{
			Signal signal = this.ValueChangedSignal;
			bool flag = signal != null;
			if (flag)
			{
				signal.Invoke(new object[] { value });
			}
		}

		[SerializeField]
		[EventName]
		public string valueChangedEventName;

		private Dropdown dropdown;

		private Signal valueChangedSignal;
	}
}
