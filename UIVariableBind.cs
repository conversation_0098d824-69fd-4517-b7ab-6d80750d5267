﻿using System;
using UnityEngine;

namespace Nirvana
{
	[ExecuteInEditMode]
	public abstract class UIVariableBind : MonoBehaviour
	{
		public UIVariableTable VariableTable { get; private set; }

		internal void Initialize()
		{
			bool flag = !this.binded;
			if (flag)
			{
				this.binded = true;
				this.RefreshVariableTable();
				this.BindVariables();
			}
		}

		public UIVariable FindVariable(string name)
		{
			bool flag = string.IsNullOrEmpty(name);
			UIVariable uivariable;
			if (flag)
			{
				uivariable = null;
			}
			else
			{
				bool flag2 = this.VariableTable != null;
				if (flag2)
				{
					uivariable = this.VariableTable.FindVariable(name);
				}
				else
				{
					uivariable = null;
				}
			}
			return uivariable;
		}

		protected void OnDestroy()
		{
			this.UnbindVariables();
			this.binded = false;
		}

		public abstract void BindVariables();

		public abstract void UnbindVariables();

		protected void Awake()
		{
			this.Initialize();
		}

		public void RefreshVariableTable()
		{
			bool flag = this.variableTable == null;
			if (flag)
			{
				this.variableTable = this.GetComponentInParentHard<UIVariableTable>();
			}
			this.VariableTable = this.variableTable;
		}

		[SerializeField]
		[Tooltip("The variable table for this bind.")]
		private UIVariableTable variableTable;

		[NonSerialized]
		public bool binded;
	}
}
