﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Event Bind InputField")]
	[RequireComponent(typeof(InputField))]
	public sealed class UIEventBindInputField : UIEventBind
	{
		private Signal ValueChangedSignal
		{
			get
			{
				bool flag = this.valueChangedSignal == null;
				if (flag)
				{
					this.valueChangedSignal = base.FindEvent(this.valueChangedEventName);
				}
				return this.valueChangedSignal;
			}
		}

		private Signal EndEditSignal
		{
			get
			{
				bool flag = this.endEditSignal == null;
				if (flag)
				{
					this.endEditSignal = base.FindEvent(this.endEditEventName);
				}
				return this.endEditSignal;
			}
		}

		private new void Awake()
		{
			base.Awake();
			this.inputField = base.GetComponent<InputField>();
		}

		private void OnEnable()
		{
			this.inputField.onValueChanged.AddListener(new UnityAction<string>(this.OnValueChanged));
			this.inputField.onEndEdit.AddListener(new UnityAction<string>(this.OnEndEdit));
		}

		private void OnDisable()
		{
			this.inputField.onValueChanged.RemoveListener(new UnityAction<string>(this.OnValueChanged));
			this.inputField.onEndEdit.RemoveListener(new UnityAction<string>(this.OnEndEdit));
		}

		private void OnValueChanged(string value)
		{
			Signal signal = this.ValueChangedSignal;
			bool flag = signal != null;
			if (flag)
			{
				signal.Invoke(new object[] { value });
			}
		}

		private void OnEndEdit(string value)
		{
			Signal signal = this.EndEditSignal;
			bool flag = signal != null;
			if (flag)
			{
				signal.Invoke(new object[] { value });
			}
		}

		[SerializeField]
		[EventName]
		public string valueChangedEventName;

		[SerializeField]
		[EventName]
		public string endEditEventName;

		private InputField inputField;

		private Signal valueChangedSignal;

		private Signal endEditSignal;
	}
}
