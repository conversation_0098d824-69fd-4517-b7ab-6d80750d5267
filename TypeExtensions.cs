﻿using System;
using System.Collections.Generic;
using System.Reflection;

namespace Nirvana
{
	public static class TypeExtensions
	{
		public static bool IsStruct(this Type type)
		{
			return type.IsValueType && !type.IsPrimitive && !type.IsEnum;
		}

		public static FieldInfo[] GetFieldInfosIncludingBaseClasses(this Type type, BindingFlags bindingFlags)
		{
			FieldInfo[] array = type.GetFields(bindingFlags);
			bool flag = type.BaseType == typeof(object);
			FieldInfo[] array2;
			if (flag)
			{
				array2 = array;
			}
			else
			{
				List<FieldInfo> list = new List<FieldInfo>(array);
				while (type.BaseType != typeof(object))
				{
					type = type.BaseType;
					array = type.GetFields(bindingFlags);
					foreach (FieldInfo fieldInfo in array)
					{
						bool flag2 = false;
						foreach (FieldInfo fieldInfo2 in list)
						{
							bool flag3 = fieldInfo2.Name == fieldInfo.Name && fieldInfo2.DeclaringType == fieldInfo.DeclaringType;
							if (flag3)
							{
								flag2 = true;
								break;
							}
						}
						bool flag4 = !flag2;
						if (flag4)
						{
							list.Add(fieldInfo);
						}
					}
				}
				array2 = list.ToArray();
			}
			return array2;
		}
	}
}
