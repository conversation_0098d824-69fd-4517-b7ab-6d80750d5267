﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Event Bind Toggle")]
	[RequireComponent(typeof(Toggle))]
	public sealed class UIEventBindToggle : UIEventBind
	{
		private Signal ValueChangedSignal
		{
			get
			{
				bool flag = this.valueChangedSignal == null;
				if (flag)
				{
					this.valueChangedSignal = base.FindEvent(this.eventName);
				}
				return this.valueChangedSignal;
			}
		}

		private new void Awake()
		{
			base.Awake();
			this.toggle = base.GetComponent<Toggle>();
		}

		private void OnEnable()
		{
			this.toggle.onValueChanged.AddListener(new UnityAction<bool>(this.OnValueChanged));
		}

		private void OnDisable()
		{
			this.toggle.onValueChanged.RemoveListener(new UnityAction<bool>(this.OnValueChanged));
		}

		private void OnValueChanged(bool value)
		{
			Signal signal = this.ValueChangedSignal;
			bool flag = signal != null;
			if (flag)
			{
				signal.Invoke(new object[] { value });
			}
		}

		[SerializeField]
		[EventName]
		public string eventName;

		private Toggle toggle;

		private Signal valueChangedSignal;
	}
}
