﻿using System;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Nirvana
{
	[ExecuteInEditMode]
	[AddComponentMenu("Nirvana/UI/Control/UI3D Display")]
	public sealed class UI3DDisplay : Mono<PERSON><PERSON><PERSON>our, IDragHandler, IEventSystemHandler
	{
		public Transform FitScaleRoot
		{
			get
			{
				return this.fitScaleRoot;
			}
		}

		public Camera DisplayCamera
		{
			get
			{
				return this.displayCamera;
			}
			set
			{
				this.displayCamera = value;
			}
		}

		public void SetDragSpeed(int dragSpeed)
		{
			this.dragSpeed = (float)dragSpeed;
		}

		public void DisplayPerspective(GameObject displayObject, Vector3 position, Vector3 rotation, float fieldOfView, float nearClipPlane, float farClipPlane)
		{
			this.DisplayInternal(displayObject, Vector3.zero, position, Quaternion.Euler(rotation), false, 0f, fieldOfView, nearClipPlane, farClipPlane);
		}

		public void DisplayPerspectiveSimple(GameObject displayObject, Vector3 position, Vector3 rotation)
		{
			this.DisplayInternal(displayObject, Vector3.zero, position, Quaternion.Euler(rotation), false, 0f, 40f, 0.3f, 500f);
		}

		public void DisplayPerspectiveWithOffset(GameObject displayObject, Vector3 offset, Vector3 position, Vector3 rotation)
		{
			this.DisplayInternal(displayObject, offset, position, Quaternion.Euler(rotation), false, 0f, 40f, 0.3f, 500f);
		}

		public void DisplayOrthographic(GameObject displayObject, Vector3 position, Vector3 rotation, float orthographicSize, float nearClipPlane, float farClipPlane)
		{
			this.DisplayInternal(displayObject, Vector3.zero, position, Quaternion.Euler(rotation), true, orthographicSize, 60f, nearClipPlane, farClipPlane);
		}

		public void DisplayOrthographicSimple(GameObject displayObject, Vector3 position, Vector3 rotation)
		{
			this.DisplayInternal(displayObject, Vector3.zero, position, Quaternion.Euler(rotation), true, 200f, 60f, 0.3f, 200f);
		}

		public void Display(GameObject displayObject, Camera lookCamera)
		{
			Transform transform = lookCamera.transform;
			this.DisplayInternal(displayObject, Vector3.zero, transform.localPosition, transform.localRotation, lookCamera.orthographic, lookCamera.orthographicSize, lookCamera.fieldOfView, lookCamera.nearClipPlane, lookCamera.farClipPlane);
		}

		private void DisplayInternal(GameObject displayObject, Vector3 offset, Vector3 position, Quaternion rotation, bool orthographic, float orthographicSize, float fieldOfView, float nearClipPlane, float farClipPlane)
		{
			Assert.IsNotNull<RawImage>(this.displayImage);
			Assert.IsNotNull<Camera>(this.displayCamera);
			this.displayObject = displayObject;
			bool flag = this.displayTexture == null;
			if (flag)
			{
				this.displayTexture = this.GetTemporaryTexture();
				this.displayImage.texture = this.displayTexture;
				this.displayCamera.targetTexture = this.displayTexture;
			}
			this.displayCamera.cullingMask = 1 << this.displayLayer;
			this.displayCamera.enabled = true;
			this.displayCamera.gameObject.SetActive(true);
			bool flag2 = this.displayCameraCtrl == null;
			if (flag2)
			{
				this.displayCameraCtrl = this.displayCamera.GetOrAddComponent<UI3DDisplayCamera>();
			}
			this.displayCameraCtrl.DisplayObject = displayObject;
			this.displayCameraCtrl.DisplayLayer = this.displayLayer;
			this.displayImage.enabled = true;
			Transform transform = this.displayObject.transform;
			bool flag3 = this.fitScaleRoot != null;
			if (flag3)
			{
				this.fitScaleRoot.localScale = Vector3.one;
				transform.SetParent(this.fitScaleRoot, true);
			}
			else
			{
				transform.SetParent(base.transform, true);
			}
			transform.localPosition = Vector3.zero;
			transform.localRotation = Quaternion.identity;
			transform.localScale = Vector3.one;
			this.displayCamera.orthographic = orthographic;
			this.displayCamera.orthographicSize = orthographicSize;
			this.displayCamera.fieldOfView = fieldOfView;
			this.displayCamera.nearClipPlane = nearClipPlane;
			this.displayCamera.farClipPlane = farClipPlane;
			this.orthographicSize = orthographicSize;
			Quaternion quaternion = Quaternion.Euler(this.displayRotation);
			Vector3 vector = quaternion * Vector3.up;
			quaternion *= Quaternion.AngleAxis(this.dragRotation, vector);
			transform.localRotation = quaternion;
			transform.localScale = this.displayScale;
			this.displayPosition = transform.localPosition + offset;
			transform.localPosition = this.displayPosition;
			Transform transform2 = this.displayCamera.transform;
			transform2.gameObject.layer = 0;
			transform2.localPosition = this.displayPosition + position;
			transform2.localRotation = rotation;
			bool flag4 = this.fitScaleRoot != null;
			if (flag4)
			{
				Vector3 lossyScale = this.fitScaleRoot.lossyScale;
				Vector3 localScale = base.transform.localScale;
				this.fitScaleRoot.localScale = new Vector3(1f / lossyScale.x * localScale.x, 1f / lossyScale.y * localScale.y, 1f / lossyScale.z * localScale.z);
			}
			bool flag5 = this.fitScaleRoot;
			if (flag5)
			{
				bool flag6 = this.autoFixed;
				if (flag6)
				{
					bool flag7 = this.displayCamera && this.displayCamera.orthographic && this.standScale > base.transform.parent.lossyScale.x;
					if (flag7)
					{
						float num = base.transform.parent.lossyScale.x / this.standScale;
						this.displayCamera.orthographicSize = this.orthographicSize * this.fitScaleRoot.localScale.x * Mathf.Pow(num, this.scalePow);
					}
				}
				else
				{
					this.displayCamera.orthographicSize = this.orthographicSize * this.fitScaleRoot.localScale.x;
				}
			}
		}

		public void ClearDisplay()
		{
			bool flag = this.displayCameraCtrl != null;
			if (flag)
			{
				this.displayCameraCtrl.DisplayObject = null;
			}
			this.displayObject = null;
		}

		public void ResetRotation()
		{
			this.dragRotation = 0f;
			bool flag = this.displayObject != null;
			if (flag)
			{
				Transform transform = this.displayObject.transform;
				Quaternion quaternion = Quaternion.Euler(this.displayRotation);
				Vector3 vector = quaternion * Vector3.up;
				quaternion *= Quaternion.AngleAxis(this.dragRotation, vector);
				transform.rotation = quaternion;
			}
		}

		public void SetRotation(Vector3 rotation)
		{
			this.displayRotation = rotation;
			bool flag = this.displayObject != null;
			if (flag)
			{
				Transform transform = this.displayObject.transform;
				Quaternion quaternion = Quaternion.Euler(this.displayRotation);
				Vector3 vector = quaternion * Vector3.up;
				quaternion *= Quaternion.AngleAxis(this.dragRotation, vector);
				transform.rotation = quaternion;
			}
		}

		public void SetScale(Vector3 scale)
		{
			this.displayScale = scale;
			bool flag = this.displayObject != null;
			if (flag)
			{
				Transform transform = this.displayObject.transform;
				transform.localScale = this.displayScale;
			}
		}

		public void OnDrag(PointerEventData eventData)
		{
			bool flag = this.displayObject && this.dragSpeed > 0f;
			if (flag)
			{
				float x = eventData.delta.x;
				float num = -this.dragSpeed * x * Time.deltaTime;
				this.dragRotation += num;
				Transform transform = this.displayObject.transform;
				Quaternion quaternion = Quaternion.Euler(this.displayRotation);
				Vector3 vector = quaternion * Vector3.up;
				quaternion *= Quaternion.AngleAxis(this.dragRotation, vector);
				transform.rotation = quaternion;
			}
		}

		private void Awake()
		{
			bool flag = this.displayImage == null;
			if (flag)
			{
				this.displayImage = base.GetComponent<RawImage>();
			}
			bool flag2 = this.displayImage != null && this.displayObject == null;
			if (flag2)
			{
				this.displayImage.enabled = false;
			}
			bool flag3 = this.displayCamera != null && this.displayObject == null;
			if (flag3)
			{
				this.displayCamera.enabled = false;
				this.displayCameraCtrl = this.displayCamera.GetOrAddComponent<UI3DDisplayCamera>();
			}
		}

		private void OnEnable()
		{
			bool flag = this.displayObject != null && this.displayTexture == null;
			if (flag)
			{
				bool flag2 = this.displayImage != null;
				if (flag2)
				{
					this.displayTexture = this.GetTemporaryTexture();
					this.displayImage.texture = this.displayTexture;
					this.displayImage.enabled = true;
				}
				bool flag3 = this.displayCamera != null;
				if (flag3)
				{
					this.displayCamera.targetTexture = this.displayTexture;
					this.displayCamera.enabled = true;
				}
			}
		}

		private void OnDisable()
		{
			bool flag = this.displayTexture != null;
			if (flag)
			{
				bool flag2 = this.displayTexture != null;
				if (flag2)
				{
					this.ReleaseTexture();
				}
				bool flag3 = this.displayImage != null;
				if (flag3)
				{
					this.displayImage.texture = null;
					this.displayImage.enabled = false;
				}
				bool flag4 = this.displayCamera != null;
				if (flag4)
				{
					this.displayCamera.targetTexture = null;
					this.displayCamera.enabled = false;
				}
			}
		}

		private void OnDestroy()
		{
			bool flag = this.displayTexture != null;
			if (flag)
			{
				this.ReleaseTexture();
				this.displayImage.texture = null;
				this.displayCamera.targetTexture = null;
				this.displayCamera.enabled = false;
			}
			bool flag2 = this.displayCameraCtrl != null;
			if (flag2)
			{
				this.displayCameraCtrl.DisplayObject = null;
			}
		}

		private void Update()
		{
			bool flag = this.fitScaleRoot;
			if (flag)
			{
				bool flag2 = this.autoFixed;
				if (flag2)
				{
					bool flag3 = this.displayCamera && this.displayCamera.orthographic && this.standScale > base.transform.parent.lossyScale.x;
					if (flag3)
					{
						float num = base.transform.parent.lossyScale.x / this.standScale;
						this.displayCamera.orthographicSize = this.orthographicSize * this.fitScaleRoot.localScale.x * Mathf.Pow(num, this.scalePow);
					}
				}
				else
				{
					this.displayCamera.orthographicSize = this.orthographicSize * this.fitScaleRoot.localScale.x;
				}
			}
		}

		private void SetupDisplayTextureResolution()
		{
			bool flag = this.resolutionSetup || this.displayImage == null;
			if (!flag)
			{
				this.resolutionSetup = true;
				this.displayImage.enabled = true;
				Rect rect = this.displayImage.rectTransform.rect;
				this.resolutionX = Mathf.FloorToInt(rect.width);
				this.resolutionY = Mathf.FloorToInt(rect.height);
				bool flag2 = this.resolutionX > 2048 || this.resolutionY > 2048;
				if (flag2)
				{
					float num = Mathf.Max((float)this.resolutionX / (float)UI3DDisplay.MaxResolutionX, (float)this.resolutionY / (float)UI3DDisplay.MaxResolutionY);
					this.resolutionX = Mathf.FloorToInt((float)this.resolutionX / num);
					this.resolutionY = Mathf.FloorToInt((float)this.resolutionY / num);
				}
			}
		}

		private RenderTexture GetTemporaryTexture()
		{
			this.SetupDisplayTextureResolution();
			RenderTexture temporary = RenderTexture.GetTemporary(this.resolutionX, this.resolutionY, 32, 0);
			temporary.autoGenerateMips = false;
			return temporary;
		}

		private void ReleaseTexture()
		{
			bool flag = null != this.displayTexture;
			if (flag)
			{
				RenderTexture.ReleaseTemporary(this.displayTexture);
				this.displayTexture = null;
			}
		}

		[SerializeField]
		[Tooltip("The display image.")]
		private RawImage displayImage;

		[SerializeField]
		[Tooltip("The display camera.")]
		private Camera displayCamera;

		[SerializeField]
		[Layer]
		[Tooltip("The display layer.")]
		private int displayLayer = 0;

		[SerializeField]
		[Tooltip("The drag speed.")]
		private float dragSpeed = 10f;

		[SerializeField]
		[Tooltip("The rotation for the display object.")]
		private Vector3 displayRotation = Vector3.zero;

		[SerializeField]
		[Tooltip("The scale for the display object.")]
		private Vector3 displayScale = Vector3.one;

		[SerializeField]
		[Tooltip("This transform will auto fit scale.")]
		private Transform fitScaleRoot;

		[SerializeField]
		private bool autoFixed = false;

		[SerializeField]
		private float standScale = 0.1503516f;

		[SerializeField]
		private float scalePow = 2.5f;

		private float dragRotation;

		private UI3DDisplayCamera displayCameraCtrl;

		private RenderTexture displayTexture;

		private GameObject displayObject;

		private Vector3 displayPosition;

		private float orthographicSize;

		private static int MaxResolutionX = 1800;

		private static int MaxResolutionY = 1200;

		private int resolutionX = 512;

		private int resolutionY = 512;

		private bool resolutionSetup = false;
	}
}
