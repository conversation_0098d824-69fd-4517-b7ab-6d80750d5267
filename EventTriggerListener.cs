﻿using System;
using System.Diagnostics;
using UnityEngine.EventSystems;

namespace Nirvana
{
	public sealed class EventTriggerListener : EventTrigger
	{
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.PointerEventDelegate BeginDragEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.BaseEventDelegate CancelEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.PointerEventDelegate DragEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.PointerEventDelegate DropEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.PointerEventDelegate EndDragEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.PointerEventDelegate PointerClickEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.PointerEventDelegate PointerDownEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.PointerEventDelegate PointerEnterEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.PointerEventDelegate PointerExitEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.PointerEventDelegate PointerUpEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.BaseEventDelegate SelectEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.BaseEventDelegate DeselectEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.BaseEventDelegate UpdateSelectedEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EventTriggerListener.AxisEventDelegate MoveEvent;

		public override void OnBeginDrag(PointerEventData eventData)
		{
			bool flag = this.BeginDragEvent != null;
			if (flag)
			{
				this.BeginDragEvent(eventData);
			}
		}

		public override void OnCancel(BaseEventData eventData)
		{
			bool flag = this.CancelEvent != null;
			if (flag)
			{
				this.CancelEvent(eventData);
			}
		}

		public override void OnDrag(PointerEventData eventData)
		{
			bool flag = this.DragEvent != null;
			if (flag)
			{
				this.DragEvent(eventData);
			}
		}

		public override void OnDrop(PointerEventData eventData)
		{
			bool flag = this.DropEvent != null;
			if (flag)
			{
				this.DropEvent(eventData);
			}
		}

		public override void OnEndDrag(PointerEventData eventData)
		{
			bool flag = this.EndDragEvent != null;
			if (flag)
			{
				this.EndDragEvent(eventData);
			}
		}

		public override void OnPointerClick(PointerEventData eventData)
		{
			bool flag = this.PointerClickEvent != null;
			if (flag)
			{
				this.PointerClickEvent(eventData);
			}
		}

		public override void OnPointerDown(PointerEventData eventData)
		{
			bool flag = this.PointerDownEvent != null;
			if (flag)
			{
				this.PointerDownEvent(eventData);
			}
		}

		public override void OnPointerEnter(PointerEventData eventData)
		{
			bool flag = this.PointerEnterEvent != null;
			if (flag)
			{
				this.PointerEnterEvent(eventData);
			}
		}

		public override void OnPointerExit(PointerEventData eventData)
		{
			bool flag = this.PointerExitEvent != null;
			if (flag)
			{
				this.PointerExitEvent(eventData);
			}
		}

		public override void OnPointerUp(PointerEventData eventData)
		{
			bool flag = this.PointerUpEvent != null;
			if (flag)
			{
				this.PointerUpEvent(eventData);
			}
		}

		public override void OnSelect(BaseEventData eventData)
		{
			bool flag = this.SelectEvent != null;
			if (flag)
			{
				this.SelectEvent(eventData);
			}
		}

		public override void OnDeselect(BaseEventData eventData)
		{
			bool flag = this.DeselectEvent != null;
			if (flag)
			{
				this.DeselectEvent(eventData);
			}
		}

		public override void OnUpdateSelected(BaseEventData eventData)
		{
			bool flag = this.UpdateSelectedEvent != null;
			if (flag)
			{
				this.UpdateSelectedEvent(eventData);
			}
		}

		public override void OnMove(AxisEventData eventData)
		{
			bool flag = this.MoveEvent != null;
			if (flag)
			{
				this.MoveEvent(eventData);
			}
		}

		public delegate void BaseEventDelegate(BaseEventData eventData);

		public delegate void PointerEventDelegate(PointerEventData eventData);

		public delegate void AxisEventDelegate(AxisEventData eventData);
	}
}
