﻿using System;
using System.Collections.Generic;

namespace Nirvana
{
	internal sealed class Signal
	{
		internal void Clear()
		{
			bool flag = this.signalDelegates != null;
			if (flag)
			{
				this.signalDelegates.Clear();
			}
		}

		internal SignalHandle Add(SignalDelegate callback)
		{
			bool flag = this.signalDelegates == null;
			if (flag)
			{
				this.signalDelegates = new LinkedList<SignalDelegate>();
			}
			LinkedListNode<SignalDelegate> linkedListNode = this.signalDelegates.AddLast(callback);
			return new SignalHandle(this.signalDelegates, linkedListNode);
		}

		internal void Invoke(params object[] args)
		{
			bool flag = this.signalDelegates != null;
			if (flag)
			{
				LinkedListNode<SignalDelegate> next;
				for (LinkedListNode<SignalDelegate> linkedListNode = this.signalDelegates.First; linkedListNode != null; linkedListNode = next)
				{
					next = linkedListNode.Next;
					SignalDelegate value = linkedListNode.Value;
					value(args);
				}
			}
		}

		private LinkedList<SignalDelegate> signalDelegates;
	}
}
