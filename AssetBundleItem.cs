﻿using System;
using UnityEngine;

namespace Nirvana
{
	internal sealed class AssetBundleItem
	{
		internal AssetBundleItem(AssetBundle assetBundle, string bundleName = "")
		{
			this.AssetBundle = assetBundle;
			this.bundleName = bundleName;
		}

		internal AssetBundleItem(WaitLoadAssetBundle wait, string bundleName = "")
		{
			this.bundleName = bundleName;
			this.wait = wait;
		}

		internal AssetBundleItem(string bundleName = "")
		{
			this.bundleName = bundleName;
		}

		internal void SetWaitLoadAssetBundle(WaitLoadAssetBundle wait)
		{
			this.wait = wait;
		}

		internal int RefCount
		{
			get
			{
				return this.refcount;
			}
		}

		internal string Error { get; set; }

		internal AssetBundle AssetBundle { get; private set; }

		internal string BundleName
		{
			get
			{
				return this.bundleName;
			}
		}

		internal AssetBundleItem[] Dependencies { get; set; }

		internal bool CheckLoading()
		{
			bool flag = this.wait == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = !this.wait.keepWaiting;
				if (flag3)
				{
					bool flag4 = this.wait.Error != null;
					if (flag4)
					{
						this.Error = this.wait.Error;
					}
					else
					{
						this.AssetBundle = this.wait.AssetBundle;
					}
					flag2 = true;
				}
				else
				{
					flag2 = false;
				}
			}
			return flag2;
		}

		internal void Retain()
		{
			this.refcount++;
		}

		internal void Release()
		{
			bool flag;
			if (this.refcount > 0)
			{
				int num = this.refcount - 1;
				this.refcount = num;
				flag = num <= 0;
			}
			else
			{
				flag = false;
			}
			bool flag2 = flag;
			if (flag2)
			{
				this.lastFreeTime = Time.time;
			}
		}

		internal void Destroy(bool unloadAllLoadedObjects = false)
		{
			bool flag = this.AssetBundle != null;
			if (flag)
			{
				this.AssetBundle.Unload(unloadAllLoadedObjects);
			}
			bool flag2 = this.Dependencies != null;
			if (flag2)
			{
				for (int i = 0; i < this.Dependencies.Length; i++)
				{
					AssetBundleItem assetBundleItem = this.Dependencies[i];
					bool flag3 = assetBundleItem != null;
					if (flag3)
					{
						assetBundleItem.Release();
					}
				}
			}
		}

		internal bool Sweep()
		{
			bool flag = this.refcount > 0;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool flag3 = Time.time >= this.lastFreeTime + 1f;
				if (flag3)
				{
					this.Destroy(true);
					flag2 = true;
				}
				else
				{
					flag2 = false;
				}
			}
			return flag2;
		}

		private const float ReleaseDelayTime = 1f;

		private int refcount = 0;

		private WaitLoadAssetBundle wait;

		private float lastFreeTime = 0f;

		private string bundleName;
	}
}
