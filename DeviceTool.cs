﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class DeviceTool
	{
		public static string GetDeviceID()
		{
			return DeviceTool.GetDeviceTool().CallStatic<string>("getDeviceID", new object[0]);
		}

		public static string GetVersion()
		{
			return DeviceTool.GetDeviceTool().CallStatic<string>("getVersion", new object[0]);
		}

		public static bool GetNetworkAccessibility()
		{
			return DeviceTool.GetDeviceTool().CallStatic<bool>("getNetworkAccessibility", new object[0]);
		}

		public static void Quit()
		{
			DeviceTool.GetDeviceTool().CallStatic("finish", new object[0]);
		}

		public static void SetScreenBrightness(float brightness)
		{
			DeviceTool.GetDeviceTool().CallStatic("setScreenBrightness", new object[] { brightness });
		}

		public static float GetScreenBrightness()
		{
			return DeviceTool.GetDeviceTool().CallStatic<float>("getScreenBrightness", new object[0]);
		}

		public static float GetUserScreenBrightness()
		{
			return DeviceTool.GetDeviceTool().CallStatic<float>("getUserScreenBrightness", new object[0]);
		}

		public static void GetScreenSafeArea(out Rect safe_area, out int resolution_width, out int resolution_height)
		{
			float num = DeviceTool.GetDeviceTool().CallStatic<float>("getNotchScreenHeight", new object[0]);
			float num2 = num;
			float num3 = 0f;
			float num4 = (float)Screen.width - num * 2f;
			float num5 = (float)Screen.height;
			resolution_width = 0;
			resolution_height = 0;
			safe_area = new Rect(num2, num3, num4, num5);
		}

		public static void GetScreenSafeAreaFix(out Rect safe_area, out int resolution_width, out int resolution_height)
		{
			float num = 0f;
			float num2 = 0f;
			float num3 = 0f;
			float num4 = 0f;
			resolution_width = 0;
			resolution_height = 0;
			string[] array = DeviceTool.GetDeviceTool().CallStatic<string>("getNotchScreenInfo", new object[0]).Split(new char[] { '_' });
			bool flag = array.Length >= 6;
			if (flag)
			{
				num = Convert.ToSingle(array[0]);
				num2 = Convert.ToSingle(array[1]);
				num3 = Convert.ToSingle(array[2]);
				num4 = Convert.ToSingle(array[3]);
				resolution_width = Convert.ToInt32(array[4]);
				resolution_height = Convert.ToInt32(array[5]);
			}
			safe_area = new Rect(num, num2, num3, num4);
		}

		public static float GetHomeSafeHeight()
		{
			return 0f;
		}

		public static string GetObbFilePath()
		{
			return DeviceTool.GetDeviceTool().CallStatic<string>("getObbFilePath", new object[0]);
		}

		public static bool IsEmulator()
		{
			return DeviceTool.GetDeviceTool().CallStatic<bool>("isEmulator", new object[0]);
		}

		public static string IsEmulator2(string data)
		{
			return DeviceTool.GetDeviceTool().CallStatic<string>("isEmulator2", new object[] { data });
		}

		public static bool IsAndroid64()
		{
			return DeviceTool.GetDeviceTool().CallStatic<bool>("isART64", new object[0]);
		}

		public static void RestartApplication()
		{
			DeviceTool.GetDeviceTool().CallStatic("restartApplication", new object[0]);
		}

		public static bool UpdateApk(string data)
		{
			return DeviceTool.GetDeviceTool().CallStatic<bool>("UpdateApk", new object[] { data });
		}

		private static AndroidJavaClass GetDeviceTool()
		{
			bool flag = DeviceTool.deviceToolClass == null;
			if (flag)
			{
				DeviceTool.deviceToolClass = new AndroidJavaClass("com.winunet.and.ConverUtil");
			}
			return DeviceTool.deviceToolClass;
		}

		private static AndroidJavaClass deviceToolClass;
	}
}
