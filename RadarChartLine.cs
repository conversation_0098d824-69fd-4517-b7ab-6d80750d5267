﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("UI/Radar Chart Line")]
	public sealed class RadarChartLine : Graphic
	{
		protected override void OnPopulateMesh(VertexHelper vh)
		{
			vh.Clear();
			bool flag = this.maxVolume == 0f;
			if (!flag)
			{
				UIVertex simpleVert = UIVertex.simpleVert;
				simpleVert.color = this.color;
				this.DrawFrame(vh, this.maxVolume);
				this.DrawAxis(vh, this.maxVolume);
				bool flag2 = this.drawMajorGrid && this.majorGridInterval < this.maxVolume;
				if (flag2)
				{
					int num = (int)(this.maxVolume / this.majorGridInterval);
					for (int i = 1; i <= num; i++)
					{
						this.DrawFrame(vh, (float)i * this.majorGridInterval);
					}
				}
			}
		}

		private void DrawFrame(VertexHelper vh, float vol)
		{
			int currentVertCount = vh.currentVertCount;
			UIVertex simpleVert = UIVertex.simpleVert;
			simpleVert.color = this.color;
			int num = this.verticesCount;
			float num2 = 360f / (float)num * 0.5f;
			float num3 = this.lineWidth / Mathf.Cos(num2 * 0.017453292f) / 2f;
			float num4 = 360f / (float)num;
			for (int i = 0; i < num; i++)
			{
				float num5 = (90f - num4 * (float)i) * 0.017453292f;
				float num6 = Mathf.Cos(num5);
				float num7 = Mathf.Sin(num5);
				float num8 = 0.5f * vol - num3;
				float num9 = 0.5f * vol + num3;
				float num10 = 0.5f + num6 * num8;
				float num11 = 0.5f + num7 * num8;
				float num12 = 0.5f + num6 * num9;
				float num13 = 0.5f + num7 * num9;
				Vector2 vector = this.CreatePos(num10, num11);
				Vector2 vector2 = this.CreatePos(num12, num13);
				simpleVert.position = vector;
				vh.AddVert(simpleVert);
				simpleVert.position = vector2;
				vh.AddVert(simpleVert);
				int num14 = num * 2;
				int num15 = i * 2;
				int num16 = (i + 1) * 2;
				int num17 = num15 % num14 + currentVertCount;
				int num18 = (num15 + 1) % num14 + currentVertCount;
				int num19 = num16 % num14 + currentVertCount;
				int num20 = (num16 + 1) % num14 + currentVertCount;
				vh.AddTriangle(num17, num18, num19);
				vh.AddTriangle(num19, num18, num20);
			}
		}

		private void DrawAxis(VertexHelper vh, float vol)
		{
			int currentVertCount = vh.currentVertCount;
			UIVertex simpleVert = UIVertex.simpleVert;
			simpleVert.color = this.color;
			int num = this.verticesCount;
			float num2 = 90f * this.lineWidth / (1.5707964f * vol);
			float num3 = 360f / (float)num;
			for (int i = 0; i < num; i++)
			{
				float num4 = (90f - num2 - num3 * (float)i) * 0.017453292f;
				float num5 = (90f + num2 - num3 * (float)i) * 0.017453292f;
				float num6 = 0.5f + Mathf.Cos(num4) * 0.5f * vol;
				float num7 = 0.5f + Mathf.Sin(num4) * 0.5f * vol;
				float num8 = 0.5f + Mathf.Cos(num5) * 0.5f * vol;
				float num9 = 0.5f + Mathf.Sin(num5) * 0.5f * vol;
				float num10 = 0.5f + (num6 - num8) / 2f;
				float num11 = 0.5f + (num7 - num9) / 2f;
				float num12 = 0.5f + (num8 - num6) / 2f;
				float num13 = 0.5f + (num9 - num7) / 2f;
				Vector2 vector = this.CreatePos(num10, num11);
				Vector2 vector2 = this.CreatePos(num12, num13);
				Vector2 vector3 = this.CreatePos(num6, num7);
				Vector2 vector4 = this.CreatePos(num8, num9);
				simpleVert.position = vector;
				vh.AddVert(simpleVert);
				simpleVert.position = vector2;
				vh.AddVert(simpleVert);
				simpleVert.position = vector3;
				vh.AddVert(simpleVert);
				simpleVert.position = vector4;
				vh.AddVert(simpleVert);
				int num14 = i * 4 + currentVertCount;
				int num15 = i * 4 + 3 + currentVertCount;
				int num16 = i * 4 + 2 + currentVertCount;
				int num17 = i * 4 + 1 + currentVertCount;
				vh.AddTriangle(num14, num15, num16);
				vh.AddTriangle(num14, num17, num15);
			}
		}

		private Vector2 CreatePos(float x, float y)
		{
			Vector2 pivot = base.rectTransform.pivot;
			Rect rect = base.rectTransform.rect;
			Vector2 zero = Vector2.zero;
			zero.x -= pivot.x;
			zero.y -= pivot.y;
			zero.x += x;
			zero.y += y;
			zero.x *= rect.width;
			zero.y *= rect.height;
			return zero;
		}

		private const float Radius = 0.5f;

		[SerializeField]
		[Range(3f, 30f)]
		private int verticesCount = 5;

		[SerializeField]
		[Range(0f, 1f)]
		private float maxVolume = 1f;

		[SerializeField]
		[Range(0.001f, 0.03f)]
		private float lineWidth = 0.02f;

		[SerializeField]
		private bool drawMajorGrid = true;

		[SerializeField]
		[Range(0.01f, 1f)]
		private float majorGridInterval = 0.2f;
	}
}
