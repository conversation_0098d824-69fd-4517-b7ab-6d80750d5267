﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.SceneManagement;

namespace Nirvana
{
	public static class AssetManager
	{
		public static void SetIsUseOBB(bool _isUseObb)
		{
			AssetManager.isUseObb = _isUseObb;
		}

		public static bool GetIsUseOBB()
		{
			return AssetManager.isUseObb;
		}

		public static event Action<string> DownloadStartEvent
		{
			add
			{
				AssetManager.bundleDownloader.DownloadStartEvent += value;
			}
			remove
			{
				AssetManager.bundleDownloader.DownloadStartEvent -= value;
			}
		}

		public static event Action<string> DownloadFinishEvent
		{
			add
			{
				AssetManager.bundleDownloader.DownloadFinishEvent += value;
			}
			remove
			{
				AssetManager.bundleDownloader.DownloadFinishEvent -= value;
			}
		}

		public static string CachePath
		{
			get
			{
				return AssetManager.bundleCache.CachePath;
			}
		}

		public static string DownloadingURL
		{
			get
			{
				return AssetManager.bundleLoader.DownloadingURL;
			}
			set
			{
				AssetManager.bundleLoader.DownloadingURL = value;
			}
		}

		public static string AssetVersion
		{
			get
			{
				return AssetManager.bundleLoader.AssetVersion;
			}
			set
			{
				AssetManager.bundleLoader.AssetVersion = value;
			}
		}

		public static bool IgnoreHashCheck
		{
			get
			{
				return AssetManager.bundleLoader.IgnoreHashCheck;
			}
			set
			{
				AssetManager.bundleLoader.IgnoreHashCheck = value;
			}
		}

		public static string[] ActiveVariants
		{
			get
			{
				return AssetManager.bundleManager.ActiveVariants;
			}
			set
			{
				AssetManager.bundleManager.ActiveVariants = value;
			}
		}

		public static AssetBundleManifest Manifest
		{
			get
			{
				return AssetManager.bundleManager.Manifest;
			}
		}

		public static bool HasManifest
		{
			get
			{
				return AssetManager.Manifest != null;
			}
		}

		public static void ClearCache()
		{
			AssetManager.bundleCache.ClearCache();
		}

		public static IEnumerator Dispose()
		{
			AssetManager.bundleLoader.Dispose();
			AssetManager.bundleManager.Manifest = null;
			AssetManager.progressWaitings.Clear();
			return new WaitUntil(delegate
			{
				bool flag = !AssetManager.bundleManager.HasLoadingBundles();
				bool flag3;
				if (flag)
				{
					bool flag2 = AssetManager.updateHandle != null;
					if (flag2)
					{
						Scheduler.RemoveFrameListener(AssetManager.updateHandle);
						AssetManager.updateHandle = null;
					}
					AssetManager.bundleManager.UnloadAll();
					flag3 = true;
				}
				else
				{
					flag3 = false;
				}
				return flag3;
			});
		}

		public static string LoadVersion()
		{
			return AssetManager.bundleCache.LoadVersion();
		}

		public static void SaveVersion(string version)
		{
			AssetManager.bundleCache.SaveVersion(version);
		}

		public static WaitLoadAsset LoadLocalManifest(string manifestAssetBundleName)
		{
			AssetManager.updateHandle = Scheduler.AddFrameListener(new Action(AssetManager.Update));
			bool flag = !AssetManager.bundleManager.LoadLocalManifest(manifestAssetBundleName);
			WaitLoadAsset waitLoadAsset;
			if (flag)
			{
				AssetManager.logger.LogError("Can not load local manifest: {0}", new object[] { manifestAssetBundleName });
				waitLoadAsset = null;
			}
			else
			{
				WaitLoadManifest waitLoadManifest = new WaitLoadManifest(AssetManager.bundleManager, manifestAssetBundleName, "AssetBundleManifest", typeof(AssetBundleManifest));
				AssetManager.progressWaitings.Add(waitLoadManifest);
				waitLoadAsset = waitLoadManifest;
			}
			return waitLoadAsset;
		}

		public static WaitLoadAsset LoadRemoteManifest(string manifestAssetBundleName)
		{
			bool flag = !AssetManager.bundleManager.LoadRemoteManifest(manifestAssetBundleName);
			WaitLoadAsset waitLoadAsset;
			if (flag)
			{
				AssetManager.logger.LogError("Can not load local manifest: {0}", new object[] { manifestAssetBundleName });
				waitLoadAsset = null;
			}
			else
			{
				WaitLoadManifest waitLoadManifest = new WaitLoadManifest(AssetManager.bundleManager, manifestAssetBundleName, "AssetBundleManifest", typeof(AssetBundleManifest));
				AssetManager.progressWaitings.Add(waitLoadManifest);
				waitLoadAsset = waitLoadManifest;
			}
			return waitLoadAsset;
		}

		public static bool ExistedInStreaming(string filePath)
		{
			return AssetManager.bundleCache.ExistedInStreaming(filePath);
		}

		public static bool IsVersionCached(string bundleName)
		{
			Hash128 assetBundleHash = AssetManager.Manifest.GetAssetBundleHash(bundleName);
			return AssetManager.bundleCache.IsVersionCached(bundleName, assetBundleHash);
		}

		public static bool IsVersionCached(string bundleName, Hash128 hash)
		{
			return AssetManager.bundleCache.IsVersionCached(bundleName, hash);
		}

		public static WaitLoadFileInfo LoadFileInfo()
		{
			string downloadingURL = AssetManager.DownloadingURL;
			Assert.IsFalse(string.IsNullOrEmpty(downloadingURL));
			string text = downloadingURL + "/file_info.txt";
			return AssetManager.fileInfo.Load(text);
		}

		public static WaitUpdateAssetBundle UpdateBundle(string bundleName)
		{
			Hash128 assetBundleHash = AssetManager.Manifest.GetAssetBundleHash(bundleName);
			return AssetManager.bundleLoader.UpdateBundle(bundleName, assetBundleHash);
		}

		public static WaitUpdateAssetBundle UpdateBundle(string bundleName, Hash128 hash)
		{
			return AssetManager.bundleLoader.UpdateBundle(bundleName, hash);
		}

		public static AssetBundle LoadBundleLocal(string assetBundleName)
		{
			return AssetManager.bundleManager.LoadAssetBundleSync(assetBundleName);
		}

		public static Object LoadObjectLocal(AssetID assetID, Type assetType)
		{
			return AssetManager.LoadObjectLocal(assetID.BundleName, assetID.AssetName, assetType);
		}

		public static Object LoadObjectLocal(string assetBundleName, string assetName, Type assetType)
		{
			AssetBundle assetBundle = AssetManager.bundleManager.LoadAssetBundleSync(assetBundleName);
			bool flag = assetBundle == null;
			Object @object;
			if (flag)
			{
				@object = null;
			}
			else
			{
				@object = assetBundle.LoadAsset(assetName, assetType);
			}
			return @object;
		}

		public static WaitLoadObject LoadObject(AssetID assetID, Type assetType)
		{
			return AssetManager.LoadObject(assetID.BundleName, assetID.AssetName, assetType);
		}

		public static WaitLoadObject LoadObjectSync(AssetID assetID, Type assetType)
		{
			return AssetManager.LoadObjectSync(assetID.BundleName, assetID.AssetName, assetType);
		}

		public static WaitLoadObject LoadObject(string assetBundleName, string assetName, Type assetType)
		{
			bool flag = AssetManager.bundleManager.LoadAssetBundle(assetBundleName, false);
			WaitLoadObject waitLoadObject;
			if (flag)
			{
				WaitLoadObjectFull waitLoadObjectFull = new WaitLoadObjectFull(AssetManager.bundleManager, assetBundleName, assetName, assetType);
				AssetManager.progressWaitings.Add(waitLoadObjectFull);
				waitLoadObject = waitLoadObjectFull;
			}
			else
			{
				string text = string.Format("Load asset bundle {0}:{1} failed.", assetBundleName, assetName);
				WaitLoadObjectFull waitLoadObjectFull2 = new WaitLoadObjectFull(text, Array.Empty<object>());
				waitLoadObject = waitLoadObjectFull2;
			}
			return waitLoadObject;
		}

		public static WaitLoadObject LoadObjectSync(string assetBundleName, string assetName, Type assetType)
		{
			bool flag = AssetManager.bundleManager.LoadAssetBundle(assetBundleName, true);
			WaitLoadObject waitLoadObject;
			if (flag)
			{
				WaitLoadObjectFullSync waitLoadObjectFullSync = new WaitLoadObjectFullSync(AssetManager.bundleManager, assetBundleName, assetName, assetType);
				AssetManager.progressWaitings.Add(waitLoadObjectFullSync);
				waitLoadObject = waitLoadObjectFullSync;
			}
			else
			{
				string text = string.Format("Load asset bundle {0}:{1} failed.", assetBundleName, assetName);
				WaitLoadObjectFull waitLoadObjectFull = new WaitLoadObjectFull(text, Array.Empty<object>());
				waitLoadObject = waitLoadObjectFull;
			}
			return waitLoadObject;
		}

		public static WaitLoadLevel LoadLevel(AssetID assetID, LoadSceneMode loadMode)
		{
			return AssetManager.LoadLevel(assetID.BundleName, assetID.AssetName, loadMode);
		}

		public static WaitLoadLevel LoadLevelSync(AssetID assetID, LoadSceneMode loadMode)
		{
			return AssetManager.LoadLevelSync(assetID.BundleName, assetID.AssetName, loadMode);
		}

		public static WaitLoadLevel LoadLevel(string assetBundleName, string sceneName, LoadSceneMode loadMode)
		{
			bool flag = AssetManager.bundleManager.LoadAssetBundle(assetBundleName, false);
			WaitLoadLevel waitLoadLevel;
			if (flag)
			{
				WaitLoadLevelFull waitLoadLevelFull = new WaitLoadLevelFull(AssetManager.bundleManager, assetBundleName, sceneName, loadMode);
				AssetManager.progressWaitings.Add(waitLoadLevelFull);
				waitLoadLevel = waitLoadLevelFull;
			}
			else
			{
				WaitLoadLevelFull waitLoadLevelFull2 = new WaitLoadLevelFull("Load Level {0}:{1} failed.", new object[] { assetBundleName, sceneName });
				waitLoadLevel = waitLoadLevelFull2;
			}
			return waitLoadLevel;
		}

		public static WaitLoadLevel LoadLevelSync(string assetBundleName, string sceneName, LoadSceneMode loadMode)
		{
			bool flag = AssetManager.bundleManager.LoadAssetBundle(assetBundleName, true);
			WaitLoadLevel waitLoadLevel;
			if (flag)
			{
				WaitLoadLevelFullSync waitLoadLevelFullSync = new WaitLoadLevelFullSync(AssetManager.bundleManager, assetBundleName, sceneName, loadMode);
				AssetManager.progressWaitings.Add(waitLoadLevelFullSync);
				waitLoadLevel = waitLoadLevelFullSync;
			}
			else
			{
				WaitLoadLevelFull waitLoadLevelFull = new WaitLoadLevelFull("Load Level {0}:{1} failed.", new object[] { assetBundleName, sceneName });
				waitLoadLevel = waitLoadLevelFull;
			}
			return waitLoadLevel;
		}

		public static string[] GetAssetsNamesInBundle(string assetBundleName, bool isFullName = false)
		{
			AssetBundle assetBundle = AssetManager.LoadBundleLocal(assetBundleName);
			bool flag = null == assetBundle;
			string[] array;
			if (flag)
			{
				array = new string[0];
			}
			else
			{
				string[] allAssetNames = assetBundle.GetAllAssetNames();
				bool flag2 = !isFullName;
				if (flag2)
				{
					for (int i = 0; i < allAssetNames.Length; i++)
					{
						int num = allAssetNames[i].LastIndexOf("/");
						bool flag3 = num >= 0;
						if (flag3)
						{
							allAssetNames[i] = allAssetNames[i].Substring(num + 1);
						}
					}
				}
				array = allAssetNames;
			}
			return array;
		}

		public static string[] GetDependBundles(string assetBundleName)
		{
			return AssetManager.Manifest.GetAllDependencies(assetBundleName);
		}

		public static string[] GetBundlesWithoutCached(string assetBundleName)
		{
			return AssetManager.bundleManager.GetBundlesWithoutCached(assetBundleName);
		}

		public static bool UnloadAsseBundle(string assetBundle)
		{
			return AssetManager.bundleManager.UnloadAsseBundle(assetBundle);
		}

		public static void UnloadAllUnUsedAssetBundle()
		{
			AssetManager.bundleManager.UnloadAllUnusedAssetBundle();
		}

		private static void Update()
		{
			AssetManager.bundleLoader.Update();
			AssetManager.bundleManager.Update();
			AssetManager.progressWaitings.RemoveAll((WaitLoadAsset waiting) => !waiting.Update());
		}

		public static void SetAssetBundleLoadMaxCount(int loadCount, int writeCount)
		{
			AssetManager.bundleManager.MaxLoadingCount = loadCount;
			AssetBundleLoadTask.SetMaxWriteCountLimit(writeCount);
		}

		public static void LoadRemoteManifest(string manifestAssetBundleName, Action<string> complete)
		{
			Scheduler.RunCoroutine(AssetManager.LoadRemoteManifestImpl(manifestAssetBundleName, complete));
		}

		public static void LoadFileInfo(Action<string, AssetBundleFileInfo> complete)
		{
			Scheduler.RunCoroutine(AssetManager.LoadFileInfoImpl(complete));
		}

		public static void UpdateBundle(string bundleName, AssetManager.UpdateDelegate update, Action<string> complete)
		{
			Scheduler.RunCoroutine(AssetManager.UpdateBundleImpl(bundleName, update, complete));
		}

		public static void UpdateBundle(string bundleName, Hash128 hash, AssetManager.UpdateDelegate update, Action<string> complete)
		{
			Scheduler.RunCoroutine(AssetManager.UpdateBundleImpl(bundleName, hash, update, complete));
		}

		public static void LoadObject(AssetID assetID, Type assetType, Action<Object> complete)
		{
			AssetManager.LoadObject(assetID.BundleName, assetID.AssetName, assetType, complete);
		}

		public static void LoadObjectSync(AssetID assetID, Type assetType, Action<Object> complete)
		{
			AssetManager.LoadObjectSync(assetID.BundleName, assetID.AssetName, assetType, complete);
		}

		public static void LoadObject(string assetBundleName, string assetName, Type assetType, Action<Object> complete)
		{
			Scheduler.RunCoroutine(AssetManager.LoadObjectImpl(assetBundleName, assetName, assetType, complete));
		}

		public static void LoadObjectSync(string assetBundleName, string assetName, Type assetType, Action<Object> complete)
		{
			Scheduler.RunCoroutine(AssetManager.LoadObjectSyncImpl(assetBundleName, assetName, assetType, complete));
		}

		public static void LoadLevel(AssetID assetID, LoadSceneMode loadMode, Action complete)
		{
			AssetManager.LoadLevel(assetID.BundleName, assetID.AssetName, loadMode, complete, null);
		}

		public static void LoadLevelSync(AssetID assetID, LoadSceneMode loadMode, Action complete)
		{
			AssetManager.LoadLevelSync(assetID.BundleName, assetID.AssetName, loadMode, complete);
		}

		public static void LoadLevel(string assetBundleName, string levelName, LoadSceneMode loadMode, Action complete, Action<float> progress = null)
		{
			Scheduler.RunCoroutine(AssetManager.LoadLevelImpl(assetBundleName, levelName, loadMode, complete, progress));
		}

		public static void LoadLevelSync(string assetBundleName, string levelName, LoadSceneMode loadMode, Action complete)
		{
			Scheduler.RunCoroutine(AssetManager.LoadLevelSyncImpl(assetBundleName, levelName, loadMode, complete));
		}

		private static IEnumerator LoadFileInfoImpl(Action<string, AssetBundleFileInfo> complete)
		{
			WaitLoadFileInfo wait = AssetManager.LoadFileInfo();
			yield return wait;
			complete(wait.Error, wait.FileInfo);
			yield break;
		}

		private static IEnumerator LoadRemoteManifestImpl(string manifestAssetBundleName, Action<string> complete)
		{
			WaitLoadAsset wait = AssetManager.LoadRemoteManifest(manifestAssetBundleName);
			yield return wait;
			complete(wait.Error);
			yield break;
		}

		private static IEnumerator UpdateBundleImpl(string bundleName, AssetManager.UpdateDelegate update, Action<string> complete)
		{
			WaitUpdateAssetBundle wait = AssetManager.UpdateBundle(bundleName);
			bool flag = !string.IsNullOrEmpty(wait.Error);
			if (flag)
			{
				complete(wait.Error);
				yield break;
			}
			LinkedListNode<Action> handle = Scheduler.AddFrameListener(delegate
			{
				update(wait.Progress, wait.DownloadSpeed, wait.BytesDownloaded, wait.ContentLength);
			});
			yield return wait;
			Scheduler.RemoveFrameListener(handle);
			complete(wait.Error);
			yield break;
		}

		private static IEnumerator UpdateBundleImpl(string bundleName, Hash128 hash, AssetManager.UpdateDelegate update, Action<string> complete)
		{
			WaitUpdateAssetBundle wait = AssetManager.UpdateBundle(bundleName, hash);
			bool flag = !string.IsNullOrEmpty(wait.Error);
			if (flag)
			{
				complete(wait.Error);
				yield break;
			}
			LinkedListNode<Action> handle = Scheduler.AddFrameListener(delegate
			{
				update(wait.Progress, wait.DownloadSpeed, wait.BytesDownloaded, wait.ContentLength);
			});
			yield return wait;
			Scheduler.RemoveFrameListener(handle);
			complete(wait.Error);
			yield break;
		}

		private static IEnumerator LoadObjectImpl(string assetBundleName, string assetName, Type assetType, Action<Object> complete)
		{
			WaitLoadObject wait = AssetManager.LoadObject(assetBundleName, assetName, assetType);
			yield return wait;
			bool flag = !string.IsNullOrEmpty(wait.Error);
			if (flag)
			{
				AssetManager.logger.LogError(wait.Error);
			}
			complete(wait.GetObject());
			yield break;
		}

		private static IEnumerator LoadObjectSyncImpl(string assetBundleName, string assetName, Type assetType, Action<Object> complete)
		{
			WaitLoadObject wait = AssetManager.LoadObjectSync(assetBundleName, assetName, assetType);
			yield return wait;
			bool flag = !string.IsNullOrEmpty(wait.Error);
			if (flag)
			{
				AssetManager.logger.LogError(wait.Error);
			}
			complete(wait.GetObject());
			yield break;
		}

		private static IEnumerator LoadLevelImpl(string assetBundleName, string levelName, LoadSceneMode loadMode, Action complete, Action<float> progress)
		{
			WaitLoadLevel wait = AssetManager.LoadLevel(assetBundleName, levelName, loadMode);
			bool flag = progress != null;
			if (flag)
			{
				LinkedListNode<Action> handle = Scheduler.AddFrameListener(delegate
				{
					progress(wait.Progress);
				});
				yield return wait;
				Scheduler.RemoveFrameListener(handle);
				handle = null;
			}
			else
			{
				yield return wait;
			}
			bool flag2 = !string.IsNullOrEmpty(wait.Error);
			if (flag2)
			{
				AssetManager.logger.LogError(wait.Error);
			}
			complete();
			yield break;
		}

		private static IEnumerator LoadLevelSyncImpl(string assetBundleName, string levelName, LoadSceneMode loadMode, Action complete)
		{
			WaitLoadLevel wait = AssetManager.LoadLevelSync(assetBundleName, levelName, loadMode);
			yield return wait;
			bool flag = !string.IsNullOrEmpty(wait.Error);
			if (flag)
			{
				AssetManager.logger.LogError(wait.Error);
			}
			complete();
			yield break;
		}

		private static Logger logger = LogSystem.GetLogger("AssetManager");

		private static LinkedListNode<Action> updateHandle;

		private static AssetBundleCache bundleCache = new AssetBundleCache();

		private static AssetBundleDownloader bundleDownloader = new AssetBundleDownloader();

		private static AssetBundleLoader bundleLoader = new AssetBundleLoader(AssetManager.bundleCache, AssetManager.bundleDownloader);

		private static AssetBundleManager bundleManager = new AssetBundleManager(AssetManager.bundleLoader);

		private static AssetBundleFileInfo fileInfo = new AssetBundleFileInfo();

		private static List<WaitLoadAsset> progressWaitings = new List<WaitLoadAsset>();

		private static bool isUseObb;

		public delegate void UpdateDelegate(float progress, int downloadSpeed, int bytesDownloaded, int contentLength);
	}
}
