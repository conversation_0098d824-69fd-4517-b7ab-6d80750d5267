﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class FontPool : Singleton<FontPool>
	{
		public FontPool()
		{
			this.sweepChecker = delegate(AssetID assetID, FontCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						float num = cache.LastFreeTime + cache.ReleaseAfterFree;
						bool flag4 = Time.realtimeSinceStartup > num;
						if (flag4)
						{
							Font @object = cache.GetObject();
							bool flag5 = @object != null;
							if (flag5)
							{
								this.lookup.Remove(@object);
								AssetManager.UnloadAsseBundle(assetID.BundleName);
							}
							flag2 = true;
						}
						else
						{
							flag2 = false;
						}
					}
				}
				return flag2;
			};
			Scheduler.AddFrameListener(new Action(this.SweepCache));
		}

		public float DefaultReleaseAfterFree
		{
			get
			{
				return this.defaultReleaseAfterFree;
			}
			set
			{
				this.defaultReleaseAfterFree = value;
				foreach (KeyValuePair<AssetID, FontCache> keyValuePair in this.caches)
				{
					keyValuePair.Value.DefaultReleaseAfterFree = value;
				}
			}
		}

		public void Load(AssetID assetID, Action<Font> complete)
		{
			Scheduler.RunCoroutine(this.LoadImpl(assetID, complete));
		}

		public WaitLoadFont Load(AssetID assetID)
		{
			FontCache fontCache;
			bool flag = this.caches.TryGetValue(assetID, out fontCache);
			WaitLoadFont waitLoadFont;
			if (flag)
			{
				fontCache.Retain();
				waitLoadFont = new WaitLoadFont(fontCache);
			}
			else
			{
				fontCache = new FontCache(assetID, this.lookup);
				fontCache.LoadObject(assetID);
				fontCache.Retain();
				this.caches.Add(assetID, fontCache);
				waitLoadFont = new WaitLoadFont(fontCache);
			}
			return waitLoadFont;
		}

		public void Free(Font obj, bool destroy = false)
		{
			bool flag = obj == null;
			if (flag)
			{
				FontPool.logger.LogError("Try to free a null Font.");
			}
			else
			{
				FontCache fontCache;
				bool flag2 = !this.lookup.TryGetValue(obj, out fontCache);
				if (flag2)
				{
					FontPool.logger.LogWarning("Try to free an instance {0} not allocated by this pool.", new object[] { obj.name });
				}
				else
				{
					fontCache.Release();
					bool flag3 = destroy && fontCache.ReferenceCount == 0;
					if (flag3)
					{
						AssetID assetID = fontCache.AssetID;
						Font @object = fontCache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						this.caches.Remove(assetID);
					}
				}
			}
		}

		public void Clear()
		{
			this.caches.Clear();
			this.lookup.Clear();
		}

		public void ClearAllUnused()
		{
			this.caches.RemoveAll(delegate(AssetID assetID, FontCache cache)
			{
				bool flag = !string.IsNullOrEmpty(cache.Error);
				bool flag2;
				if (flag)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = cache.ReferenceCount != 0;
					if (flag3)
					{
						flag2 = false;
					}
					else
					{
						Font @object = cache.GetObject();
						bool flag4 = @object != null;
						if (flag4)
						{
							AssetManager.UnloadAsseBundle(assetID.BundleName);
							this.lookup.Remove(@object);
						}
						flag2 = true;
					}
				}
				return flag2;
			});
		}

		private IEnumerator LoadImpl(AssetID assetID, Action<Font> complete)
		{
			WaitLoadFont waitLoad = this.Load(assetID);
			bool keepWaiting = waitLoad.keepWaiting;
			if (keepWaiting)
			{
				yield return waitLoad;
			}
			bool flag = !string.IsNullOrEmpty(waitLoad.Error);
			if (flag)
			{
				FontPool.logger.LogError(waitLoad.Error);
				complete(null);
			}
			else
			{
				complete(waitLoad.LoadedObject);
			}
			yield break;
		}

		private void SweepCache()
		{
			this.caches.RemoveAll(this.sweepChecker);
		}

		private static Logger logger = LogSystem.GetLogger("FontPool");

		private Dictionary<AssetID, FontCache> caches = new Dictionary<AssetID, FontCache>();

		private Dictionary<Font, FontCache> lookup = new Dictionary<Font, FontCache>();

		private float defaultReleaseAfterFree = 30f;

		private Func<AssetID, FontCache, bool> sweepChecker;
	}
}
