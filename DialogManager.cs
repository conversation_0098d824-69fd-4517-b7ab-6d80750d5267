﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class DialogManager
	{
		public static void ShowMessage(string title, string message, string buttonLabel, Action buttonListener)
		{
			DialogManager.GetDialogManager().CallStatic("showMessage", new object[]
			{
				title,
				message,
				buttonLabel,
				new DialogManager.DialogListener(buttonListener)
			});
		}

		public static void ShowConfirm(string title, string message, string confirmLabel, Action confirmListener, string cancelLabel, Action cancelListener)
		{
			DialogManager.GetDialogManager().CallStatic("showConfirm", new object[]
			{
				title,
				message,
				confirmLabel,
				new DialogManager.DialogListener(confirmListener),
				cancelLabel,
				new DialogManager.DialogListener(cancelListener)
			});
		}

		private static AndroidJavaClass GetDialogManager()
		{
			bool flag = DialogManager.dialogManager == null;
			if (flag)
			{
				DialogManager.dialogManager = new AndroidJavaClass("com.winunet.and.ConverMgr");
			}
			return DialogManager.dialogManager;
		}

		private static AndroidJavaClass dialogManager;

		private class DialogListener : AndroidJavaProxy
		{
			public DialogListener(Action callback)
				: base("com.winunet.and.ConverListener")
			{
				this.callback = callback;
			}

			private void onPick()
			{
				SdkScheduler.PostTask(delegate
				{
					this.callback();
				});
			}

			private Action callback;
		}
	}
}
