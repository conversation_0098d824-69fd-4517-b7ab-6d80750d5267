﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{AC7D2309-D28E-426A-A1FA-19558B2F1C9D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Nirvana</RootNamespace>
    <AssemblyName>NirvanaNative</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="UnityEngine" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AudioGVoice.cs" />
    <Compile Include="AudioPlayer.cs" />
    <Compile Include="AudioRecorder.cs" />
    <Compile Include="ChannelAgent.cs" />
    <Compile Include="ChannelAgentEvent.cs" />
    <Compile Include="ChannelUserInfo.cs" />
    <Compile Include="ClipboardTool.cs" />
    <Compile Include="DeviceInfo.cs" />
    <Compile Include="DeviceTool.cs" />
    <Compile Include="DialogManager.cs" />
    <Compile Include="ImagePicker.cs" />
    <Compile Include="ImageTool.cs" />
    <Compile Include="LocalNotification.cs" />
    <Compile Include="NetWorkState.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SdkScheduler.cs" />
    <Compile Include="WebView.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>