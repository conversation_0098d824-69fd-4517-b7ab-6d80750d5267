﻿using System;
using System.Net.NetworkInformation;

namespace Nirvana
{
	public static class NetworkInfo
	{
		public static string GetMacAddress()
		{
			NetworkInterface[] allNetworkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
			foreach (NetworkInterface networkInterface in allNetworkInterfaces)
			{
				PhysicalAddress physicalAddress = networkInterface.GetPhysicalAddress();
				string text = physicalAddress.ToString();
				bool flag = !string.IsNullOrEmpty(text);
				if (flag)
				{
					return text;
				}
			}
			return string.Empty;
		}
	}
}
