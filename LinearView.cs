﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class LinearView : ListView
	{
		protected override Vector2 CalculateContentSize(int cellCount)
		{
			bool flag = cellCount == 0;
			Vector2 vector;
			if (flag)
			{
				vector = Vector2.zero;
			}
			else
			{
				bool flag2 = this.cellSizeArray == null || this.cellSizeArray.Length < cellCount;
				if (flag2)
				{
					this.cellSizeArray = new float[cellCount];
				}
				bool flag3 = this.cellOffsetArray == null || this.cellOffsetArray.Length < cellCount;
				if (flag3)
				{
					this.cellOffsetArray = new float[cellCount];
				}
				bool flag4 = this.scrollDirection == LinearView.ScrollDirectionEnum.Horizontal;
				if (flag4)
				{
					float num = 0f;
					float num2 = 0f;
					for (int i = 0; i < cellCount; i++)
					{
						Vector2 vector2 = base.GetCellSizeDel(i);
						this.cellSizeArray[i] = vector2.x;
						this.cellOffsetArray[i] = num2;
						num2 += vector2.x + this.spacing;
						bool flag5 = num < vector2.y;
						if (flag5)
						{
							num = vector2.y;
						}
					}
					vector = new Vector2(num2, num);
				}
				else
				{
					float num3 = 0f;
					float num4 = 0f;
					for (int j = 0; j < cellCount; j++)
					{
						Vector2 vector3 = base.GetCellSizeDel(j);
						this.cellSizeArray[j] = vector3.y;
						this.cellOffsetArray[j] = num4;
						num4 += vector3.y + this.spacing;
						bool flag6 = num3 < vector3.x;
						if (flag6)
						{
							num3 = vector3.x;
						}
					}
					vector = new Vector2(num3, num4);
				}
			}
			return vector;
		}

		protected override void CalculateCurrentActiveCellRange(Vector2 scrollPosition, out int startIndex, out int endIndex)
		{
			bool flag = this.scrollDirection == LinearView.ScrollDirectionEnum.Horizontal;
			if (flag)
			{
				float num = base.ScrollContent.rect.width - base.ScrollView.rect.width;
				float num2 = scrollPosition.x * num;
				float num3 = num2;
				float num4 = num3 + base.ScrollView.rect.width;
				startIndex = this.GetCellIndexAtPosition(num3, 0, base.CellCount);
				endIndex = this.GetCellIndexAtPosition(num4, 0, base.CellCount);
			}
			else
			{
				float num5 = base.ScrollContent.rect.height - base.ScrollView.rect.height;
				float num6 = scrollPosition.y * num5;
				float num7 = num6;
				float num8 = num7 + base.ScrollView.rect.height;
				startIndex = this.GetCellIndexAtPosition(num7, 0, base.CellCount);
				endIndex = this.GetCellIndexAtPosition(num8, 0, base.CellCount);
			}
			bool flag2 = startIndex > 0;
			if (flag2)
			{
				startIndex--;
			}
		}

		protected override void LayoutCell(GameObject cell, int index)
		{
			RectTransform rectTransform = (RectTransform)cell.transform;
			rectTransform.anchorMin = Vector2.zero;
			rectTransform.anchorMax = Vector2.zero;
			rectTransform.pivot = Vector2.zero;
			bool flag = this.scrollDirection == LinearView.ScrollDirectionEnum.Horizontal;
			if (flag)
			{
				rectTransform.pivot = new Vector2(0f, 0f);
				rectTransform.anchoredPosition = new Vector2(this.cellOffsetArray[index], 0f);
				rectTransform.sizeDelta = new Vector2(this.cellSizeArray[index], rectTransform.sizeDelta.y);
			}
			else
			{
				rectTransform.pivot = new Vector2(0f, 0f);
				rectTransform.anchoredPosition = new Vector2(0f, this.cellOffsetArray[index]);
				rectTransform.sizeDelta = new Vector2(rectTransform.sizeDelta.x, this.cellSizeArray[index]);
			}
		}

		protected override Vector2 GetCellPositionAtIndex(int index)
		{
			bool flag = this.scrollDirection == LinearView.ScrollDirectionEnum.Horizontal;
			Vector2 vector;
			if (flag)
			{
				vector = new Vector2(this.cellOffsetArray[index], 0f);
			}
			else
			{
				vector = new Vector2(0f, this.cellOffsetArray[index]);
			}
			return vector;
		}

		protected override void UpdateSnapping()
		{
			bool flag = !this.snapping;
			if (flag)
			{
			}
		}

		private int GetCellIndexAtPosition(float position, int startIndex, int endIndex)
		{
			bool flag = startIndex >= endIndex;
			int num;
			if (flag)
			{
				num = startIndex;
			}
			else
			{
				int num2 = (startIndex + endIndex) / 2;
				bool flag2 = this.cellOffsetArray[num2] >= position;
				if (flag2)
				{
					num = this.GetCellIndexAtPosition(position, startIndex, num2);
				}
				else
				{
					num = this.GetCellIndexAtPosition(position, num2 + 1, endIndex);
				}
			}
			return num;
		}

		[SerializeField]
		[Tooltip("The element scroll direction.")]
		private LinearView.ScrollDirectionEnum scrollDirection;

		[SerializeField]
		[Tooltip("The space between each element.")]
		private float spacing;

		[SerializeField]
		[Tooltip("Whether snapping the element in the border.")]
		private bool snapping;

		private float[] cellSizeArray;

		private float[] cellOffsetArray;

		public enum ScrollDirectionEnum
		{
			Horizontal,
			Vertical
		}
	}
}
