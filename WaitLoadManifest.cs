﻿using System;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	internal sealed class WaitLoadManifest : WaitLoadObjectFull
	{
		internal WaitLoadManifest(AssetBundleManager assetBundleManager, string assetBundleName, string assetName, Type type)
			: base(assetBundleManager, assetBundleName, assetName, type)
		{
		}

		public override bool keepWaiting
		{
			get
			{
				bool flag = base.Request == null && base.Error != null;
				bool flag2;
				if (flag)
				{
					flag2 = false;
				}
				else
				{
					bool flag3 = base.Request != null && base.Request.isDone;
					flag2 = !flag3;
				}
				return flag2;
			}
		}

		internal override bool Update()
		{
			base.Update();
			bool flag = base.Error != null;
			bool flag2;
			if (flag)
			{
				base.AssetBundleManager.DestroyAsseBundle(base.AssetBundleName, false);
				flag2 = false;
			}
			else
			{
				bool flag3 = base.Request != null && base.Request.isDone;
				if (flag3)
				{
					AssetBundleManifest @object = base.GetObject<AssetBundleManifest>();
					base.AssetBundleManager.DestroyAsseBundle(base.AssetBundleName, false);
					Assert.IsNotNull<AssetBundleManifest>(@object);
					base.AssetBundleManager.Manifest = @object;
					flag2 = false;
				}
				else
				{
					flag2 = true;
				}
			}
			return flag2;
		}
	}
}
