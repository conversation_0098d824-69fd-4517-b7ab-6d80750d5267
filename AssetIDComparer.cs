﻿using System;
using System.Collections.Generic;

namespace Nirvana
{
	public sealed class AssetIDComparer : IEqualityComparer<AssetID>
	{
		public static AssetIDComparer Default
		{
			get
			{
				bool flag = AssetIDComparer.defaultComparer == null;
				if (flag)
				{
					AssetIDComparer.defaultComparer = new AssetIDComparer();
				}
				return AssetIDComparer.defaultComparer;
			}
		}

		public bool Equals(AssetID x, AssetID y)
		{
			return x.Equals(y);
		}

		public int GetHashCode(AssetID obj)
		{
			return obj.GetHashCode();
		}

		private static volatile AssetIDComparer defaultComparer;
	}
}
