﻿using System;
using System.Collections;
using System.Collections.Generic;

namespace Nirvana
{
	public sealed class PriorityQueue<T> : IEnumerable<T>, IEnumerable
	{
		public PriorityQueue()
			: this(null)
		{
		}

		public PriorityQueue(int capacity)
			: this(capacity, null)
		{
		}

		public PriorityQueue(IComparer<T> comparer)
			: this(16, comparer)
		{
		}

		public PriorityQueue(int capacity, IComparer<T> comparer)
		{
			IComparer<T> comparer2;
			if (comparer != null)
			{
				comparer2 = comparer;
			}
			else
			{
				IComparer<T> @default = Comparer<T>.Default;
				comparer2 = @default;
			}
			this.comparer = comparer2;
			this.heap = new T[capacity];
		}

		public int Count { get; private set; }

		public T this[int index]
		{
			get
			{
				return this.heap[index];
			}
		}

		public IEnumerator<T> GetEnumerator()
		{
			return new PriorityQueue<T>.Enumerator(this.heap, this.Count);
		}

		IEnumerator IEnumerable.GetEnumerator()
		{
			return this.GetEnumerator();
		}

		public void Clear()
		{
			this.Count = 0;
		}

		public void Push(T v)
		{
			bool flag = this.Count >= this.heap.Length;
			if (flag)
			{
				Array.Resize<T>(ref this.heap, this.Count * 2);
			}
			this.heap[this.Count] = v;
			int count = this.Count;
			this.Count = count + 1;
			this.SiftUp(count);
		}

		public T Pop()
		{
			T t = this.Top();
			T[] array = this.heap;
			int num = 0;
			T[] array2 = this.heap;
			int num2 = this.Count - 1;
			this.Count = num2;
			array[num] = array2[num2];
			bool flag = this.Count > 0;
			if (flag)
			{
				this.SiftDown(0);
			}
			return t;
		}

		public T Top()
		{
			bool flag = this.Count > 0;
			if (flag)
			{
				return this.heap[0];
			}
			throw new InvalidOperationException("The PriorityQueue is empty.");
		}

		private void SiftUp(int n)
		{
			T t = this.heap[n];
			int num = n / 2;
			while (n > 0 && this.comparer.Compare(t, this.heap[num]) > 0)
			{
				this.heap[n] = this.heap[num];
				n = num;
				num /= 2;
			}
			this.heap[n] = t;
		}

		private void SiftDown(int n)
		{
			T t = this.heap[n];
			for (int i = n * 2; i < this.Count; i *= 2)
			{
				bool flag = i + 1 < this.Count && this.comparer.Compare(this.heap[i + 1], this.heap[i]) > 0;
				if (flag)
				{
					i++;
				}
				bool flag2 = this.comparer.Compare(t, this.heap[i]) >= 0;
				if (flag2)
				{
					break;
				}
				this.heap[n] = this.heap[i];
				n = i;
			}
			this.heap[n] = t;
		}

		private IComparer<T> comparer;

		private T[] heap;

		public struct Enumerator : IEnumerator<T>, IEnumerator, IDisposable
		{
			internal Enumerator(T[] heap, int count)
			{
				this.heap = heap;
				this.count = count;
				this.index = -1;
			}

			public T Current
			{
				get
				{
					return this.heap[this.index];
				}
			}

			object IEnumerator.Current
			{
				get
				{
					return this.Current;
				}
			}

			public void Dispose()
			{
			}

			public void Reset()
			{
				this.index = -1;
			}

			public bool MoveNext()
			{
				bool flag;
				if (this.index <= this.count)
				{
					int num = this.index + 1;
					this.index = num;
					flag = num < this.count;
				}
				else
				{
					flag = false;
				}
				return flag;
			}

			private readonly T[] heap;

			private readonly int count;

			private int index;
		}
	}
}
