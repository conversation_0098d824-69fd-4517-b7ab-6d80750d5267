﻿using System;
using UnityEngine;

namespace Nirvana
{
	[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
	public sealed class FootprintMesh : MonoBehaviour
	{
		internal void Initialize(int maxCount, Material material)
		{
			this.maxFootprints = maxCount;
			Renderer component = base.GetComponent<Renderer>();
			bool flag = component != null;
			if (flag)
			{
				component.sharedMaterial = material;
			}
			this.vertices = new Vector3[4 * this.maxFootprints];
			this.normals = new Vector3[4 * this.maxFootprints];
			this.uvs = new Vector2[4 * this.maxFootprints];
			this.times = new Vector2[4 * this.maxFootprints];
			this.triangles = new int[6 * this.maxFootprints];
			bool flag2 = this.mesh == null;
			if (flag2)
			{
				MeshFilter component2 = base.GetComponent<MeshFilter>();
				bool flag3 = component2.mesh == null;
				if (flag3)
				{
					component2.mesh = new Mesh();
				}
				this.mesh = component2.mesh;
				this.mesh.name = "Footprints Mesh";
			}
		}

		internal void AddFootprint(Vector3 pos, Vector3 fwd, Vector3 rht, Footprint footprint)
		{
			Vector2 size = footprint.Size;
			Vector3 vector = Vector3.up * footprint.GroundOffset;
			int num = 4 * this.footprintIndex;
			Vector3 vector2 = pos + fwd * size.y * 0.5f + -rht * size.x * 0.5f;
			this.vertices[num] = vector2 + vector;
			this.normals[num] = Vector3.up;
			int num2 = 4 * this.footprintIndex + 1;
			Vector3 vector3 = pos + fwd * size.y * 0.5f + rht * size.x * 0.5f;
			this.vertices[num2] = vector3 + vector;
			this.normals[num2] = Vector3.up;
			int num3 = 4 * this.footprintIndex + 2;
			Vector3 vector4 = pos + -fwd * size.y * 0.5f + -rht * size.x * 0.5f;
			this.vertices[num3] = vector4 + vector;
			this.normals[num3] = Vector3.up;
			int num4 = 4 * this.footprintIndex + 3;
			Vector3 vector5 = pos + -fwd * size.y * 0.5f + rht * size.x * 0.5f;
			this.vertices[num4] = vector5 + vector;
			this.normals[num4] = Vector3.up;
			int num5 = 4 * this.footprintIndex;
			this.uvs[num5] = footprint.UV1;
			this.uvs[num5 + 1] = footprint.UV2;
			this.uvs[num5 + 2] = footprint.UV3;
			this.uvs[num5 + 3] = footprint.UV4;
			Vector2 vector6;
			vector6..ctor(Time.timeSinceLevelLoad + footprint.Duration, footprint.Fadeout);
			this.times[num5] = vector6;
			this.times[num5 + 1] = vector6;
			this.times[num5 + 2] = vector6;
			this.times[num5 + 3] = vector6;
			int num6 = 6 * this.footprintIndex;
			int num7 = 4 * this.footprintIndex;
			this.triangles[num6] = num7;
			this.triangles[num6 + 1] = num7 + 1;
			this.triangles[num6 + 2] = num7 + 2;
			this.triangles[num6 + 3] = num7 + 2;
			this.triangles[num6 + 4] = num7 + 1;
			this.triangles[num6 + 5] = num7 + 3;
			this.footprintIndex++;
			bool flag = this.footprintIndex >= this.maxFootprints;
			if (flag)
			{
				this.footprintIndex = 0;
			}
			this.mesh.vertices = this.vertices;
			this.mesh.normals = this.normals;
			this.mesh.uv = this.uvs;
			this.mesh.uv2 = this.times;
			this.mesh.triangles = this.triangles;
			this.mesh.RecalculateBounds();
		}

		private Mesh mesh;

		private Vector3[] vertices;

		private Vector3[] normals;

		private Vector2[] uvs;

		private Vector2[] times;

		private int[] triangles;

		private int footprintIndex = 0;

		private int maxFootprints = 64;
	}
}
