﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class VariableNameAttribute : PropertyAttribute
	{
		public VariableNameAttribute()
		{
			this.mask = 31;
		}

		public VariableNameAttribute(UIVariableType t1)
		{
			this.mask = 1 << (int)t1;
		}

		public VariableNameAttribute(UIVariableType t1, UIVariableType t2)
		{
			this.mask = (1 << (int)t1) | (1 << (int)t2);
		}

		public VariableNameAttribute(UIVariableType t1, UIVariableType t2, UIVariableType t3)
		{
			this.mask = (1 << (int)t1) | (1 << (int)t2) | (1 << (int)t3);
		}

		public VariableNameAttribute(UIVariableType t1, UIVariableType t2, UIVariableType t3, UIVariableType t4)
		{
			this.mask = (1 << (int)t1) | (1 << (int)t2) | (1 << (int)t3) | (1 << (int)t4);
		}

		public bool IsValid(UIVariableType type)
		{
			return (this.mask & (1 << (int)type)) != 0;
		}

		private int mask;
	}
}
