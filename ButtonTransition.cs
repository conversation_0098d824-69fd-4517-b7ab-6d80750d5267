﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/Button Transition")]
	[RequireComponent(typeof(ButtonEx))]
	[ExecuteInEditMode]
	public sealed class ButtonTransition : MonoBehaviour
	{
		private void Awake()
		{
			this.button = base.GetComponent<ButtonEx>();
			this.button.ToNormalEvent += this.ToNormalHandler;
			this.button.ToHighlightedEvent += this.ToHighlightedHandler;
			this.button.ToPressedEvent += this.ToPressedHandler;
			this.button.ToDisabledEvent += this.ToDisabledHandler;
		}

		private void OnEnable()
		{
			bool interactable = this.button.interactable;
			if (interactable)
			{
				this.ToNormalHandler();
			}
			else
			{
				this.ToDisabledHandler();
			}
		}

		private void OnDestroy()
		{
			this.button.ToNormalEvent -= this.ToNormalHandler;
			this.button.ToHighlightedEvent -= this.ToHighlightedHandler;
			this.button.ToPressedEvent -= this.ToPressedHandler;
			this.button.ToDisabledEvent -= this.ToDisabledHandler;
		}

		private void ToNormalHandler()
		{
			bool flag = this.gryascale != null;
			if (flag)
			{
				this.gryascale.GrayScale = this.normalGray;
			}
		}

		private void ToHighlightedHandler()
		{
			bool flag = this.gryascale != null;
			if (flag)
			{
				this.gryascale.GrayScale = this.highlightedGray;
			}
		}

		private void ToPressedHandler()
		{
			bool flag = this.gryascale != null;
			if (flag)
			{
				this.gryascale.GrayScale = this.pressedGray;
			}
		}

		private void ToDisabledHandler()
		{
			bool flag = this.gryascale != null;
			if (flag)
			{
				this.gryascale.GrayScale = this.disabledGray;
			}
		}

		[SerializeField]
		private UIGrayscale gryascale;

		[SerializeField]
		[Range(0f, 255f)]
		private int normalGray = 255;

		[SerializeField]
		[Range(0f, 255f)]
		private int highlightedGray = 255;

		[SerializeField]
		[Range(0f, 255f)]
		private int pressedGray = 255;

		[SerializeField]
		[Range(0f, 255f)]
		private int disabledGray = 0;

		private ButtonEx button;
	}
}
