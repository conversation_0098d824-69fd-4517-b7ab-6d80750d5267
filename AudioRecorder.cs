﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class AudioRecorder
	{
		public static bool Start(Action<bool, int, string> complete = null)
		{
			AudioRecorder.audioRecorderListener.CompleteDelegate = complete;
			return AudioRecorder.GetAudioRecorder().CallStatic<bool>("start", new object[0]);
		}

		public static string Stop()
		{
			return AudioRecorder.GetAudioRecorder().CallStatic<string>("stop", new object[0]);
		}

		public static bool StartRecorder(Action<bool, int, string> complete = null)
		{
			AudioRecorder.audioRecorderListener.CompleteDelegate = complete;
			return AudioRecorder.GetAudioRecorder().CallStatic<bool>("startRecorder", new object[0]);
		}

		public static void StopRecorder()
		{
			AudioRecorder.GetAudioRecorder().CallStatic("stopRecorder", new object[0]);
		}

		private static AndroidJavaClass GetAudioRecorder()
		{
			bool flag = AudioRecorder.audioRecorderClass == null;
			if (flag)
			{
				AudioRecorder.audioRecorderClass = new AndroidJavaClass("com.winunet.and.SoundRecorder");
				AudioRecorder.audioRecorderClass.CallStatic("setListener", new object[] { AudioRecorder.audioRecorderListener });
			}
			return AudioRecorder.audioRecorderClass;
		}

		private static AndroidJavaClass audioRecorderClass;

		private static AudioRecorder.AudioRecorderListener audioRecorderListener = new AudioRecorder.AudioRecorderListener();

		private class AudioRecorderListener : AndroidJavaProxy
		{
			public AudioRecorderListener()
				: base("com.winunet.and.SoundRecorderListener")
			{
			}

			public Action<bool, int, string> CompleteDelegate { get; set; }

			private void onComplete(bool status, int error, string msg)
			{
				SdkScheduler.PostTask(delegate
				{
					bool flag = this.CompleteDelegate != null;
					if (flag)
					{
						this.CompleteDelegate(status, error, msg);
					}
				});
			}
		}
	}
}
