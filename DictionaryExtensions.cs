﻿using System;
using System.Collections.Generic;

namespace Nirvana
{
	public static class DictionaryExtensions
	{
		public static void RemoveAll<K, V>(this Dictionary<K, V> dic, Func<K, V, bool> filter)
		{
			List<K> sweepList = DictionaryExtensions.RemoveList<K>.SweepList;
			foreach (KeyValuePair<K, V> keyValuePair in dic)
			{
				bool flag = filter(keyValuePair.Key, keyValuePair.Value);
				if (flag)
				{
					sweepList.Add(keyValuePair.Key);
				}
			}
			for (int i = 0; i < sweepList.Count; i++)
			{
				dic.Remove(sweepList[i]);
			}
			sweepList.Clear();
		}

		private static class RemoveList<T>
		{
			public static List<T> SweepList
			{
				get
				{
					return DictionaryExtensions.RemoveList<T>.sweepList;
				}
			}

			private static List<T> sweepList = new List<T>();
		}
	}
}
