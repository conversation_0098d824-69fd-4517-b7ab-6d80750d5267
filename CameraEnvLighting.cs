﻿using System;
using UnityEngine;
using UnityEngine.Rendering;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Render/Camera Environment Lighting")]
	[RequireComponent(typeof(Camera))]
	public sealed class CameraEnvLighting : MonoBehaviour
	{
		public AmbientMode AmbientMode
		{
			get
			{
				return this.ambientMode;
			}
			set
			{
				this.ambientMode = value;
			}
		}

		public float AmbientIntensity
		{
			get
			{
				return this.ambientIntensity;
			}
			set
			{
				this.ambientIntensity = value;
			}
		}

		public Color AmbientLight
		{
			get
			{
				return this.ambientLight;
			}
			set
			{
				this.ambientLight = value;
			}
		}

		public Color AmbientSkyColor
		{
			get
			{
				return this.ambientSkyColor;
			}
			set
			{
				this.ambientSkyColor = value;
			}
		}

		public Color AmbientEquatorColor
		{
			get
			{
				return this.ambientEquatorColor;
			}
			set
			{
				this.ambientEquatorColor = value;
			}
		}

		public Color AmbientGroundColor
		{
			get
			{
				return this.ambientGroundColor;
			}
			set
			{
				this.ambientGroundColor = value;
			}
		}

		public Cubemap CustomReflection
		{
			get
			{
				return this.customReflection;
			}
			set
			{
				this.customReflection = value;
			}
		}

		public float ReflectionIntensity
		{
			get
			{
				return this.reflectionIntensity;
			}
			set
			{
				this.reflectionIntensity = value;
			}
		}

		public bool Fog
		{
			get
			{
				return this.fog;
			}
			set
			{
				this.fog = value;
			}
		}

		private void OnPreRender()
		{
		}

		private void OnPostRender()
		{
		}

		[SerializeField]
		private DefaultReflectionMode reflectionMode = 1;

		[SerializeField]
		private AmbientMode ambientMode = 1;

		[SerializeField]
		private float ambientIntensity;

		[SerializeField]
		private Color ambientLight;

		[SerializeField]
		private Color ambientSkyColor;

		[SerializeField]
		private Color ambientEquatorColor;

		[SerializeField]
		private Color ambientGroundColor;

		[SerializeField]
		private Cubemap customReflection;

		[SerializeField]
		private float reflectionIntensity = 1f;

		[SerializeField]
		private bool fog;

		private DefaultReflectionMode originReflectionMode;

		private AmbientMode originAmbientMode;

		private float originAmbientIntensity;

		private Color originAmbientLight;

		private Color originAmbientSkyColor;

		private Color originAmbientEquatorColor;

		private Color originAmbientGroundColorr;

		private Cubemap originCustomReflection;

		private float originalReflectionIntensity;

		private bool originFog;
	}
}
