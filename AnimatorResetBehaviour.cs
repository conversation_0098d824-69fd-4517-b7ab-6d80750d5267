﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class AnimatorResetBehaviour : StateMachineBehaviour
	{
		public override void OnStateEnter(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
		{
			bool flag = this.enterResets != null;
			if (flag)
			{
				this.enterResetKeys = new int[this.enterResets.Length];
				for (int i = 0; i < this.enterResetKeys.Length; i++)
				{
					this.enterResetKeys[i] = Animator.StringToHash(this.enterResets[i]);
				}
				this.enterResets = null;
			}
			bool flag2 = this.enterResetKeys == null;
			if (!flag2)
			{
				foreach (int num in this.enterResetKeys)
				{
					animator.ResetTrigger(num);
				}
			}
		}

		public override void OnStateExit(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
		{
			bool flag = this.exitResets != null;
			if (flag)
			{
				this.exitResetKeys = new int[this.exitResets.Length];
				for (int i = 0; i < this.exitResetKeys.Length; i++)
				{
					this.exitResetKeys[i] = Animator.StringToHash(this.exitResets[i]);
				}
				this.exitResets = null;
			}
			bool flag2 = this.exitResetKeys == null;
			if (!flag2)
			{
				foreach (int num in this.exitResetKeys)
				{
					animator.ResetTrigger(num);
				}
			}
		}

		[SerializeField]
		[Tooltip("The reset triggers when enter this state.")]
		private string[] enterResets;

		[SerializeField]
		[Tooltip("The reset triggers when leave this state.")]
		private string[] exitResets;

		private int[] enterResetKeys;

		private int[] exitResetKeys;
	}
}
