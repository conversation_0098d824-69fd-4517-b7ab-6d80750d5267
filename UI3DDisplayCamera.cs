﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class UI3DDisplayCamera : MonoBehaviour
	{
		internal GameObject DisplayObject
		{
			get
			{
				return this.displayObject;
			}
			set
			{
				bool flag = this.displayObject != null && this.displayObject != value;
				if (flag)
				{
					this.ResetRenderer(this.displayObject.transform);
					this.RemoveFromCache(this.displayObject.transform);
				}
				this.displayObject = value;
			}
		}

		internal int DisplayLayer { get; set; }

		private void ResetRenderer(Transform transform)
		{
			Renderer component = transform.GetComponent<Renderer>();
			bool flag = component != null;
			if (flag)
			{
				UI3DDisplayRecord component2 = component.GetComponent<UI3DDisplayRecord>();
				bool flag2 = component2 != null;
				if (flag2)
				{
					component2.ManualDestroy();
					Object.Destroy(component2);
				}
				UI3DDisplayCamera.RendererItem rendererItem;
				bool flag3 = this.cache.TryGetValue(transform, out rendererItem);
				if (flag3)
				{
					rendererItem.Record = null;
				}
			}
			for (int i = 0; i < transform.childCount; i++)
			{
				Transform child = transform.GetChild(i);
				this.ResetRenderer(child);
			}
		}

		private void RemoveFromCache(Transform transform)
		{
			bool flag = this.cache.ContainsKey(transform);
			if (flag)
			{
				this.cache.Remove(transform);
			}
			for (int i = 0; i < transform.childCount; i++)
			{
				Transform child = transform.GetChild(i);
				this.RemoveFromCache(child);
			}
		}

		private UI3DDisplayRecord AddRecord(Transform transform, Renderer renderer)
		{
			UI3DDisplayRecord ui3DDisplayRecord = transform.GetComponent<UI3DDisplayRecord>();
			bool flag = ui3DDisplayRecord == null;
			if (flag)
			{
				ui3DDisplayRecord = renderer.gameObject.AddComponent<UI3DDisplayRecord>();
				ui3DDisplayRecord.hideFlags = 52;
				ui3DDisplayRecord.Initialize(renderer, this);
				ui3DDisplayRecord.gameObject.layer = this.DisplayLayer;
			}
			return ui3DDisplayRecord;
		}

		private void UpdateCache(Transform transform)
		{
			UI3DDisplayCamera.RendererItem rendererItem;
			bool flag = !this.cache.TryGetValue(transform, out rendererItem);
			if (flag)
			{
				rendererItem = new UI3DDisplayCamera.RendererItem();
				Renderer component = transform.GetComponent<Renderer>();
				bool flag2 = component != null && component.enabled;
				if (flag2)
				{
					rendererItem.Renderer = component;
					rendererItem.Record = this.AddRecord(transform, component);
				}
				this.cache.Add(transform, rendererItem);
			}
			else
			{
				bool flag3 = rendererItem.Renderer != null && rendererItem.Record == null;
				if (flag3)
				{
					rendererItem.Record = this.AddRecord(transform, rendererItem.Renderer);
				}
			}
			rendererItem.IsExisted = true;
			for (int i = 0; i < transform.childCount; i++)
			{
				Transform child = transform.GetChild(i);
				bool flag4 = !child.gameObject.activeSelf;
				if (!flag4)
				{
					this.UpdateCache(child);
				}
			}
		}

		private void OnPreCull()
		{
			bool flag = this.DisplayObject != null;
			if (flag)
			{
				foreach (KeyValuePair<Transform, UI3DDisplayCamera.RendererItem> keyValuePair in this.cache)
				{
					keyValuePair.Value.IsExisted = false;
				}
				this.UpdateCache(this.DisplayObject.transform);
				this.cache.RemoveAll((Transform k, UI3DDisplayCamera.RendererItem v) => !v.IsExisted);
				foreach (KeyValuePair<Transform, UI3DDisplayCamera.RendererItem> keyValuePair2 in this.cache)
				{
					bool flag2 = keyValuePair2.Value.Renderer != null && keyValuePair2.Value.Record != null;
					if (flag2)
					{
						keyValuePair2.Value.Renderer.gameObject.layer = this.DisplayLayer;
					}
				}
			}
		}

		private void OnDisable()
		{
			bool flag = this.DisplayObject != null;
			if (flag)
			{
				this.ResetRenderer(this.DisplayObject.transform);
			}
		}

		private GameObject displayObject;

		private Dictionary<Transform, UI3DDisplayCamera.RendererItem> cache = new Dictionary<Transform, UI3DDisplayCamera.RendererItem>();

		private class RendererItem
		{
			public bool IsExisted { get; set; }

			public Renderer Renderer { get; set; }

			public UI3DDisplayRecord Record { get; set; }
		}
	}
}
