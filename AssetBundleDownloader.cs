﻿using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace Nirvana
{
	internal class AssetBundleDownloader
	{
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		internal event Action<string> DownloadStartEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		internal event Action<string> DownloadFinishEvent;

		internal int WorkerCount
		{
			get
			{
				return this.workerCount;
			}
			set
			{
				this.workerCount = value;
			}
		}

		internal void Start(AssetBundleLoadTask task)
		{
			bool flag = this.workingList.Count < this.workerCount;
			if (flag)
			{
				this.StartTask(task);
			}
			else
			{
				this.pendingQueue.Enqueue(task);
			}
		}

		internal void Complete(AssetBundleLoadTask task, bool isTryReDownload = false)
		{
			this.workingList.Remove(task);
			bool flag = this.DownloadFinishEvent != null;
			if (flag)
			{
				this.DownloadFinishEvent(task.Url);
			}
			if (isTryReDownload)
			{
				this.StartTask(task);
			}
			else
			{
				while (this.workingList.Count < this.workerCount && this.pendingQueue.Count > 0)
				{
					AssetBundleLoadTask assetBundleLoadTask = this.pendingQueue.Dequeue();
					this.StartTask(assetBundleLoadTask);
				}
			}
		}

		private void StartTask(AssetBundleLoadTask task)
		{
			task.StartDownload();
			this.workingList.AddLast(task);
			bool flag = this.DownloadStartEvent != null;
			if (flag)
			{
				this.DownloadStartEvent(task.Url);
			}
		}

		private int workerCount = 4;

		private Queue<AssetBundleLoadTask> pendingQueue = new Queue<AssetBundleLoadTask>();

		private LinkedList<AssetBundleLoadTask> workingList = new LinkedList<AssetBundleLoadTask>();
	}
}
