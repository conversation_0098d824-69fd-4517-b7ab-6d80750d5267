﻿using System;
using System.Diagnostics;
using UnityEngine;

namespace Nirvana
{
	[Serializable]
	public sealed class UIVariable
	{
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Action OnValueChanged;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Action OnValueInitialized;

		public string Name
		{
			get
			{
				return this.name;
			}
		}

		public UIVariableType Type
		{
			get
			{
				return this.type;
			}
		}

		public object ValueObject
		{
			get
			{
				object obj;
				switch (this.type)
				{
				case UIVariableType.Boolean:
					obj = this.booleanValue;
					break;
				case UIVariableType.Integer:
					obj = this.integerValue;
					break;
				case UIVariableType.Float:
					obj = this.floatValue;
					break;
				case UIVariableType.String:
					obj = this.stringValue;
					break;
				case UIVariableType.Asset:
					obj = this.assetValue;
					break;
				default:
					obj = null;
					break;
				}
				return obj;
			}
		}

		public bool GetBoolean()
		{
			return this.booleanValue;
		}

		public int GetInteger()
		{
			return (int)this.integerValue;
		}

		public float GetFloat()
		{
			return this.floatValue;
		}

		public string GetString()
		{
			return this.stringValue;
		}

		public AssetID GetAsset()
		{
			return this.assetValue;
		}

		public void InitBoolean(bool value)
		{
			bool flag = this.booleanValue != value;
			if (flag)
			{
				this.booleanValue = value;
				this.TriggerValueInitialized();
			}
		}

		public void InitInteger(long value)
		{
			bool flag = this.integerValue != value;
			if (flag)
			{
				this.integerValue = value;
				this.TriggerValueInitialized();
			}
		}

		public void InitFloat(float value)
		{
			bool flag = this.floatValue != value;
			if (flag)
			{
				this.floatValue = value;
				this.TriggerValueInitialized();
			}
		}

		public void InitString(string value)
		{
			bool flag = this.stringValue != value;
			if (flag)
			{
				this.stringValue = value;
				this.TriggerValueInitialized();
			}
		}

		public void SetBoolean(bool value)
		{
			bool flag = this.booleanValue != value;
			if (flag)
			{
				this.booleanValue = value;
				this.TriggerValueChanged();
			}
		}

		public void SetInteger(long value)
		{
			bool flag = this.integerValue != value;
			if (flag)
			{
				this.integerValue = value;
				this.TriggerValueChanged();
			}
		}

		public void SetFloat(float value)
		{
			bool flag = this.floatValue != value;
			if (flag)
			{
				this.floatValue = value;
				this.TriggerValueChanged();
			}
		}

		public void SetString(string value)
		{
			bool flag = this.stringValue != value;
			if (flag)
			{
				this.stringValue = value;
				this.TriggerValueChanged();
			}
		}

		public void InitValue(bool value)
		{
			switch (this.type)
			{
			case UIVariableType.Boolean:
				this.InitBoolean(value);
				break;
			case UIVariableType.Integer:
				this.InitInteger(value ? 1L : 0L);
				break;
			case UIVariableType.Float:
				this.InitFloat(value ? 1f : 0f);
				break;
			case UIVariableType.String:
				this.InitString(value.ToString());
				break;
			}
		}

		public void InitValue(long value)
		{
			switch (this.type)
			{
			case UIVariableType.Boolean:
				this.InitBoolean(value != 0L);
				break;
			case UIVariableType.Integer:
				this.InitInteger(value);
				break;
			case UIVariableType.Float:
				this.InitFloat((float)value);
				break;
			case UIVariableType.String:
				this.InitString(value.ToString());
				break;
			}
		}

		public void InitValue(float value)
		{
			switch (this.type)
			{
			case UIVariableType.Boolean:
				this.InitBoolean(!Mathf.Approximately(value, 0f));
				break;
			case UIVariableType.Integer:
				this.InitInteger((long)value);
				break;
			case UIVariableType.Float:
				this.InitFloat(value);
				break;
			case UIVariableType.String:
				this.InitString(value.ToString());
				break;
			}
		}

		public void InitValue(string value)
		{
			switch (this.type)
			{
			case UIVariableType.Boolean:
				this.InitBoolean(bool.Parse(value));
				break;
			case UIVariableType.Integer:
				this.InitInteger(long.Parse(value));
				break;
			case UIVariableType.Float:
				this.InitFloat(float.Parse(value));
				break;
			case UIVariableType.String:
				this.InitString(value);
				break;
			}
		}

		public void SetValue(bool value)
		{
			switch (this.type)
			{
			case UIVariableType.Boolean:
				this.SetBoolean(value);
				break;
			case UIVariableType.Integer:
				this.SetInteger(value ? 1L : 0L);
				break;
			case UIVariableType.Float:
				this.SetFloat(value ? 1f : 0f);
				break;
			case UIVariableType.String:
				this.SetString(value.ToString());
				break;
			}
		}

		public void SetValue(long value)
		{
			switch (this.type)
			{
			case UIVariableType.Boolean:
				this.SetBoolean(value != 0L);
				break;
			case UIVariableType.Integer:
				this.SetInteger(value);
				break;
			case UIVariableType.Float:
				this.SetFloat((float)value);
				break;
			case UIVariableType.String:
				this.SetString(value.ToString());
				break;
			}
		}

		public void SetValue(float value)
		{
			switch (this.type)
			{
			case UIVariableType.Boolean:
				this.SetBoolean(!Mathf.Approximately(value, 0f));
				break;
			case UIVariableType.Integer:
				this.SetInteger((long)value);
				break;
			case UIVariableType.Float:
				this.SetFloat(value);
				break;
			case UIVariableType.String:
				this.SetString(value.ToString());
				break;
			}
		}

		public void SetValue(string value)
		{
			switch (this.type)
			{
			case UIVariableType.Boolean:
				this.SetBoolean(bool.Parse(value));
				break;
			case UIVariableType.Integer:
				this.SetInteger(long.Parse(value));
				break;
			case UIVariableType.Float:
				this.SetFloat(float.Parse(value));
				break;
			case UIVariableType.String:
				this.SetString(value);
				break;
			}
		}

		public void SetAsset(string bundle, string asset)
		{
			this.SetAsset(new AssetID(bundle, asset));
		}

		public void SetAsset(AssetID assetID)
		{
			bool flag = !this.assetValue.Equals(assetID);
			if (flag)
			{
				this.assetValue = assetID;
				this.TriggerValueChanged();
			}
		}

		public void ResetAsset()
		{
			this.SetAsset(AssetID.Empty);
		}

		internal void TriggerValueChanged()
		{
			bool flag = this.OnValueChanged != null;
			if (flag)
			{
				this.OnValueChanged();
			}
		}

		internal void TriggerValueInitialized()
		{
			bool flag = this.OnValueInitialized != null;
			if (flag)
			{
				this.OnValueInitialized();
			}
		}

		[SerializeField]
		private string name;

		[SerializeField]
		private UIVariableType type;

		[SerializeField]
		private bool booleanValue;

		[SerializeField]
		private long integerValue;

		[SerializeField]
		private float floatValue;

		[SerializeField]
		private string stringValue;

		[SerializeField]
		private AssetID assetValue;
	}
}
