﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class DeviceInfo
	{
		public static string GetDeviceInfoJsonData()
		{
			return DeviceInfo.GetDeviceInfo().CallStatic<string>("getAndroidDeviceInfo", new object[0]);
		}

		private static AndroidJavaClass GetDeviceInfo()
		{
			if (DeviceInfo.androidDeviceInfo == null)
			{
				DeviceInfo.androidDeviceInfo = new AndroidJavaClass("com.winunet.and.AndroidDeviceInfo");
			}
			return DeviceInfo.androidDeviceInfo;
		}

		private static AndroidJavaClass androidDeviceInfo;
	}
}
