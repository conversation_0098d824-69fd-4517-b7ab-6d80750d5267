﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Inner Bevel")]
	[ExecuteInEditMode]
	[DisallowMultipleComponent]
	[RequireComponent(typeof(UIMaterialEffect))]
	public sealed class UIInnerBevel : BaseMeshEffect
	{
		public Color HighlightColor
		{
			get
			{
				return this.highlightColor;
			}
			set
			{
				bool flag = this.highlightColor != value;
				if (flag)
				{
					this.highlightColor = value;
					this.Refresh();
				}
			}
		}

		public ColorModeEnum HighlightColorMode
		{
			get
			{
				return this.highlightColorMode;
			}
			set
			{
				bool flag = this.highlightColorMode != value;
				if (flag)
				{
					this.highlightColorMode = value;
					this.Refresh();
				}
			}
		}

		public Color ShadowColor
		{
			get
			{
				return this.shadowColor;
			}
			set
			{
				bool flag = this.shadowColor != value;
				if (flag)
				{
					this.shadowColor = value;
					this.Refresh();
				}
			}
		}

		public ColorModeEnum ShadowColorMode
		{
			get
			{
				return this.shadowColorMode;
			}
			set
			{
				bool flag = this.shadowColorMode != value;
				if (flag)
				{
					this.shadowColorMode = value;
					this.Refresh();
				}
			}
		}

		public Vector2 BevelDirectionAndDepth
		{
			get
			{
				return this.bevelDirectionAndDepth;
			}
			set
			{
				bool flag = this.bevelDirectionAndDepth != value;
				if (flag)
				{
					this.bevelDirectionAndDepth = value;
					this.Refresh();
				}
			}
		}

		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive() || vh.currentVertCount <= 0;
			if (!flag)
			{
				List<UIVertex> list = UIVertexListPool.Get();
				vh.GetUIVertexStream(list);
				List<UIVertex> list2 = this.Modify(list);
				bool flag2 = list2 != null;
				if (flag2)
				{
					vh.Clear();
					vh.AddUIVertexTriangleStream(list2);
				}
				UIVertexListPool.Release(list);
			}
		}

		protected override void Awake()
		{
			base.Awake();
			this.Refresh();
		}

		protected override void OnEnable()
		{
			base.OnEnable();
			this.Refresh();
		}

		protected override void OnDisable()
		{
			base.OnDisable();
			this.Refresh();
		}

		private void Refresh()
		{
			bool flag = this.materialEffect == null;
			if (flag)
			{
				this.materialEffect = this.GetOrAddComponent<UIMaterialEffect>();
			}
			UIEffectMaterialKey materialKey = this.materialEffect.MaterialKey;
			materialKey.EnableInnerBevel = base.enabled;
			materialKey.HighlightColor = this.highlightColor;
			materialKey.HighlightColorMode = this.HighlightColorMode;
			materialKey.ShadowColor = this.shadowColor;
			materialKey.ShadowColorMode = this.shadowColorMode;
			materialKey.HighlightOffset = this.bevelDirectionAndDepth / 500f;
			this.materialEffect.MaterialKey = materialKey;
			this.materialEffect.MarkDirty();
		}

		private List<UIVertex> Modify(List<UIVertex> verts)
		{
			int count = verts.Count;
			for (int i = 0; i < count; i += 6)
			{
				UIVertex uivertex = verts[i];
				Vector4 normalized = (verts[i + 1].uv0 - verts[i].uv0).normalized;
				Vector4 normalized2 = (verts[i + 1].uv0 - verts[i + 2].uv0).normalized;
				Vector4 vector = normalized;
				vector.z = normalized2.x;
				vector.w = normalized2.y;
				uivertex.tangent = vector;
				bool flag = uivertex.uv1 == Vector4.zero;
				if (flag)
				{
					uivertex.uv1 = new Vector2(1f, 1f);
				}
				verts[i] = uivertex;
				for (int j = 1; j < 6; j++)
				{
					uivertex = verts[i + j];
					uivertex.tangent = verts[i].tangent;
					bool flag2 = uivertex.uv1 == Vector4.zero;
					if (flag2)
					{
						uivertex.uv1 = new Vector2(1f, 1f);
					}
					verts[i + j] = uivertex;
				}
			}
			return verts;
		}

		[SerializeField]
		private Color highlightColor = Color.white;

		[SerializeField]
		private ColorModeEnum highlightColorMode = ColorModeEnum.Blend;

		[SerializeField]
		private Color shadowColor = Color.black;

		[SerializeField]
		private ColorModeEnum shadowColorMode = ColorModeEnum.Blend;

		[SerializeField]
		private Vector2 bevelDirectionAndDepth = Vector2.one;

		private UIMaterialEffect materialEffect;

		public enum TextureModeEnum
		{
			Local,
			GlobalTextArea,
			GlobalFullRect
		}
	}
}
