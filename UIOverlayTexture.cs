﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Overlay Texture")]
	[ExecuteInEditMode]
	[DisallowMultipleComponent]
	[RequireComponent(typeof(UIMaterialEffect))]
	public sealed class UIOverlayTexture : BaseMeshEffect
	{
		public Texture2D OverlayTexture
		{
			get
			{
				return this.overlayTexture;
			}
			set
			{
				bool flag = this.overlayTexture != value;
				if (flag)
				{
					this.overlayTexture = value;
					this.Refresh();
				}
			}
		}

		public ColorModeEnum ColorMode
		{
			get
			{
				return this.colorMode;
			}
			set
			{
				bool flag = this.colorMode != value;
				if (flag)
				{
					this.colorMode = value;
					this.Refresh();
				}
			}
		}

		public UIOverlayTexture.TextureModeEnum TextureMode
		{
			get
			{
				return this.textureMode;
			}
			set
			{
				bool flag = this.textureMode != value;
				if (flag)
				{
					this.textureMode = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public float Speed
		{
			get
			{
				return this.speed;
			}
			set
			{
				bool flag = this.speed != value;
				if (flag)
				{
					this.speed = value;
					this.Refresh();
				}
			}
		}

		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive() || vh.currentVertCount <= 0;
			if (!flag)
			{
				List<UIVertex> list = UIVertexListPool.Get();
				vh.GetUIVertexStream(list);
				List<UIVertex> list2 = this.Modify(list);
				bool flag2 = list2 != null;
				if (flag2)
				{
					vh.Clear();
					vh.AddUIVertexTriangleStream(list2);
				}
				UIVertexListPool.Release(list);
			}
		}

		protected override void OnEnable()
		{
			base.OnEnable();
			this.Refresh();
		}

		protected override void OnDisable()
		{
			base.OnDisable();
			bool flag = this.materialEffect != null;
			if (flag)
			{
				UIEffectMaterialKey materialKey = this.materialEffect.MaterialKey;
				materialKey.OverlayTexture = null;
				this.materialEffect.MaterialKey = materialKey;
				this.materialEffect.MarkDirty();
			}
		}

		private void Refresh()
		{
			bool flag = this.materialEffect == null;
			if (flag)
			{
				this.materialEffect = this.GetOrAddComponent<UIMaterialEffect>();
			}
			UIEffectMaterialKey materialKey = this.materialEffect.MaterialKey;
			materialKey.OverlayTexture = this.overlayTexture;
			materialKey.OverlayColorMode = this.colorMode;
			materialKey.OverlaySpeed = this.speed;
			this.materialEffect.MaterialKey = materialKey;
			this.materialEffect.MarkDirty();
		}

		private List<UIVertex> Modify(List<UIVertex> verts)
		{
			int count = verts.Count;
			bool flag = this.textureMode == UIOverlayTexture.TextureModeEnum.GlobalTextArea || this.textureMode == UIOverlayTexture.TextureModeEnum.GlobalFullRect;
			if (flag)
			{
				Vector2 vector = Vector2.zero;
				Vector2 vector2 = Vector2.zero;
				bool flag2 = this.textureMode == UIOverlayTexture.TextureModeEnum.GlobalFullRect;
				if (flag2)
				{
					RectTransform rectTransform = base.transform as RectTransform;
					Rect rect = rectTransform.rect;
					vector..ctor(rect.xMin, rect.yMax);
					vector2..ctor(rect.xMax, rect.yMin);
				}
				else
				{
					vector = verts[0].position;
					vector2 = verts[verts.Count - 1].position;
					for (int i = 0; i < verts.Count; i++)
					{
						bool flag3 = verts[i].position.x < vector.x;
						if (flag3)
						{
							vector.x = verts[i].position.x;
						}
						bool flag4 = verts[i].position.y > vector.y;
						if (flag4)
						{
							vector.y = verts[i].position.y;
						}
						bool flag5 = verts[i].position.x > vector2.x;
						if (flag5)
						{
							vector2.x = verts[i].position.x;
						}
						bool flag6 = verts[i].position.y < vector2.y;
						if (flag6)
						{
							vector2.y = verts[i].position.y;
						}
					}
				}
				float num = vector.y - vector2.y;
				float num2 = vector2.x - vector.x;
				for (int j = 0; j < count; j++)
				{
					UIVertex uivertex = verts[j];
					uivertex.uv1 = new Vector2((uivertex.position.x - vector.x) / num2, 1f - (vector.y - uivertex.position.y) / num);
					verts[j] = uivertex;
				}
			}
			else
			{
				for (int k = 0; k < count; k++)
				{
					UIVertex uivertex2 = verts[k];
					uivertex2.uv1 = new Vector2((float)((k % 6 == 0 || k % 6 == 5 || k % 6 == 4) ? 0 : 1), (float)((k % 6 == 2 || k % 6 == 3 || k % 6 == 4) ? 0 : 1));
					verts[k] = uivertex2;
				}
			}
			return verts;
		}

		[SerializeField]
		private Texture2D overlayTexture;

		[SerializeField]
		private ColorModeEnum colorMode;

		[SerializeField]
		private UIOverlayTexture.TextureModeEnum textureMode;

		[SerializeField]
		[Range(0f, 1f)]
		private float speed = 0f;

		private UIMaterialEffect materialEffect;

		public enum TextureModeEnum
		{
			Local,
			GlobalTextArea,
			GlobalFullRect
		}
	}
}
