﻿using System;
using System.Text;

namespace Nirvana
{
	public static class StringExtensions
	{
		public static string ToLiteral(this string input)
		{
			StringBuilder stringBuilder = new StringBuilder(input.Length + 2);
			int i = 0;
			while (i < input.Length)
			{
				char c = input[i];
				char c2 = c;
				char c3 = c2;
				if (c3 <= '"')
				{
					switch (c3)
					{
					case '\0':
						stringBuilder.Append("\\0");
						break;
					case '\u0001':
					case '\u0002':
					case '\u0003':
					case '\u0004':
					case '\u0005':
					case '\u0006':
						goto IL_012E;
					case '\a':
						stringBuilder.Append("\\a");
						break;
					case '\b':
						stringBuilder.Append("\\b");
						break;
					case '\t':
						stringBuilder.Append("\\t");
						break;
					case '\n':
						stringBuilder.Append("\\n");
						break;
					case '\v':
						stringBuilder.Append("\\v");
						break;
					case '\f':
						stringBuilder.Append("\\f");
						break;
					case '\r':
						stringBuilder.Append("\\r");
						break;
					default:
						if (c3 != '"')
						{
							goto IL_012E;
						}
						stringBuilder.Append("\\\"");
						break;
					}
				}
				else if (c3 != '\'')
				{
					if (c3 != '\\')
					{
						goto IL_012E;
					}
					stringBuilder.Append("\\\\");
				}
				else
				{
					stringBuilder.Append("\\'");
				}
				IL_0138:
				i++;
				continue;
				IL_012E:
				stringBuilder.Append(c);
				goto IL_0138;
			}
			return stringBuilder.ToString();
		}
	}
}
