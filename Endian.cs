﻿using System;

namespace Nirvana
{
	public static class Endian
	{
		public static ushort ReverseBytes(ushort value)
		{
			return (ushort)(((int)(value & 255) << 8) | (int)((uint)(value & 65280) >> 8));
		}

		public static uint ReverseBytes(uint value)
		{
			return ((value & 255U) << 24) | ((value & 65280U) << 8) | ((value & 16711680U) >> 8) | ((value & 4278190080U) >> 24);
		}

		public static ulong ReverseBytes(ulong value)
		{
			return ((value & 255UL) << 56) | ((value & 65280UL) << 40) | ((value & 16711680UL) << 24) | ((value & (ulong)(-16777216)) << 8) | ((value & 1095216660480UL) >> 8) | ((value & 280375465082880UL) >> 24) | ((value & 71776119061217280UL) >> 40) | ((value & 18374686479671623680UL) >> 56);
		}
	}
}
