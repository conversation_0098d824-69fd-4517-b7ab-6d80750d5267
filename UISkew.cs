﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Skew")]
	[RequireComponent(typeof(RectTransform))]
	public sealed class UISkew : BaseMeshEffect
	{
		public Vector2 UpperLeftOffset
		{
			get
			{
				return this.upperLeftOffset;
			}
			set
			{
				bool flag = this.upperLeftOffset != value;
				if (flag)
				{
					this.upperLeftOffset = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public Vector2 UpperRightOffset
		{
			get
			{
				return this.upperRightOffset;
			}
			set
			{
				bool flag = this.upperRightOffset != value;
				if (flag)
				{
					this.upperRightOffset = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public Vector2 LowerLeftOffset
		{
			get
			{
				return this.lowerLeftOffset;
			}
			set
			{
				bool flag = this.lowerLeftOffset != value;
				if (flag)
				{
					this.lowerLeftOffset = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public Vector2 LowerRightOffset
		{
			get
			{
				return this.lowerRightOffset;
			}
			set
			{
				bool flag = this.lowerRightOffset != value;
				if (flag)
				{
					this.lowerRightOffset = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive() || vh.currentVertCount <= 0;
			if (!flag)
			{
				List<UIVertex> list = UIVertexListPool.Get();
				vh.GetUIVertexStream(list);
				List<UIVertex> list2 = this.Modify(list);
				bool flag2 = list2 != null;
				if (flag2)
				{
					vh.Clear();
					vh.AddUIVertexTriangleStream(list2);
				}
				UIVertexListPool.Release(list);
			}
		}

		private List<UIVertex> Modify(List<UIVertex> verts)
		{
			Vector2 vector = Vector2.zero;
			Vector2 vector2 = Vector2.zero;
			bool flag = this.skewMode == UISkew.SkewMode.FullRect;
			if (flag)
			{
				Rect rect = base.GetComponent<RectTransform>().rect;
				vector..ctor(rect.xMin, rect.yMax);
				vector2..ctor(rect.xMax, rect.yMin);
			}
			else
			{
				vector = verts[0].position;
				vector2 = verts[verts.Count - 1].position;
				for (int i = 0; i < verts.Count; i++)
				{
					bool flag2 = verts[i].position.x < vector.x;
					if (flag2)
					{
						vector.x = verts[i].position.x;
					}
					bool flag3 = verts[i].position.y > vector.y;
					if (flag3)
					{
						vector.y = verts[i].position.y;
					}
					bool flag4 = verts[i].position.x > vector2.x;
					if (flag4)
					{
						vector2.x = verts[i].position.x;
					}
					bool flag5 = verts[i].position.y < vector2.y;
					if (flag5)
					{
						vector2.y = verts[i].position.y;
					}
				}
			}
			float num = vector.y - vector2.y;
			float num2 = vector2.x - vector.x;
			for (int j = 0; j < verts.Count; j++)
			{
				UIVertex uivertex = verts[j];
				float num3 = (uivertex.position.y - vector2.y) / num;
				float num4 = 1f - num3;
				float num5 = (vector2.x - uivertex.position.x) / num2;
				float num6 = 1f - num5;
				Vector3 zero = Vector3.zero;
				float num7 = (this.upperLeftOffset.y * num3 + this.lowerLeftOffset.y * num4) * num5;
				float num8 = (this.upperRightOffset.y * num3 + this.lowerRightOffset.y * num4) * num6;
				zero.y = num7 + num8;
				float num9 = (this.upperLeftOffset.x * num5 + this.upperRightOffset.x * num6) * num3;
				float num10 = (this.lowerLeftOffset.x * num5 + this.lowerRightOffset.x * num6) * num4;
				zero.x = num9 + num10;
				uivertex.position += zero;
				verts[j] = uivertex;
			}
			return verts;
		}

		[SerializeField]
		private UISkew.SkewMode skewMode = UISkew.SkewMode.FullRect;

		[SerializeField]
		private Vector2 lowerLeftOffset = Vector2.zero;

		[SerializeField]
		private Vector2 lowerRightOffset = Vector2.zero;

		[SerializeField]
		private Vector2 upperLeftOffset = Vector2.zero;

		[SerializeField]
		private Vector2 upperRightOffset = Vector2.zero;

		public enum SkewMode
		{
			FullRect,
			TextArea
		}
	}
}
