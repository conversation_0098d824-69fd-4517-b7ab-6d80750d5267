﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Gradient")]
	[RequireComponent(typeof(RectTransform))]
	public sealed class UIGradient : BaseMeshEffect
	{
		public UIGradient.GradientModeEnum GradientMode
		{
			get
			{
				return this.gradientMode;
			}
			set
			{
				bool flag = this.gradientMode != value;
				if (flag)
				{
					this.gradientMode = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public UIGradient.GradientDirectionEnum GradientDirection
		{
			get
			{
				return this.gradientDirection;
			}
			set
			{
				bool flag = this.gradientDirection != value;
				if (flag)
				{
					this.gradientDirection = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public UIGradient.ColorModeEnum ColorMode
		{
			get
			{
				return this.colorMode;
			}
			set
			{
				bool flag = this.colorMode != value;
				if (flag)
				{
					this.colorMode = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public Color Color1
		{
			get
			{
				return this.vertex1;
			}
			set
			{
				bool flag = this.vertex1 != value;
				if (flag)
				{
					this.vertex1 = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public Color Color2
		{
			get
			{
				return this.vertex2;
			}
			set
			{
				bool flag = this.vertex2 != value;
				if (flag)
				{
					this.vertex2 = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public bool UseGraphicAlpha
		{
			get
			{
				return this.useGraphicAlpha;
			}
			set
			{
				bool flag = this.useGraphicAlpha != value;
				if (flag)
				{
					this.useGraphicAlpha = value;
					bool flag2 = base.graphic != null;
					if (flag2)
					{
						base.graphic.SetVerticesDirty();
					}
				}
			}
		}

		public override void ModifyMesh(VertexHelper vh)
		{
			bool flag = !this.IsActive() || vh.currentVertCount <= 0;
			if (!flag)
			{
				List<UIVertex> list = UIVertexListPool.Get();
				vh.GetUIVertexStream(list);
				List<UIVertex> list2 = this.Modify(list);
				bool flag2 = list2 != null;
				if (flag2)
				{
					vh.Clear();
					vh.AddUIVertexTriangleStream(list2);
				}
				UIVertexListPool.Release(list);
			}
		}

		private List<UIVertex> Modify(List<UIVertex> verts)
		{
			bool flag = this.gradientMode == UIGradient.GradientModeEnum.GlobalTextArea || this.gradientMode == UIGradient.GradientModeEnum.GlobalFullRect;
			if (flag)
			{
				this.ModifyGlobal(verts);
			}
			else
			{
				this.ModifyLocal(verts);
			}
			return verts;
		}

		private void ModifyGlobal(List<UIVertex> verts)
		{
			Vector2 vector = Vector2.zero;
			Vector2 vector2 = Vector2.zero;
			bool flag = this.gradientMode == UIGradient.GradientModeEnum.GlobalFullRect;
			if (flag)
			{
				RectTransform rectTransform = base.transform as RectTransform;
				Rect rect = rectTransform.rect;
				vector..ctor(rect.xMin, rect.yMax);
				vector2..ctor(rect.xMax, rect.yMin);
			}
			else
			{
				vector = verts[0].position;
				vector2 = verts[verts.Count - 1].position;
				for (int i = 0; i < verts.Count; i++)
				{
					bool flag2 = verts[i].position.x < vector.x;
					if (flag2)
					{
						vector.x = verts[i].position.x;
					}
					bool flag3 = verts[i].position.y > vector.y;
					if (flag3)
					{
						vector.y = verts[i].position.y;
					}
					bool flag4 = verts[i].position.x > vector2.x;
					if (flag4)
					{
						vector2.x = verts[i].position.x;
					}
					bool flag5 = verts[i].position.y < vector2.y;
					if (flag5)
					{
						vector2.y = verts[i].position.y;
					}
				}
			}
			float num = vector.y - vector2.y;
			float num2 = vector2.x - vector.x;
			for (int j = 0; j < verts.Count; j++)
			{
				UIVertex uivertex = verts[j];
				bool flag6 = this.gradientDirection == UIGradient.GradientDirectionEnum.Vertical;
				if (flag6)
				{
					Color color = Color.Lerp(this.vertex1, this.vertex2, (vector.y - uivertex.position.y) / num);
					uivertex.color = this.CalculateColor(uivertex.color, color, this.colorMode);
				}
				else
				{
					Color color2 = Color.Lerp(this.vertex1, this.vertex2, (uivertex.position.x - vector.x) / num2);
					uivertex.color = this.CalculateColor(uivertex.color, color2, this.colorMode);
				}
				bool flag7 = this.useGraphicAlpha;
				if (flag7)
				{
					uivertex.color.a = uivertex.color.a * verts[j].color.a / byte.MaxValue;
				}
				verts[j] = uivertex;
			}
		}

		private void ModifyLocal(List<UIVertex> verts)
		{
			bool flag = base.graphic is Text;
			if (flag)
			{
				this.ModifyLocalText(verts);
			}
			else
			{
				this.ModifyLocalImage(verts);
			}
		}

		private void ModifyLocalText(List<UIVertex> verts)
		{
			for (int i = 0; i < verts.Count; i++)
			{
				UIVertex uivertex = verts[i];
				UIGradient.GradientDirectionEnum gradientDirectionEnum = this.gradientDirection;
				UIGradient.GradientDirectionEnum gradientDirectionEnum2 = gradientDirectionEnum;
				if (gradientDirectionEnum2 != UIGradient.GradientDirectionEnum.Vertical)
				{
					if (gradientDirectionEnum2 == UIGradient.GradientDirectionEnum.Horizontal)
					{
						bool flag = i % 6 == 0 || i % 6 == 4 || i % 6 == 5;
						if (flag)
						{
							Color color = this.vertex1;
							uivertex.color = this.CalculateColor(uivertex.color, color, this.colorMode);
						}
						else
						{
							Color color2 = this.vertex2;
							uivertex.color = this.CalculateColor(uivertex.color, color2, this.colorMode);
						}
					}
				}
				else
				{
					bool flag2 = i % 6 == 0 || i % 6 == 1 || i % 6 == 5;
					if (flag2)
					{
						Color color3 = this.vertex1;
						uivertex.color = this.CalculateColor(uivertex.color, color3, this.colorMode);
					}
					else
					{
						Color color4 = this.vertex2;
						uivertex.color = this.CalculateColor(uivertex.color, color4, this.colorMode);
					}
				}
				bool flag3 = this.useGraphicAlpha;
				if (flag3)
				{
					uivertex.color.a = uivertex.color.a * verts[i].color.a / byte.MaxValue;
				}
				verts[i] = uivertex;
			}
		}

		private void ModifyLocalImage(List<UIVertex> verts)
		{
			for (int i = 0; i < verts.Count; i++)
			{
				UIVertex uivertex = verts[i];
				UIGradient.GradientDirectionEnum gradientDirectionEnum = this.gradientDirection;
				UIGradient.GradientDirectionEnum gradientDirectionEnum2 = gradientDirectionEnum;
				if (gradientDirectionEnum2 != UIGradient.GradientDirectionEnum.Vertical)
				{
					if (gradientDirectionEnum2 == UIGradient.GradientDirectionEnum.Horizontal)
					{
						bool flag = i % 6 == 0 || i % 6 == 1 || i % 6 == 5;
						if (flag)
						{
							Color color = this.vertex1;
							uivertex.color = this.CalculateColor(uivertex.color, color, this.colorMode);
						}
						else
						{
							Color color2 = this.vertex2;
							uivertex.color = this.CalculateColor(uivertex.color, color2, this.colorMode);
						}
					}
				}
				else
				{
					bool flag2 = i % 6 == 1 || i % 6 == 2 || i % 6 == 3;
					if (flag2)
					{
						Color color3 = this.vertex1;
						uivertex.color = this.CalculateColor(uivertex.color, color3, this.colorMode);
					}
					else
					{
						Color color4 = this.vertex2;
						uivertex.color = this.CalculateColor(uivertex.color, color4, this.colorMode);
					}
				}
				bool flag3 = this.useGraphicAlpha;
				if (flag3)
				{
					uivertex.color.a = uivertex.color.a * verts[i].color.a / byte.MaxValue;
				}
				verts[i] = uivertex;
			}
		}

		private Color CalculateColor(Color initialColor, Color newColor, UIGradient.ColorModeEnum colorMode)
		{
			bool flag = colorMode == UIGradient.ColorModeEnum.Override;
			Color color;
			if (flag)
			{
				color = newColor;
			}
			else
			{
				bool flag2 = colorMode == UIGradient.ColorModeEnum.Additive;
				if (flag2)
				{
					color = initialColor + newColor;
				}
				else
				{
					bool flag3 = colorMode == UIGradient.ColorModeEnum.Multiply;
					if (flag3)
					{
						color = initialColor * newColor;
					}
					else
					{
						color = newColor;
					}
				}
			}
			return color;
		}

		[SerializeField]
		private UIGradient.GradientModeEnum gradientMode = UIGradient.GradientModeEnum.Local;

		[SerializeField]
		private UIGradient.GradientDirectionEnum gradientDirection = UIGradient.GradientDirectionEnum.Vertical;

		[SerializeField]
		private UIGradient.ColorModeEnum colorMode = UIGradient.ColorModeEnum.Override;

		[SerializeField]
		private Color vertex1 = Color.white;

		[SerializeField]
		private Color vertex2 = Color.black;

		[SerializeField]
		private bool useGraphicAlpha = true;

		public enum GradientModeEnum
		{
			GlobalFullRect,
			GlobalTextArea = 2,
			Local = 1
		}

		public enum GradientDirectionEnum
		{
			Vertical,
			Horizontal
		}

		public enum ColorModeEnum
		{
			Override,
			Additive,
			Multiply
		}
	}
}
