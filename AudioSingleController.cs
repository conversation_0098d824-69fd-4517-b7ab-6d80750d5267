﻿using System;
using System.Collections;
using UnityEngine;

namespace Nirvana
{
	public sealed class AudioSingleController : IAudioController, IAudioPlayer
	{
		public AudioSingleController(AudioSourcePool pool, AudioItem item, AudioSubItem subItem, bool loop)
		{
			this.pool = pool;
			this.item = item;
			AudioSource audioSource = this.pool.Allocate(this.item.name);
			this.item.SetupAudioSource(audioSource, subItem);
			audioSource.loop = loop;
			this.source = audioSource;
			float num = this.item.Delay + subItem.GetDelay();
			float fadeInTime = subItem.GetFadeInTime();
			float fadeOutTime = subItem.GetFadeOutTime();
			this.player = new AudioItemPlayer(audioSource, num, fadeInTime, fadeOutTime);
		}

		public bool IsPlaying
		{
			get
			{
				return this.player.IsPlaying;
			}
		}

		public float LeftTime
		{
			get
			{
				return this.player.TotalTime - this.player.PlayTime;
			}
		}

		public override string ToString()
		{
			return this.item.name;
		}

		public IEnumerator WaitFinish()
		{
			return new WaitUntil(() => !this.IsPlaying);
		}

		public void Stop()
		{
			this.player.Stop();
		}

		public void SetPosition(Vector3 position)
		{
			this.source.transform.position = position;
		}

		public void SetTransform(Transform transform)
		{
			this.trans3D = transform;
		}

		public void Play()
		{
			this.player.Play();
		}

		public void Update()
		{
			bool flag = this.trans3D != null;
			if (flag)
			{
				this.source.transform.SetPositionAndRotation(this.trans3D.position, this.trans3D.rotation);
				this.source.transform.localScale = this.trans3D.localScale;
			}
			this.player.Update();
		}

		public void FinshAudio()
		{
			bool flag = this.item != null;
			if (flag)
			{
				this.item.ReducePlayingCount(this);
			}
			bool flag2 = this.pool != null;
			if (flag2)
			{
				this.pool.Free(this.source);
			}
		}

		private AudioItem item;

		private AudioSourcePool pool;

		private AudioSource source;

		private AudioItemPlayer player;

		private Transform trans3D;
	}
}
