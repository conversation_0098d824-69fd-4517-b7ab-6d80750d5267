﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AttributeUsage(AttributeTargets.Enum | AttributeTargets.Field)]
	public sealed class EnumLabelAttribute : PropertyAttribute
	{
		public EnumLabelAttribute()
		{
		}

		public EnumLabelAttribute(string label)
		{
			this.label = label;
		}

		public EnumLabelAttribute(string label, params int[] enumOrder)
		{
			this.label = label;
			this.enumOrder = enumOrder;
		}

		public string Label
		{
			get
			{
				return this.label;
			}
		}

		public int[] EnumOrder
		{
			get
			{
				return this.enumOrder;
			}
		}

		private string label;

		private int[] enumOrder = new int[0];
	}
}
