﻿using System;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Render/Minimap Camera")]
	[ExecuteInEditMode]
	[RequireComponent(typeof(Camera))]
	public sealed class MinimapCamera : MonoBehaviour
	{
		public static MinimapCamera Instance
		{
			get
			{
				return MinimapCamera.instance;
			}
		}

		public Texture2D MapTexture
		{
			get
			{
				return this.mapTexture;
			}
			set
			{
				this.mapTexture = value;
			}
		}

		public int MapTextureWidth
		{
			get
			{
				return this.mapTextureWidth;
			}
		}

		public int MapTextureHeight
		{
			get
			{
				return this.mapTextureHeight;
			}
		}

		public Vector2 TransformWorldToUV(Vector3 pos)
		{
			float num = (pos.x - this.cameraOrigin.x) / (2f * this.cameraOrthSize);
			float num2 = (pos.z - this.cameraOrigin.y) / (2f * this.cameraOrthSize);
			num -= 0.5f;
			num2 -= 0.5f;
			Vector3 vector;
			vector..ctor(num, pos.y, num2);
			float num3 = this.snapEulerAnglesY;
			vector = Quaternion.Euler(0f, -num3, 0f) * vector;
			vector.x *= this.cameraOrthRadio;
			return new Vector2(vector.x, vector.z);
		}

		public Vector3 TransformUVToWorld(Vector2 uv)
		{
			uv.x /= this.cameraOrthRadio;
			Vector3 vector;
			vector..ctor(uv.x, 0f, uv.y);
			float num = this.snapEulerAnglesY;
			vector = Quaternion.Euler(0f, num, 0f) * vector;
			vector.x += 0.5f;
			vector.z += 0.5f;
			float num2 = vector.x * 2f * this.cameraOrthSize + this.cameraOrigin.x;
			float num3 = vector.z * 2f * this.cameraOrthSize + this.cameraOrigin.y;
			Vector3 vector2;
			vector2..ctor(num2, vector.y, num3);
			return vector2;
		}

		private void Awake()
		{
			Assert.IsNull<MinimapCamera>(MinimapCamera.instance);
			MinimapCamera.instance = this;
			this.mapCamera = base.GetComponent<Camera>();
			Assert.IsNotNull<Camera>(this.mapCamera);
			Assert.IsTrue(this.mapCamera.orthographic);
			this.mapCamera.enabled = false;
			this.cameraOrthSize = this.mapCamera.orthographicSize;
			this.cameraOrthRadio = (float)this.mapTextureHeight / (float)this.mapTextureWidth;
			Vector3 position = this.mapCamera.transform.position;
			this.cameraOrigin = new Vector2(position.x - this.cameraOrthSize, position.z - this.cameraOrthSize);
			bool isPlaying = Application.isPlaying;
			if (isPlaying)
			{
				bool flag = 0f == this.snapEulerAnglesY;
				if (flag)
				{
					this.snapEulerAnglesY = base.transform.rotation.eulerAngles.y;
				}
			}
		}

		private void OnDestroy()
		{
			MinimapCamera.instance = null;
		}

		private static MinimapCamera instance;

		[SerializeField]
		[Tooltip("The map texture.")]
		private Texture2D mapTexture;

		[SerializeField]
		[Tooltip("The minimap texture width.")]
		private int mapTextureWidth = 512;

		[SerializeField]
		[Tooltip("The minimap texture height.")]
		private int mapTextureHeight = 512;

		[HideInInspector]
		[SerializeField]
		private float snapEulerAnglesY = 0f;

		private Camera mapCamera;

		private Vector2 cameraOrigin;

		private float cameraOrthSize;

		private float cameraOrthRadio;
	}
}
