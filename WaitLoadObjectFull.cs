﻿using System;
using UnityEngine;

namespace Nirvana
{
	internal class WaitLoadObjectFull : WaitLoadObject
	{
		internal WaitLoadObjectFull(AssetBundleManager assetBundleManager, string assetBundleName, string assetName, Type assetType)
		{
			this.AssetBundleManager = assetBundleManager;
			this.assetBundleName = assetBundleName;
			this.assetName = assetName;
			this.assetType = assetType;
		}

		internal WaitLoadObjectFull(string format, params object[] args)
		{
			base.Error = string.Format(format, args);
		}

		public override bool keepWaiting
		{
			get
			{
				bool flag = this.Request == null && base.Error != null;
				bool flag2;
				if (flag)
				{
					flag2 = false;
				}
				else
				{
					bool flag3 = this.Request != null && this.Request.isDone;
					flag2 = !flag3;
				}
				return flag2;
			}
		}

		internal AssetBundleManager AssetBundleManager { get; private set; }

		private protected AssetBundleRequest Request { protected get; private set; }

		protected string AssetBundleName
		{
			get
			{
				return this.assetBundleName;
			}
		}

		public override Object GetObject()
		{
			bool flag = this.Request != null && this.Request.isDone;
			Object @object;
			if (flag)
			{
				@object = this.Request.asset;
			}
			else
			{
				@object = null;
			}
			return @object;
		}

		internal override bool Update()
		{
			AssetBundleItem assetBundle = this.AssetBundleManager.GetAssetBundle(this.assetBundleName);
			bool flag = assetBundle == null;
			bool flag2;
			if (flag)
			{
				flag2 = true;
			}
			else
			{
				bool flag3 = assetBundle.Error != null;
				if (flag3)
				{
					base.Error = assetBundle.Error;
					flag2 = false;
				}
				else
				{
					this.Request = assetBundle.AssetBundle.LoadAssetAsync(this.assetName, this.assetType);
					flag2 = false;
				}
			}
			return flag2;
		}

		private string assetBundleName;

		private string assetName;

		private Type assetType;
	}
}
