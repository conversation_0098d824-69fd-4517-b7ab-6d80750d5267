﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class ClipboardTool
	{
		public static void CopyTextToClipboard(string text)
		{
			ClipboardTool.GetClipboardTool().CallStatic("copyTextToClipboard", new object[] { text });
		}

		public static string GetTextClipboard()
		{
			return ClipboardTool.GetClipboardTool().CallStatic<string>("getTextClipboard", new object[0]);
		}

		private static AndroidJavaClass GetClipboardTool()
		{
			if (ClipboardTool.clipboardToolClass == null)
			{
				ClipboardTool.clipboardToolClass = new AndroidJavaClass("com.winunet.and.CutBoardTool");
			}
			return ClipboardTool.clipboardToolClass;
		}

		private static AndroidJavaClass clipboardToolClass;
	}
}
