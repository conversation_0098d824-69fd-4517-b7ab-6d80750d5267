﻿using System;
using UnityEngine;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/Render/Batch Renderer")]
	[ExecuteInEditMode]
	public sealed class BatchRenderer : MonoBehaviour
	{
		public Material TemplateMaterial
		{
			get
			{
				return this.templateMaterial;
			}
		}

		public Texture MainTexture
		{
			get
			{
				return this.mainTexture;
			}
		}

		private void Refresh()
		{
			bool flag = this.mainTexture == null || this.templateMaterial == null;
			if (!flag)
			{
				NirvanaRenderer component = base.GetComponent<NirvanaRenderer>();
				bool flag2 = component != null;
				if (flag2)
				{
					component.Materials = new Material[]
					{
						new Material(this.templateMaterial)
						{
							name = string.Format("{0}:{1}", this.templateMaterial.name, this.mainTexture),
							mainTexture = this.mainTexture,
							hideFlags = 61
						}
					};
				}
				else
				{
					Renderer component2 = base.GetComponent<Renderer>();
					bool flag3 = component2 != null;
					if (flag3)
					{
						component2.sharedMaterial = new Material(this.templateMaterial)
						{
							name = string.Format("{0}:{1}", this.templateMaterial.name, this.mainTexture),
							mainTexture = this.mainTexture,
							hideFlags = 61
						};
					}
				}
			}
		}

		private void Awake()
		{
			this.Refresh();
		}

		[SerializeField]
		[Tooltip("The template material.")]
		private Material templateMaterial;

		[SerializeField]
		[Tooltip("The main texture")]
		private Texture mainTexture;
	}
}
