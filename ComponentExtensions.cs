﻿using System;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	public static class ComponentExtensions
	{
		public static Component GetOrAddComponent(this Component obj, Type type)
		{
			Component component = obj.GetComponent(type);
			bool flag = component == null;
			if (flag)
			{
				component = obj.gameObject.AddComponent(type);
			}
			return component;
		}

		public static T GetOrAddComponent<T>(this Component obj) where T : Component
		{
			T t = obj.GetComponent<T>();
			bool flag = t == null;
			if (flag)
			{
				t = obj.gameObject.AddComponent<T>();
			}
			return t;
		}

		public static T GetOrAddComponentDontSave<T>(this Component obj) where T : Component
		{
			T t = obj.GetComponent<T>();
			bool flag = t == null;
			if (flag)
			{
				t = obj.gameObject.AddComponent<T>();
				t.hideFlags = 52;
			}
			return t;
		}

		public static bool HasComponent(this Component obj, Type type)
		{
			return obj.GetComponent(type) != null;
		}

		public static bool HasComponent<T>(this Component obj) where T : Component
		{
			return obj.GetComponent<T>() != null;
		}

		public static T GetComponentInParentHard<T>(this Component obj) where T : Component
		{
			Assert.IsNotNull<Component>(obj);
			Transform transform = obj.transform;
			while (transform != null)
			{
				T component = transform.GetComponent<T>();
				bool flag = component != null;
				if (flag)
				{
					return component;
				}
				transform = transform.parent;
			}
			return default(T);
		}
	}
}
