﻿using System;
using System.Collections;
using UnityEngine;

namespace Nirvana
{
	public static class AnimatorExtensions
	{
		public static IEnumerator WaitEvent(this Animator animator, string eventName)
		{
			AnimatorEventDispatcher orAddComponentDontSave = animator.GetOrAddComponentDontSave<AnimatorEventDispatcher>();
			return orAddComponentDontSave.WaitEvent(eventName);
		}

		public static void WaitEvent(this Animator animator, string eventName, Action<string, AnimatorStateInfo> complete)
		{
			AnimatorEventDispatcher orAddComponentDontSave = animator.GetOrAddComponentDontSave<AnimatorEventDispatcher>();
			orAddComponentDontSave.WaitEvent(eventName, complete);
		}

		public static SignalHandle ListenEvent(this Animator animator, string eventName, Action<string, AnimatorStateInfo> eventDelegate)
		{
			AnimatorEventDispatcher orAddComponentDontSave = animator.GetOrAddComponentDontSave<AnimatorEventDispatcher>();
			return orAddComponentDontSave.ListenEvent(eventName, eventDelegate);
		}

		public static AnimationClip GetAnimationClip(this Animator animator, string name)
		{
			RuntimeAnimatorController runtimeAnimatorController = animator.runtimeAnimatorController;
			bool flag = runtimeAnimatorController == null;
			AnimationClip animationClip;
			if (flag)
			{
				animationClip = null;
			}
			else
			{
				AnimatorOverrideController animatorOverrideController = runtimeAnimatorController as AnimatorOverrideController;
				bool flag2 = animatorOverrideController != null;
				if (flag2)
				{
					animationClip = animatorOverrideController[name];
				}
				else
				{
					foreach (AnimationClip animationClip2 in runtimeAnimatorController.animationClips)
					{
						bool flag3 = animationClip2.name == name;
						if (flag3)
						{
							return animationClip2;
						}
					}
					animationClip = null;
				}
			}
			return animationClip;
		}
	}
}
