﻿using System;
using UnityEngine;

namespace Nirvana
{
	public static class MeshExtensions
	{
		public static Mesh Duplicate(this Mesh origin)
		{
			return new Mesh
			{
				name = origin.name + " (Clone)",
				vertices = origin.vertices,
				triangles = origin.triangles,
				uv = origin.uv,
				uv2 = origin.uv2,
				uv3 = origin.uv3,
				uv4 = origin.uv4,
				normals = origin.normals,
				tangents = origin.tangents,
				colors = origin.colors,
				bounds = origin.bounds,
				boneWeights = origin.boneWeights,
				bindposes = origin.bindposes,
				subMeshCount = origin.subMeshCount
			};
		}
	}
}
