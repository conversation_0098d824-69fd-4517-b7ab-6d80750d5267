﻿using System;
using UnityEngine;
using UnityEngine.Assertions;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Control/Toggle Interactable")]
	[RequireComponent(typeof(Toggle))]
	public sealed class ToggleInteractable : MonoBehaviour
	{
		private void Awake()
		{
			this.toggle = base.GetComponent<Toggle>();
			Assert.IsNotNull<Toggle>(this.toggle);
			this.OnToggleChanged(this.toggle.isOn);
			this.toggle.onValueChanged.AddListener(new UnityAction<bool>(this.OnToggleChanged));
		}

		private void OnEnable()
		{
			this.OnToggleChanged(this.toggle.isOn);
		}

		private void OnToggleChanged(bool isOn)
		{
			foreach (Selectable selectable in this.actives)
			{
				selectable.interactable = isOn;
			}
			foreach (Selectable selectable2 in this.deactives)
			{
				selectable2.interactable = !isOn;
			}
		}

		[SerializeField]
		private Selectable[] actives;

		[SerializeField]
		private Selectable[] deactives;

		private Toggle toggle;
	}
}
