﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[ExecuteInEditMode]
	[DisallowMultipleComponent]
	[RequireComponent(typeof(Graphic))]
	public sealed class UIMaterialEffect : MonoBehaviour, IMaterialModifier
	{
		internal UIEffectMaterialKey MaterialKey
		{
			get
			{
				return this.materialKey;
			}
			set
			{
				bool flag = !this.materialKey.Equals(value);
				if (flag)
				{
					this.materialKey = value;
					bool flag2 = this.material != null;
					if (flag2)
					{
						UIEffectMaterials.Free(this.material);
						this.material = null;
					}
				}
			}
		}

		public Material GetModifiedMaterial(Material baseMaterial)
		{
			Material material = baseMaterial;
			bool enabled = base.enabled;
			if (enabled)
			{
				bool flag = this.material == null;
				if (flag)
				{
					this.material = UIEffectMaterials.Get(this.materialKey);
				}
				bool flag2 = this.material;
				if (flag2)
				{
					material = this.material;
				}
			}
			MaskableGraphic maskableGraphic = this.graphic as MaskableGraphic;
			bool flag3 = maskableGraphic != null;
			Material material2;
			if (flag3)
			{
				material2 = maskableGraphic.GetModifiedMaterial(material);
			}
			else
			{
				material2 = material;
			}
			return material2;
		}

		internal void MarkDirty()
		{
			bool flag = this.graphic != null;
			if (flag)
			{
				this.graphic.SetMaterialDirty();
			}
		}

		private void Awake()
		{
			this.graphic = base.GetComponent<Graphic>();
		}

		private void OnEnable()
		{
			bool flag = this.graphic != null;
			if (flag)
			{
				this.graphic.SetMaterialDirty();
			}
		}

		private void OnDisable()
		{
			bool flag = this.graphic != null;
			if (flag)
			{
				this.graphic.SetMaterialDirty();
			}
		}

		private Graphic graphic;

		private UIEffectMaterialKey materialKey;

		private Material material;
	}
}
