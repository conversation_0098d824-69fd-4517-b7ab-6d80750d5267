﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Nirvana
{
	public sealed class ListComparer<T> : IEqualityComparer<List<T>>
	{
		public static ListComparer<T> Default
		{
			get
			{
				bool flag = ListComparer<T>.defaultComparer == null;
				if (flag)
				{
					ListComparer<T>.defaultComparer = new ListComparer<T>();
				}
				return ListComparer<T>.defaultComparer;
			}
		}

		public bool Equals(List<T> x, List<T> y)
		{
			return x.SequenceEqual(y);
		}

		public int GetHashCode(List<T> obj)
		{
			int num = 0;
			foreach (T t in obj)
			{
				num ^= t.GetHashCode();
			}
			return num;
		}

		private static volatile ListComparer<T> defaultComparer;
	}
}
