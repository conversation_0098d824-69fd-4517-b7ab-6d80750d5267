﻿using System;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Nirvana
{
	[RequireComponent(typeof(RectTransform))]
	[AddComponentMenu("UI/Accordion Element")]
	public sealed class AccordionElement : Toggle
	{
		public void Refresh()
		{
			this.OnValueChanged(base.isOn);
		}

		protected override void Awake()
		{
			base.Awake();
			this.contentLayout = this.content.GetComponent<HorizontalOrVerticalLayoutGroup>();
			this.layoutSpacing = this.contentLayout.spacing;
			base.transition = 0;
			this.toggleTransition = 0;
			this.accordion = base.GetComponentInParent<Accordion>();
			this.rectTransform = base.transform as RectTransform;
			this.scrollRect = base.GetComponentInParent<ScrollRect>();
			bool flag = this.scrollRect != null;
			if (flag)
			{
				this.scrollTransform = this.scrollRect.GetComponent<RectTransform>();
			}
			this.onValueChanged.AddListener(new UnityAction<bool>(this.OnValueChanged));
			this.OnValueChanged(base.isOn);
		}

		private float GetExpandedHeight()
		{
			bool flag = this.content == null;
			float num;
			if (flag)
			{
				num = 0f;
			}
			else
			{
				float preferredHeight = this.content.preferredHeight;
				this.content.preferredHeight = -1f;
				RectTransform component = this.content.GetComponent<RectTransform>();
				float preferredHeight2 = LayoutUtility.GetPreferredHeight(component);
				this.content.preferredHeight = preferredHeight;
				num = preferredHeight2;
			}
			return num;
		}

		private void OnValueChanged(bool state)
		{
			bool flag = this.content == null;
			if (!flag)
			{
				Accordion.Transition transition = ((this.accordion != null) ? this.accordion.TransitionType : Accordion.Transition.Instant);
				bool flag2 = transition == Accordion.Transition.Instant;
				if (flag2)
				{
					if (state)
					{
						this.content.gameObject.SetActive(true);
						this.content.preferredHeight = -1f;
					}
					else
					{
						this.content.gameObject.SetActive(false);
						this.content.preferredHeight = 0f;
					}
				}
				else
				{
					bool flag3 = transition == Accordion.Transition.Tween;
					if (flag3)
					{
						this.tween = this.accordion.TransitionDuration;
						this.tweenFrame = Time.frameCount;
						this.tweenFrom = -1f;
						bool isOn = base.isOn;
						if (isOn)
						{
							this.content.gameObject.SetActive(true);
						}
					}
				}
			}
		}

		private void SetHeight(float height)
		{
			bool flag = this.content == null;
			if (!flag)
			{
				this.content.preferredHeight = height;
			}
		}

		private void Update()
		{
			bool flag = !Application.isPlaying;
			if (!flag)
			{
				bool flag2 = null != base.group;
				if (flag2)
				{
					bool flag3 = !base.isOn && base.group.AnyTogglesOn();
					if (flag3)
					{
						this.normalizePosition = -1f;
					}
				}
				bool flag4 = this.normalizePosition >= 0f;
				if (flag4)
				{
					float num = this.normalizePosition - this.scrollRect.verticalNormalizedPosition;
					float num2 = 2.5f * Time.deltaTime;
					bool flag5 = Mathf.Abs(num) <= num2;
					if (flag5)
					{
						this.scrollRect.verticalNormalizedPosition = this.normalizePosition;
						this.normalizePosition = -1f;
					}
					else
					{
						this.scrollRect.verticalNormalizedPosition += Mathf.Sign(num) * num2;
					}
				}
				bool flag6 = this.tween >= 0f;
				if (flag6)
				{
					bool flag7 = this.tweenFrom < 0f && Time.frameCount > this.tweenFrame;
					if (flag7)
					{
						bool isOn = base.isOn;
						if (isOn)
						{
							this.tweenFrom = 0f;
							this.tweenTo = this.GetExpandedHeight();
							this.spacingFrom = 0f;
							this.spacingTo = this.layoutSpacing;
							this.tweenActive = true;
						}
						else
						{
							this.tweenFrom = this.GetExpandedHeight();
							this.tweenTo = 0f;
							this.spacingFrom = this.layoutSpacing;
							this.spacingTo = 0f;
							this.tweenActive = false;
						}
					}
					bool flag8 = this.tweenFrom >= 0f;
					if (flag8)
					{
						float num3 = this.tween / this.accordion.TransitionDuration;
						float num4 = Mathf.Lerp(this.tweenTo, this.tweenFrom, num3);
						this.SetHeight(num4);
						float num5 = Mathf.Lerp(this.spacingTo, this.spacingFrom, num3);
						this.contentLayout.spacing = num5;
						this.tween -= Time.deltaTime;
						bool flag9 = this.tween <= 0f;
						if (flag9)
						{
							this.SetHeight(this.tweenTo);
							this.content.gameObject.SetActive(this.tweenActive);
							this.tween = -1f;
						}
						bool flag10 = this.tweenActive && this.scrollRect != null;
						if (flag10)
						{
							this.AdjustScroll(this.scrollRect, this.scrollTransform, this.rectTransform);
						}
					}
				}
			}
		}

		private void AdjustScroll(ScrollRect scrollRect, RectTransform scrollTransform, RectTransform obj)
		{
			float num = 0f;
			float num2 = 0f;
			float num3 = 0f;
			RectTransform rectTransform = this.scrollRect.content;
			Transform transform = rectTransform.transform;
			HorizontalOrVerticalLayoutGroup component = rectTransform.GetComponent<HorizontalOrVerticalLayoutGroup>();
			for (int i = 0; i < transform.childCount; i++)
			{
				Transform child = transform.GetChild(i);
				bool flag = !child.gameObject.activeInHierarchy;
				if (!flag)
				{
					bool flag2 = child == base.transform;
					if (flag2)
					{
						num2 = num;
					}
					RectTransform component2 = child.GetComponent<RectTransform>();
					num += LayoutUtility.GetPreferredHeight(component2);
					bool flag3 = component != null && i != transform.childCount - 1;
					if (flag3)
					{
						num += component.spacing;
					}
					bool flag4 = child == this.content.transform;
					if (flag4)
					{
						num3 = num;
					}
				}
			}
			float height = scrollRect.viewport.rect.height;
			float num4 = num - height;
			bool flag5 = num4 > 0f;
			if (flag5)
			{
				float num5 = (1f - scrollRect.verticalNormalizedPosition) * num4;
				float num6 = num5 + height;
				bool flag6 = num2 < num5 || num3 > num6;
				if (flag6)
				{
					bool flag7 = num3 - num2 < height;
					if (flag7)
					{
						float num7 = num5 + num3 - num6;
						float num8 = 1f - num7 / num4;
						this.normalizePosition = Mathf.Clamp(num8, 0f, 1f);
					}
					else
					{
						float num9 = 1f - num2 / num4;
						this.normalizePosition = Mathf.Clamp(num9, 0f, 1f);
					}
				}
			}
		}

		[SerializeField]
		[Tooltip("The min height of this element.")]
		private LayoutElement content;

		private HorizontalOrVerticalLayoutGroup contentLayout;

		private Accordion accordion;

		private RectTransform rectTransform;

		private ScrollRect scrollRect;

		private RectTransform scrollTransform;

		private float tweenFrom;

		private float tweenTo;

		private float layoutSpacing;

		private float spacingFrom;

		private float spacingTo;

		private bool tweenActive;

		private float tween = -1f;

		private float normalizePosition = -1f;

		private int tweenFrame = 0;
	}
}
