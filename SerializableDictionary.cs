﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	[Serializable]
	public sealed class SerializableDictionary<TKey, TValue> : Dictionary<TKey, TValue>, ISerializationCallbackReceiver
	{
		public void OnBeforeSerialize()
		{
			this.data = new SerializableDictionary<TKey, TValue>.KeyValuePair[base.Count];
			int num = 0;
			foreach (KeyValuePair<TKey, TValue> keyValuePair in this)
			{
				this.data[num++] = new SerializableDictionary<TKey, TValue>.KeyValuePair(keyValuePair.Key, keyValuePair.Value);
			}
		}

		public void OnAfterDeserialize()
		{
			base.Clear();
			foreach (SerializableDictionary<TKey, TValue>.KeyValuePair keyValuePair in this.data)
			{
				base.Add(keyValuePair.Key, keyValuePair.Value);
			}
			this.data = null;
		}

		[SerializeField]
		private SerializableDictionary<TK<PERSON>, TValue>.KeyValuePair[] data;

		[Serializable]
		private struct KeyValuePair
		{
			public KeyValuePair(TKey k, TValue v)
			{
				this.Key = k;
				this.Value = v;
			}

			[SerializeField]
			public TKey Key;

			[SerializeField]
			public TValue Value;
		}
	}
}
