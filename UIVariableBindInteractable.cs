﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Bind/Variable Bind Interactable")]
	[RequireComponent(typeof(Selectable))]
	public sealed class UIVariableBindInteractable : UIVariableBindBool
	{
		protected override void OnValueChanged()
		{
			bool flag = this.selectable == null;
			if (flag)
			{
				this.selectable = base.GetComponent<Selectable>();
			}
			this.selectable.interactable = base.GetResult();
		}

		private Selectable selectable;
	}
}
