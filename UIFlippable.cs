﻿using System;
using UnityEngine;
using UnityEngine.UI;

namespace Nirvana
{
	[AddComponentMenu("Nirvana/UI/Effects/Flippable")]
	public sealed class UIFlippable : BaseMeshEffect
	{
		public bool Horizontal
		{
			get
			{
				return this.horizontal;
			}
			set
			{
				this.horizontal = value;
			}
		}

		public bool Vertical
		{
			get
			{
				return this.veritical;
			}
			set
			{
				this.veritical = value;
			}
		}

		public override void ModifyMesh(VertexHelper vertexHelper)
		{
			RectTransform rectTransform = base.transform as RectTransform;
			for (int i = 0; i < vertexHelper.currentVertCount; i++)
			{
				UIVertex uivertex = default(UIVertex);
				vertexHelper.PopulateUIVertex(ref uivertex, i);
				uivertex.position = new Vector3(this.horizontal ? (uivertex.position.x + (rectTransform.rect.center.x - uivertex.position.x) * 2f) : uivertex.position.x, this.veritical ? (uivertex.position.y + (rectTransform.rect.center.y - uivertex.position.y) * 2f) : uivertex.position.y, uivertex.position.z);
				vertexHelper.SetUIVertex(uivertex, i);
			}
		}

		[SerializeField]
		private bool horizontal = false;

		[SerializeField]
		private bool veritical = false;
	}
}
