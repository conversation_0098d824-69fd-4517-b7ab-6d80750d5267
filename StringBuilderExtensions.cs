﻿using System;
using System.Text;
using UnityEngine;
using UnityEngine.Assertions;

namespace Nirvana
{
	public static class StringBuilderExtensions
	{
		public static StringBuilder Concat(this StringBuilder builder, uint uintVal, uint padAmount, char padChar, uint baseVal)
		{
			Assert.IsTrue(padAmount >= 0U);
			Assert.IsTrue(baseVal > 0U && baseVal <= 16U);
			uint num = 0U;
			uint num2 = uintVal;
			do
			{
				num2 /= baseVal;
				num += 1U;
			}
			while (num2 > 0U);
			builder.Append(padChar, (int)Mathf.Max(padAmount, num));
			int num3 = builder.Length;
			while (num > 0U)
			{
				num3--;
				builder[num3] = StringBuilderExtensions.Digits[(int)(uintVal % baseVal)];
				uintVal /= baseVal;
				num -= 1U;
			}
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, uint uintVal)
		{
			builder.Concat(uintVal, 0U, StringBuilderExtensions.DefaultPadChar, 10U);
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, uint uintVal, uint padAmount)
		{
			builder.Concat(uintVal, padAmount, StringBuilderExtensions.DefaultPadChar, 10U);
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, uint uintVal, uint padAmount, char padChar)
		{
			builder.Concat(uintVal, padAmount, padChar, 10U);
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, int intVal, uint padAmount, char padChar, uint baseVal)
		{
			Assert.IsTrue(padAmount >= 0U);
			Assert.IsTrue(baseVal > 0U && baseVal <= 16U);
			bool flag = intVal < 0;
			if (flag)
			{
				builder.Append('-');
				uint num = (uint)(-1 - intVal + 1);
				builder.Concat(num, padAmount, padChar, baseVal);
			}
			else
			{
				builder.Concat((uint)intVal, padAmount, padChar, baseVal);
			}
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, int intVal)
		{
			builder.Concat(intVal, 0U, StringBuilderExtensions.DefaultPadChar, 10U);
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, int intVal, uint padAmount)
		{
			builder.Concat(intVal, padAmount, StringBuilderExtensions.DefaultPadChar, 10U);
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, int intVal, uint padAmount, char padChar)
		{
			builder.Concat(intVal, padAmount, padChar, 10U);
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, float floatVal, uint decimalPlaces, uint padAmount, char padChar)
		{
			Assert.IsTrue(padAmount >= 0U);
			bool flag = decimalPlaces == 0U;
			if (flag)
			{
				bool flag2 = floatVal >= 0f;
				int num;
				if (flag2)
				{
					num = (int)(floatVal + 0.5f);
				}
				else
				{
					num = (int)(floatVal - 0.5f);
				}
				builder.Concat(num, padAmount, padChar, 10U);
			}
			else
			{
				int num2 = (int)floatVal;
				builder.Concat(num2, padAmount, padChar, 10U);
				builder.Append('.');
				float num3 = Mathf.Abs(floatVal - (float)num2);
				do
				{
					num3 *= 10f;
					decimalPlaces -= 1U;
				}
				while (decimalPlaces > 0U);
				num3 += 0.5f;
				builder.Concat((uint)num3, 0U, '0', 10U);
			}
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, float floatVal)
		{
			builder.Concat(floatVal, StringBuilderExtensions.DefaultDecimalPlaces, 0U, StringBuilderExtensions.DefaultPadChar);
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, float floatVal, uint decimalPlaces)
		{
			builder.Concat(floatVal, decimalPlaces, 0U, StringBuilderExtensions.DefaultPadChar);
			return builder;
		}

		public static StringBuilder Concat(this StringBuilder builder, float floatVal, uint decimalPlaces, uint padAmount)
		{
			builder.Concat(floatVal, decimalPlaces, padAmount, StringBuilderExtensions.DefaultPadChar);
			return builder;
		}

		private static readonly char[] Digits = new char[]
		{
			'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
			'A', 'B', 'C', 'D', 'E', 'F'
		};

		private static readonly uint DefaultDecimalPlaces = 5U;

		private static readonly char DefaultPadChar = '0';
	}
}
