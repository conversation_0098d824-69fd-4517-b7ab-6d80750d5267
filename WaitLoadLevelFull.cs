﻿using System;
using System.IO;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Nirvana
{
	internal sealed class WaitLoadLevelFull : WaitLoadLevel
	{
		internal WaitLoadLevelFull(AssetBundleManager assetBundleManager, string assetbundleName, string levelName, LoadSceneMode loadMode)
		{
			this.assetBundleManager = assetBundleManager;
			this.assetBundleName = assetbundleName;
			this.levelName = Path.GetFileNameWithoutExtension(levelName);
			this.loadMode = loadMode;
		}

		internal WaitLoadLevelFull(string format, params object[] args)
		{
			base.Error = string.Format(format, args);
		}

		public override float Progress
		{
			get
			{
				return (this.asyncOpt != null) ? this.asyncOpt.progress : 0f;
			}
		}

		public override bool keepWaiting
		{
			get
			{
				bool flag = this.asyncOpt == null && base.Error != null;
				bool flag2;
				if (flag)
				{
					flag2 = false;
				}
				else
				{
					bool flag3 = this.asyncOpt != null && this.asyncOpt.isDone;
					flag2 = !flag3;
				}
				return flag2;
			}
		}

		internal override bool Update()
		{
			AssetBundleItem assetBundle = this.assetBundleManager.GetAssetBundle(this.assetBundleName);
			bool flag = assetBundle == null;
			bool flag2;
			if (flag)
			{
				flag2 = true;
			}
			else
			{
				bool flag3 = !string.IsNullOrEmpty(assetBundle.Error);
				if (flag3)
				{
					base.Error = assetBundle.Error;
					flag2 = false;
				}
				else
				{
					this.asyncOpt = SceneManager.LoadSceneAsync(this.levelName, this.loadMode);
					flag2 = false;
				}
			}
			return flag2;
		}

		private AssetBundleManager assetBundleManager;

		private string assetBundleName;

		private string levelName;

		private LoadSceneMode loadMode;

		private AsyncOperation asyncOpt;
	}
}
