﻿using System;
using UnityEngine;
using UnityEngine.Networking;

namespace Nirvana
{
	public sealed class WaitLoadFileInfo : CustomYieldInstruction
	{
		internal WaitLoadFileInfo(AssetBundleFileInfo fileInfo)
		{
			this.fileInfo = fileInfo;
		}

		internal WaitLoadFileInfo(AssetBundleFileInfo fileInfo, UnityWebRequest request, AsyncOperation async)
		{
			this.fileInfo = fileInfo;
			this.request = request;
			this.async = async;
		}

		public string Error { get; private set; }

		public AssetBundleFileInfo FileInfo
		{
			get
			{
				return this.fileInfo;
			}
		}

		public override bool keepWaiting
		{
			get
			{
				bool loaded = this.fileInfo.Loaded;
				bool flag;
				if (loaded)
				{
					flag = false;
				}
				else
				{
					bool flag2 = !this.async.isDone;
					if (flag2)
					{
						flag = true;
					}
					else
					{
						this.fileInfo.LoadComplete();
						bool isNetworkError = this.request.isNetworkError;
						if (isNetworkError)
						{
							this.Error = this.request.error;
							this.request.Dispose();
							this.request = null;
							flag = false;
						}
						else
						{
							bool flag3 = this.request.responseCode < 0L || this.request.responseCode >= 400L;
							if (flag3)
							{
								this.Error = string.Format("Http error code: {0}", this.request.responseCode);
								this.request.Dispose();
								this.request = null;
								flag = false;
							}
							else
							{
								string content = DownloadHandlerBuffer.GetContent(this.request);
								this.request.Dispose();
								this.request = null;
								bool flag4 = !this.fileInfo.Parse(content);
								if (flag4)
								{
									this.Error = "Parse file info file failed.";
								}
								flag = false;
							}
						}
					}
				}
				return flag;
			}
		}

		private AssetBundleFileInfo fileInfo;

		private UnityWebRequest request;

		private AsyncOperation async;
	}
}
