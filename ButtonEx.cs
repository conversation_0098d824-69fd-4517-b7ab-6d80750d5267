﻿using System;
using System.Diagnostics;
using UnityEngine.UI;

namespace Nirvana
{
	public sealed class ButtonEx : But<PERSON>
	{
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Action ToNormalEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Action ToHighlightedEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Action ToPressedEvent;

		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Action ToDisabledEvent;

		protected override void DoStateTransition(Selectable.SelectionState state, bool instant)
		{
			base.DoStateTransition(state, instant);
			switch (state)
			{
			case 0:
			{
				bool flag = this.ToNormalEvent != null;
				if (flag)
				{
					this.ToNormalEvent();
				}
				break;
			}
			case 1:
			{
				bool flag2 = this.ToHighlightedEvent != null;
				if (flag2)
				{
					this.ToHighlightedEvent();
				}
				break;
			}
			case 2:
			{
				bool flag3 = this.ToPressedEvent != null;
				if (flag3)
				{
					this.ToPressedEvent();
				}
				break;
			}
			case 4:
			{
				bool flag4 = this.ToDisabledEvent != null;
				if (flag4)
				{
					this.ToDisabledEvent();
				}
				break;
			}
			}
		}
	}
}
