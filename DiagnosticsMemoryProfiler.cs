﻿using System;
using System.Text;
using UnityEngine;
using UnityEngine.Profiling;

namespace Nirvana
{
	[Serializable]
	internal sealed class DiagnosticsMemoryProfiler : IDiagnosticsDocker, IDiagnosticsTab
	{
		public DiagnosticsDockAnchor DockAnchor
		{
			get
			{
				return this.dockAnchor;
			}
		}

		public bool DockDirty
		{
			get
			{
				return this.dockDitry;
			}
			set
			{
				this.dockDitry = value;
			}
		}

		public string TabName
		{
			get
			{
				return "Memory";
			}
		}

		public string GetDockText()
		{
			bool flag = !Profiler.supported;
			string text;
			if (flag)
			{
				this.textBuilder.Remove(0, this.textBuilder.Length);
				this.textBuilder.Append("<size=");
				this.textBuilder.Append(this.dockFontSize);
				this.textBuilder.Append("><color=FF4747FF><b>The unity profiler is disabled, can not show memory information.</b></color></size>");
				text = this.textBuilder.ToString();
			}
			else
			{
				this.textBuilder.Remove(0, this.textBuilder.Length);
				this.textBuilder.Append("<size=");
				this.textBuilder.Append(this.dockFontSize);
				this.textBuilder.Append('>');
				bool flag2 = this.dockTotalMemory;
				if (flag2)
				{
					this.textBuilder.Append("<color=47FF6EFF><b>Total Allocated: ");
					this.textBuilder.Append(Profiler.GetTotalAllocatedMemoryLong() / 1048576L);
					this.textBuilder.Append(" / ");
					this.textBuilder.Append(Profiler.GetTotalReservedMemoryLong() / 1048576L);
					this.textBuilder.Append("MB</b></color>\n");
				}
				bool flag3 = this.dockMonoMemory;
				if (flag3)
				{
					this.textBuilder.Append("<color=47FF6EFF><b>Mono: ");
					this.textBuilder.Append(Profiler.GetMonoUsedSizeLong() / 1048576L);
					this.textBuilder.Append(" / ");
					this.textBuilder.Append(Profiler.GetMonoHeapSizeLong() / 1048576L);
					this.textBuilder.Append("MB</b></color>\n");
				}
				this.textBuilder.Append("</size>");
				text = this.textBuilder.ToString();
			}
			return text;
		}

		public void DrawTab()
		{
			GUILayout.Label("Statistics: ", Array.Empty<GUILayoutOption>());
			GUILayout.BeginVertical(GUI.skin.textArea, Array.Empty<GUILayoutOption>());
			this.DrawStatistics();
			GUILayout.EndVertical();
			GUILayout.Label("Docker: ", Array.Empty<GUILayoutOption>());
			GUILayout.BeginVertical(GUI.skin.textArea, Array.Empty<GUILayoutOption>());
			this.DrawDock();
			GUILayout.EndVertical();
		}

		private void DrawStatistics()
		{
			bool supported = Profiler.supported;
			if (supported)
			{
				GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
				this.DrawMemoryIndicator("Mono Memory Usage", Profiler.GetMonoUsedSizeLong(), Profiler.GetMonoHeapSizeLong());
				this.DrawMemoryIndicator("Memory Usage", Profiler.GetTotalAllocatedMemoryLong(), Profiler.GetTotalReservedMemoryLong());
				GUILayout.EndHorizontal();
			}
			else
			{
				GUILayout.Label("<color=FF4747FF><b>The unity profiler is disabled, can not show memory information.</b></color>", Array.Empty<GUILayoutOption>());
			}
			this.warningMemoryPercent = DiagnosticsGUI.SliderLabel(this.warningMemoryPercent, "Memory Warning Percent: ", 0f, 1f);
			this.criticalMemoryPercent = DiagnosticsGUI.SliderLabel(this.criticalMemoryPercent, "Critical Warning Percent: ", 0f, 1f);
			GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
			bool flag = GUILayout.Button("Collect GC", DiagnosticsSkin.BenchButton, Array.Empty<GUILayoutOption>());
			if (flag)
			{
				GC.Collect();
			}
			bool flag2 = GUILayout.Button("Unload Unused Assets", DiagnosticsSkin.BenchButton, Array.Empty<GUILayoutOption>());
			if (flag2)
			{
				Resources.UnloadUnusedAssets();
			}
			GUILayout.EndHorizontal();
		}

		private void DrawDock()
		{
			DiagnosticsDockAnchor diagnosticsDockAnchor = DiagnosticsGUI.Anchor(this.dockAnchor, "Memory Anchor: ");
			bool flag = this.dockAnchor != diagnosticsDockAnchor;
			if (flag)
			{
				this.dockDitry = true;
				this.dockAnchor = diagnosticsDockAnchor;
			}
			int num = (int)DiagnosticsGUI.SliderLabel((float)this.dockFontSize, "Dock Font Size", 5f, 25f);
			bool flag2 = this.dockFontSize != num;
			if (flag2)
			{
				this.dockDitry = true;
				this.dockFontSize = num;
			}
			bool flag3 = DiagnosticsGUI.Toggle(this.dockTotalMemory, "Dock Total Memory: ");
			bool flag4 = this.dockTotalMemory != flag3;
			if (flag4)
			{
				this.dockDitry = true;
				this.dockTotalMemory = flag3;
			}
			bool flag5 = DiagnosticsGUI.Toggle(this.dockMonoMemory, "Dock Mono Memory: ");
			bool flag6 = this.dockMonoMemory != flag5;
			if (flag6)
			{
				this.dockDitry = true;
				this.dockMonoMemory = flag5;
			}
		}

		private void DrawMemoryIndicator(string label, long usedSize, long totalSize)
		{
			GUILayout.BeginVertical(Array.Empty<GUILayoutOption>());
			GUILayout.Label(label, DiagnosticsSkin.MemoryTitleLabel, Array.Empty<GUILayoutOption>());
			float num = (float)usedSize / (float)totalSize;
			GUILayout.BeginHorizontal(new GUILayoutOption[] { GUILayout.Width(200f) });
			string text = string.Format("<color=#{0}>Total: <b>{1}</b>MB</color>", this.GetMemoryColor(num), totalSize / 1048576L);
			GUILayout.Label(text, DiagnosticsSkin.MemoryTotalLabel, Array.Empty<GUILayoutOption>());
			GUILayout.EndHorizontal();
			DiagnosticsGUI.Progress(200f * num, 200f, 15f);
			GUILayout.BeginHorizontal(new GUILayoutOption[] { GUILayout.Width(200f) });
			GUILayout.Space(200f * num);
			string text2 = string.Format("<color=#{0}>Used: <b>{1}</b>MB</color>", this.GetMemoryColor(num), usedSize / 1048576L);
			GUILayout.Label(text2, DiagnosticsSkin.MemoryUsedLabel, Array.Empty<GUILayoutOption>());
			GUILayout.EndHorizontal();
			GUILayout.EndVertical();
		}

		private string GetMemoryColor(float percent)
		{
			bool flag = percent < this.warningMemoryPercent;
			string text;
			if (flag)
			{
				text = "47FF6EFF";
			}
			else
			{
				bool flag2 = percent < this.criticalMemoryPercent;
				if (flag2)
				{
					text = "FFAD1CFF";
				}
				else
				{
					text = "FF4747FF";
				}
			}
			return text;
		}

		[Header("Memory Statistics Configuration")]
		[SerializeField]
		[Range(0f, 1f)]
		[Tooltip("If FPS will drop below this value, the fps will mark as warning.")]
		private float warningMemoryPercent = 0.6f;

		[SerializeField]
		[Range(0f, 1f)]
		[Tooltip("If FPS will be equal or less this value, the fps will mark as critical.")]
		private float criticalMemoryPercent = 0.85f;

		[Header("Dock Display Configuration")]
		[SerializeField]
		[Tooltip("The dock anchor.")]
		[EnumLabel]
		private DiagnosticsDockAnchor dockAnchor;

		[SerializeField]
		[Tooltip("The font size of simple panel.")]
		private int dockFontSize = 12;

		[SerializeField]
		[Tooltip("Whether dock the total memory usage to anchor.")]
		private bool dockTotalMemory = true;

		[SerializeField]
		[Tooltip("Whether dock the mono memory usage to anchor.")]
		private bool dockMonoMemory = true;

		private bool dockDitry = true;

		private StringBuilder textBuilder = new StringBuilder();
	}
}
